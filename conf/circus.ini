[circus]
check_delay=5
endpoint=tcp://127.0.0.1:5553
pubsub_endpoint=tcp://127.0.0.1:5556
statsd=true
pidfile=/home/<USER>/circus.pid

[watcher:gunicorn]
copy_env=True
copy_path=True
working_dir=/home/<USER>/ecs-deep-diagnose/target/ecs-deep-diagnose/src
use_sockets=True
cmd=/home/<USER>/ecs-deep-diagnose/target/ecs-deep-diagnose/.venv/bin/gunicorn -c gunicorn_conf.py deep_diagnose.api.app:app
stop_signal=SIGTERM
graceful_timeout=30
env.PYTHONPATH=/home/<USER>/ecs-deep-diagnose/target/ecs-deep-diagnose/src
stdout_stream.class=FileStream
stdout_stream.filename=/home/<USER>/ecs-deep-diagnose/logs/application.log
stdout_stream.max_bytes=104857600
stdout_stream.time_format = %Y-%m-%d %H:%M:%S
stdout_stream.backup_count=30
stderr_stream.class=FileStream
stderr_stream.filename=/home/<USER>/ecs-deep-diagnose/logs/error.log
stderr_stream.max_bytes=104857600
stderr_stream.backup_count=30
stderr_stream.time_format = %Y-%m-%d %H:%M:%S

[watcher:celery-flower]
copy_env=True
copy_path=True
working_dir=/home/<USER>/ecs-deep-diagnose/target/ecs-deep-diagnose/src
cmd=/home/<USER>/ecs-deep-diagnose/target/ecs-deep-diagnose/.venv/bin/python -m celery -A deep_diagnose.services.task.celery_base.celery flower --address=0.0.0.0 --port=5555 --loglevel=debug
use_sockets=False
stop_signal=SIGTERM
env.PYTHONPATH=/home/<USER>/ecs-deep-diagnose/target/ecs-deep-diagnose/src
stdout_stream.class=FileStream
stdout_stream.filename=/home/<USER>/ecs-deep-diagnose/logs/celery-flower.log
stdout_stream.max_bytes=10485760
stdout_stream.time_format = %Y-%m-%d %H:%M:%S
stdout_stream.backup_count=10
stderr_stream.class=FileStream
stderr_stream.filename=/home/<USER>/ecs-deep-diagnose/logs/celery-flower-error.log
stderr_stream.max_bytes=10485760
stderr_stream.backup_count=10
stderr_stream.time_format = %Y-%m-%d %H:%M:%S

[watcher:celery-worker]
copy_env=True
copy_path=True
working_dir=/home/<USER>/ecs-deep-diagnose/target/ecs-deep-diagnose/src
cmd=/home/<USER>/ecs-deep-diagnose/target/ecs-deep-diagnose/.venv/bin/python -m celery -A deep_diagnose.services.task.celery_base.celery worker  -Q {{app_queue}}  --loglevel=info
use_sockets=False
stop_signal=SIGTERM
env.PYTHONPATH=/home/<USER>/ecs-deep-diagnose/target/ecs-deep-diagnose/src
stdout_stream.class=FileStream
stdout_stream.filename=/home/<USER>/ecs-deep-diagnose/logs/celery-worker.log
stdout_stream.max_bytes=10485760
stdout_stream.time_format = %Y-%m-%d %H:%M:%S
stdout_stream.backup_count=10
stderr_stream.class=FileStream
stderr_stream.filename=/home/<USER>/ecs-deep-diagnose/logs/celery-worker-error.log
stderr_stream.max_bytes=10485760
stderr_stream.backup_count=10
stderr_stream.time_format = %Y-%m-%d %H:%M:%S

[watcher:celery-beat]
copy_env=True
copy_path=True
working_dir=/home/<USER>/ecs-deep-diagnose/target/ecs-deep-diagnose/src
cmd=/home/<USER>/ecs-deep-diagnose/target/ecs-deep-diagnose/.venv/bin/python -m celery -A deep_diagnose.services.task.celery_base.celery beat --loglevel=info
use_sockets=False
stop_signal=SIGTERM
env.PYTHONPATH=/home/<USER>/ecs-deep-diagnose/target/ecs-deep-diagnose/src
stdout_stream.class=FileStream
stdout_stream.filename=/home/<USER>/ecs-deep-diagnose/logs/celery-beat.log
stdout_stream.max_bytes=10485760
stdout_stream.time_format = %Y-%m-%d %H:%M:%S
stdout_stream.backup_count=10
stderr_stream.class=FileStream
stderr_stream.filename=/home/<USER>/ecs-deep-diagnose/logs/celery-beat-error.log
stderr_stream.max_bytes=10485760
stderr_stream.backup_count=10
stderr_stream.time_format = %Y-%m-%d %H:%M:%S