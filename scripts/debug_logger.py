"""
简单的调试日志记录器

直接输出 astream 的原始内容到文件，不做过度处理
"""

import logging
from datetime import datetime
from typing import Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class SimpleDebugLogger:
    """
    简单的调试记录器 - 直接输出原始内容
    """

    def __init__(self, request_id: str, debug_dir: str = "debug_logs"):
        """
        初始化调试记录器

        Args:
            request_id: 请求ID
            debug_dir: 调试日志目录
        """
        self.request_id = request_id
        self.event_counter = 0

        # 创建调试目录
        debug_path = Path(debug_dir)
        debug_path.mkdir(exist_ok=True)

        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"astream_debug_{request_id}_{timestamp}.txt"
        self.log_file_path = debug_path / filename

        # 写入文件头
        with open(self.log_file_path, 'w', encoding='utf-8') as f:
            f.write(f"AStream Debug Log\n")
            f.write(f"Request ID: {request_id}\n")
            f.write(f"Start Time: {datetime.now().isoformat()}\n")
            f.write("=" * 80 + "\n\n")

        logger.info(f"Created debug log: {self.log_file_path}")

    def log_astream_event(self, agent: Any, xxxx: Any, event_data: Any) -> None:
        """
        记录 astream 事件 - 直接输出原始内容
        """
        self.event_counter += 1

        try:
            with open(self.log_file_path, 'a', encoding='utf-8') as f:
                f.write(f"Event #{self.event_counter} - {datetime.now().isoformat()}\n")
                f.write("-" * 40 + "\n")
                f.write(f"Agent: {repr(agent)}\n")
                f.write(f"XXXX: {repr(xxxx)}\n")
                f.write(f"Event Data: {repr(event_data)}\n")
                f.write("\n")

        except Exception as e:
            logger.error(f"Failed to log event: {e}")

    def finalize_log(self) -> str:
        """
        完成日志记录
        """
        try:
            with open(self.log_file_path, 'a', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write(f"Total Events: {self.event_counter}\n")
                f.write(f"End Time: {datetime.now().isoformat()}\n")

            logger.info(f"Debug log completed: {self.log_file_path}")

        except Exception as e:
            logger.error(f"Failed to finalize log: {e}")

        return str(self.log_file_path)


def create_debug_logger(request_id: str, enabled: bool = True) -> Optional[SimpleDebugLogger]:
    """
    创建简单调试记录器
    """
    if not enabled:
        return None

    try:
        return SimpleDebugLogger(request_id)
    except Exception as e:
        logger.error(f"Failed to create debug logger: {e}")
        return None