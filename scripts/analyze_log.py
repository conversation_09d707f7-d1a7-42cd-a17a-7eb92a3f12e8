
import re
import sys

def analyze_log_final_corrected(log_file):
    """
    Analyzes a log file, ignoring 'updates' events and providing a detailed
    breakdown of message types for each agent pattern.

    Args:
        log_file (str): The path to the log file.
    """
    stats = {}
    ordered_patterns = []

    # Regex patterns
    agent_line_re = re.compile(r"Agent: (.*)")
    run_id_re = re.compile(r"id=['\"](run-[-a-f0-9]+)['\"]")
    call_id_re = re.compile(r"'id':\s*'call_([\\w]+)'")

    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except FileNotFoundError:
        print(f"Error: File not found at {log_file}")
        return
    except Exception as e:
        print(f"An error occurred: {e}")
        return

    events = re.split(r'\nEvent #\d+', content)[1:]
    filtered_events_count = 0
    ignored_events_count = 0

    for event in events:
        if "XXXX: 'updates'" in event:
            ignored_events_count += 1
            continue

        agent_line_match = agent_line_re.search(event)
        if agent_line_match:
            agent_line = agent_line_match.group(0)
            if "Agent: ('" in agent_line:
                # Only process events that have a data line
                if "Event Data:" not in event:
                    continue

                filtered_events_count += 1
                key = agent_line_match.group(1)

                if key not in stats:
                    ordered_patterns.append(key)
                    stats[key] = {
                        "total_messages": 0,
                        "tool_messages": 0,
                        "ai_messages": 0,
                        "run_ids": set(),
                        "call_ids": set(),
                        "content_accumulated": [],
                        "reasoning_content_accumulated": []
                    }

                stats[key]["total_messages"] += 1
                if "Event Data: (ToolMessage" in event:
                    stats[key]["tool_messages"] += 1
                elif "Event Data: (AIMessageChunk" in event:
                    stats[key]['ai_messages'] += 1

                run_id_match = run_id_re.search(event)
                if run_id_match:
                    stats[key]["run_ids"].add(run_id_match.group(1))

                call_id_matches = call_id_re.findall(event)
                for call_id in call_id_matches:
                    if call_id:
                        stats[key]["call_ids"].add(call_id)

                content_match = re.search(r"content='([^']*)'", event)
                if content_match:
                    stats[key]["content_accumulated"].append(content_match.group(1))

                reasoning_content_match = re.search(r"reasoning_content='([^']*)'", event)
                if reasoning_content_match:
                    stats[key]["reasoning_content_accumulated"].append(reasoning_content_match.group(1))


    print("--- Log Analysis Results (Final Corrected) ---")
    print(f"Total Events Analyzed: {len(events)}")
    print(f"Events Ignored ('updates'): {ignored_events_count}")
    print(f"Events matching pattern \"Agent: ('...\"): {filtered_events_count}\n")

    for pattern in ordered_patterns:
        data = stats[pattern]
        print(f"Pattern: {pattern}")
        print(f"  - Total Messages: {data['total_messages']}")
        print(f"    - AIMessageChunk: {data['ai_messages']}")
        print(f"    - ToolMessage: {data['tool_messages']}")
        print(f"  - Distinct Run IDs: {len(data['run_ids'])}")
        print(f"  - Distinct Call IDs: {len(data['call_ids'])}")
        print(f"  - Content: {''.join(data['content_accumulated'])}")
        print(f"  - Reasoning Content: {''.join(data['reasoning_content_accumulated'])}")
        print("-" * 20)

if __name__ == "__main__":
    if len(sys.argv) > 1:
        log_file_path = sys.argv[1]
    else:
        log_file_path = 'langgraph_event.txt'

    analyze_log_final_corrected(log_file_path)
