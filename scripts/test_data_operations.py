#!/usr/bin/env python3
"""
数据访问测试脚本 - 改进版
演示如何使用新的领域结构进行数据操作，处理重复数据问题
"""
import sys
import asyncio
from pathlib import Path
from uuid import uuid4

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "src"))

from deep_diagnose.data.database import init_db, close_db
from deep_diagnose.domain.user.models import CloudbotAgentUser
from deep_diagnose.domain.user.repository import user_crud
from deep_diagnose.domain.user.schemas import UserCreate, UserUpdate
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_user_operations():
    """测试用户数据操作"""
    try:
        logger.info("=== 测试用户数据操作 ===")
        
        # 生成唯一的测试用户名
        test_username = f"test_user_{uuid4().hex[:8]}"
        
        # 1. 创建用户
        logger.info("1. 创建新用户")
        user_data = UserCreate(username=test_username, password="test_password")
        new_user = await user_crud.create(user_data)
        logger.info(f"✅ 创建用户成功: {new_user.username} (ID: {new_user.id})")
        
        # 2. 查询用户
        logger.info("2. 查询用户")
        found_user = await user_crud.get_by_username(test_username)
        if found_user:
            logger.info(f"✅ 查询用户成功: {found_user.username}")
        
        # 3. 更新用户密码
        logger.info("3. 更新用户密码")
        update_data = UserUpdate(password="new_test_password")
        updated_user = await user_crud.update(new_user.id, update_data)
        if updated_user:
            logger.info(f"✅ 更新密码成功")
        
        # 4. 验证新密码
        logger.info("4. 验证新密码")
        verified_user = await user_crud.verify_password(test_username, "new_test_password")
        if verified_user:
            logger.info(f"✅ 密码验证成功")
        
        # 5. 获取用户列表
        logger.info("5. 获取用户列表")
        users = await user_crud.get_multi(limit=5)
        logger.info(f"✅ 获取到 {len(users)} 个用户")
        for user in users:
            logger.info(f"  - {user.username} (ID: {user.id})")
        
        # 6. 统计用户数量
        logger.info("6. 统计用户数量")
        total_count = await user_crud.count()
        logger.info(f"✅ 总用户数: {total_count}")
        
        # 7. 删除测试用户
        logger.info("7. 删除测试用户")
        deleted = await user_crud.delete(new_user.id)
        if deleted:
            logger.info(f"✅ 删除用户成功")
        
        logger.info("=== 用户数据操作测试完成 ===")
        return True
        
    except Exception as e:
        logger.error(f"❌ 用户操作测试失败: {e}")
        return False


async def test_direct_model_operations():
    """测试直接模型操作"""
    try:
        logger.info("\n=== 测试直接模型操作 ===")
        
        # 生成唯一的测试用户名
        test_username = f"direct_test_{uuid4().hex[:8]}"
        
        # 1. 直接创建用户
        logger.info("1. 直接创建用户模型")
        direct_user = await CloudbotAgentUser.create(
            username=test_username,
            password="direct_test_password"
        )
        logger.info(f"✅ 直接创建用户成功: {direct_user.username} (ID: {direct_user.id})")
        
        # 2. 查询用户
        logger.info("2. 查询用户")
        found_user = await CloudbotAgentUser.get_or_none(username=test_username)
        if found_user:
            logger.info(f"✅ 查询用户成功: {found_user.username}")
        
        # 3. 更新用户
        logger.info("3. 更新用户信息")
        found_user.password = "updated_password"
        await found_user.save()
        logger.info("✅ 用户信息更新成功")
        
        # 4. 获取所有用户
        logger.info("4. 获取所有用户")
        all_users = await CloudbotAgentUser.all()
        logger.info(f"✅ 获取到 {len(all_users)} 个用户")
        for user in all_users[:3]:  # 只显示前3个用户
            logger.info(f"  - {user.username} (ID: {user.id})")
        if len(all_users) > 3:
            logger.info(f"  ... 还有 {len(all_users) - 3} 个用户")
        
        # 5. 删除测试用户
        logger.info("5. 删除测试用户")
        await direct_user.delete()
        logger.info("✅ 测试用户删除成功")
        
        logger.info("=== 直接模型操作测试完成 ===")
        return True
        
    except Exception as e:
        logger.error(f"❌ 直接模型操作测试失败: {e}")
        return False


async def test_existing_users():
    """测试现有用户操作"""
    try:
        logger.info("\n=== 测试现有用户操作 ===")
        
        # 1. 查询现有的admin用户
        logger.info("1. 查询现有admin用户")
        admin_user = await CloudbotAgentUser.get_or_none(username="admin")
        if admin_user:
            logger.info(f"✅ 找到admin用户: ID {admin_user.id}")
        else:
            logger.info("ℹ️ admin用户不存在")
        
        # 2. 查询所有现有用户
        logger.info("2. 查询所有现有用户")
        existing_users = await CloudbotAgentUser.all()
        logger.info(f"✅ 数据库中共有 {len(existing_users)} 个用户:")
        for user in existing_users:
            logger.info(f"  - {user.username} (ID: {user.id}, 创建时间: {user.gmt_create})")
        
        # 3. 测试密码验证（如果admin用户存在）
        if admin_user:
            logger.info("3. 测试admin用户密码验证")
            verified = await user_crud.verify_password("admin", "admin")
            if verified:
                logger.info("✅ admin用户密码验证成功")
            else:
                logger.info("ℹ️ admin用户密码验证失败（可能密码不是'admin'）")
        
        logger.info("=== 现有用户操作测试完成 ===")
        return True
        
    except Exception as e:
        logger.error(f"❌ 现有用户操作测试失败: {e}")
        return False


async def main():
    """主函数"""
    try:
        # 初始化数据库
        await init_db()
        logger.info("数据库连接初始化成功")
        
        # 运行所有测试
        tests = [
            ("现有用户操作", test_existing_users),
            ("用户CRUD操作", test_user_operations),
            ("直接模型操作", test_direct_model_operations),
        ]
        
        results = []
        for test_name, test_func in tests:
            logger.info(f"\n{'='*50}")
            logger.info(f"开始测试: {test_name}")
            logger.info(f"{'='*50}")
            
            result = await test_func()
            results.append((test_name, result))
            
            if result:
                logger.info(f"✅ {test_name} 测试通过")
            else:
                logger.error(f"❌ {test_name} 测试失败")
        
        # 总结测试结果
        logger.info(f"\n{'='*50}")
        logger.info("测试结果总结")
        logger.info(f"{'='*50}")
        
        passed = 0
        total = len(results)
        
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"{test_name}: {status}")
            if result:
                passed += 1
        
        logger.info(f"\n总计: {passed}/{total} 测试通过")
        
        if passed == total:
            logger.info("🎉 所有测试完成!")
        else:
            logger.error("💥 部分测试失败!")
            sys.exit(1)
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        sys.exit(1)
    finally:
        await close_db()


if __name__ == "__main__":
    asyncio.run(main())