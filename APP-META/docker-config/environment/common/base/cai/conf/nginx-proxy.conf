# proxy conf
#include                     user.conf;
user admin;
worker_rlimit_nofile        100000;

error_log                   "logs/nginx_error.log" warn;
pid                         /home/<USER>/cai/logs/tengine-proxy.pid;

events {
    use                     epoll;
    worker_connections      20480;
}


http {
    include                 /home/<USER>/cai/conf/mime.types;
    default_type            application/octet-stream;

    root                    htdocs;

    sendfile                on;
    tcp_nopush              on;

    server_tokens           off;

    keepalive_timeout       0;

    client_header_timeout   20m;
    send_timeout            20m;
    client_max_body_size    50m;
    client_body_temp_path   /home/<USER>/cai/data/client_body;

    index                   index.html index.htm;

    log_format              proxyformat    "$remote_addr $http_x_readtime [$time_local] \"$request_method http://$host$request_uri\" $status $body_bytes_sent \"$http_referer\" \"$upstream_addr\" \"$http_user_agent\" \"$cookie_unb\" \"$cookie_cookie2\" \"$http_eagleeye_traceid\"";

    access_log              "logs/nginx_access.log" proxyformat;
    log_not_found           off;

    gzip                    on;
    gzip_http_version       1.0;
    gzip_comp_level         6;
    gzip_min_length         1024;
    gzip_proxied            any;
    gzip_vary               on;
    gzip_disable            msie6;
    gzip_buffers            96 8k;
    gzip_types              text/xml text/plain text/css application/javascript application/x-javascript application/rss+xml application/json;

    proxy_set_header        Host $host;
    proxy_set_header        X-Real-IP $remote_addr;
    proxy_set_header        Web-Server-Type nginx;
    proxy_set_header        WL-Proxy-Client-IP $remote_addr;
    proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header        EagleEye-TraceId $http_eagleeye_traceid;
    proxy_redirect          off;
    proxy_buffers           128 8k;
    proxy_temp_path         data/proxy;
    proxy_intercept_errors  off;


    variables_hash_max_size     1024;
    variables_hash_bucket_size  64;

    server {
        listen              80;
        server_name         _;

        proxy_set_header  Host $host;
        proxy_set_header  X-Real-IP  $remote_addr;
        proxy_set_header  X-Forwarded-For $proxy_add_x_forwarded_for;
        set $app_scheme $scheme;
        if ($http_x_forwarded_proto != false) {
            set $app_scheme $http_x_forwarded_proto;
        }


        proxy_set_header X-Accel-Buffering no;
        proxy_buffering off;
        proxy_cache off;
        proxy_set_header X-Forwarded-Proto  $app_scheme;
        proxy_buffers 256 4k;
        proxy_buffer_size  128k;
        proxy_max_temp_file_size 0;
        proxy_connect_timeout 600;
        proxy_send_timeout 600; # 增加了这个指令，300
        proxy_ignore_client_abort on;
        underscores_in_headers on;

        # for daily-traceid: use user_ip in eagleeye traceid
        set $eaddr $remote_addr;
        if ($http_x_forwarded_for != "") {
               set $eaddr $http_x_forwarded_for;
        }
        if ($http_x_real_ip != "") {
               set $eaddr $http_x_real_ip;
        }



        location /api/v1 {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Allow' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With, X-CSRF-Token';
            add_header 'TCP_NODELAY' 'on';
            if ($request_uri ~ .*\.(txt|gif|jpg|jpeg|png|bmp|swf|js|css)$) {
                access_log off;
            }
            proxy_pass http://127.0.0.1:8000/api/v1;

            proxy_set_header Origin '';
            proxy_http_version 1.1;
            proxy_set_header Connection upgrade;
            proxy_read_timeout 86400;

            if ($request_method = 'OPTIONS') {
                return 200;
            }
            # if you want to enable cell logic, you must change your proxy_pass conf to following
            #
            # proxy_pass $ups;
        }
        location /api/v1/chat/sessions {

            proxy_pass http://127.0.0.1:8000/api/v1/chat/sessions;

        }


     

        location /public/buc/getUser {
            proxy_pass   http://127.0.0.1:8000/public/buc/getUser;
            # if you want to enable cell logic, you must change your proxy_pass conf to following
            #
            # proxy_pass $ups;
        }
         location /login {
            proxy_pass   http://127.0.0.1:8000/login;
            # if you want to enable cell logic, you must change your proxy_pass conf to following
            #
            # proxy_pass $ups;
        }
        location /sendBucSSOToken.do {
            proxy_pass   http://127.0.0.1:8000/sendBucSSOToken.do;
            # if you want to enable cell logic, you must change your proxy_pass conf to following
            #
            # proxy_pass $ups;
        }
        location  /api/current-user {
            proxy_pass   http://127.0.0.1:8000/api/current-user;
            # if you want to enable cell logic, you must change your proxy_pass conf to following
            #
            # proxy_pass $ups;
        }

        location /status.taobao {
            proxy_pass   http://127.0.0.1:8000/status.taobao;
        }
        location /docs {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Allow' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With, X-CSRF-Token';
            add_header 'TCP_NODELAY' 'on';
            if ($request_uri ~ .*\.(txt|gif|jpg|jpeg|png|bmp|swf|js|css)$) {
                access_log off;
            }
            proxy_set_header Origin '';
            proxy_http_version 1.1;
            proxy_set_header Connection upgrade;
            proxy_read_timeout 86400;

            if ($request_method = 'OPTIONS') {
                return 200;
            }
            proxy_pass   http://127.0.0.1:8000/docs;
        }
        location /api/token {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Allow' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With, X-CSRF-Token';
            add_header 'TCP_NODELAY' 'on';
            if ($request_uri ~ .*\.(txt|gif|jpg|jpeg|png|bmp|swf|js|css)$) {
                access_log off;
            }
            proxy_set_header Origin '';
            proxy_http_version 1.1;
            proxy_set_header Connection upgrade;
            proxy_read_timeout 86400;

            if ($request_method = 'OPTIONS') {
                return 200;
            }
            proxy_pass   http://127.0.0.1:8000/api/token;
        }

        location /api/v1/tasks {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Allow' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With, X-CSRF-Token';
            add_header 'TCP_NODELAY' 'on';
            if ($request_uri ~ .*\.(txt|gif|jpg|jpeg|png|bmp|swf|js|css)$) {
                access_log off;
            }
            proxy_set_header Origin '';
            proxy_http_version 1.1;
            proxy_set_header Connection upgrade;
            proxy_read_timeout 86400;

            if ($request_method = 'OPTIONS') {
                return 200;
            }
            proxy_pass   http://127.0.0.1:8000/api/v1/tasks;
        }

        location /api {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Allow' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, X-Requested-With, X-CSRF-Token';
            add_header 'TCP_NODELAY' 'on';
            if ($request_uri ~ .*\.(txt|gif|jpg|jpeg|png|bmp|swf|js|css)$) {
                access_log off;
            }
            proxy_set_header Origin '';
            proxy_http_version 1.1;
            proxy_set_header Connection upgrade;
            proxy_read_timeout 86400;

            if ($request_method = 'OPTIONS') {
                return 200;
            }
            proxy_pass   http://127.0.0.1:8000/api;
        }

             location /workers/ {

                proxy_pass http://127.0.0.1:5555/workers;

        }
        location /workers {

                proxy_pass http://127.0.0.1:5555/workers;

        }
        location /worker/ {

                proxy_pass http://127.0.0.1:5555/worker/;

        }
        location /task/ {

                proxy_pass http://127.0.0.1:5555/task/;

        }
        location /tasks/ {

                proxy_pass http://127.0.0.1:5555/tasks;

        }
        location /tasks/datatable {

                proxy_pass http://127.0.0.1:5555/tasks/datatable;

        }
        location /broker/ {

                    proxy_pass http://127.0.0.1:5555/broker;

        }
        location /static/ {

                    proxy_pass http://127.0.0.1:5555/static/;

        }


    }

    server {
        listen              80;
        server_name         status.taobao.com;


        location            = /nginx_status {
            stub_status     on;
        }
    }

    include /home/<USER>/cai/apps/*.conf;
}
