export CIRCUS_PID="${ADMIN_HOME}/application.pid"
export CIRCUS_LOG="${APP_HOME}/logs/application.log"
export PYTHONPATH=$PYTHONPATH:${APP_HOME}/target/${APP_NAME}
export PATH=$ADMIN_HOME/.local/bin:$PATH
export MAX_START_TIMEOUT_SECONDS=120
export MAX_START_VIPSERVER_TIMEOUT_SECONDS=15
export MAX_STOP_TIMEOUT_SECONDS=15
export MAX_STOP_VIPSERVER_TIMEOUT_SECONDS=30
EXPORT USE_GUNICORN="true"

# 检查antx.properties文件是否存在
if [ -f "$APP_HOME/target/antx.properties" ]; then
  # 读取a.properties文件中的数据并将其export到环境变量中
  while IFS='=' read -r key value
  do
    export "$key"="$value"
  done < "$APP_HOME/target/antx.properties"
fi