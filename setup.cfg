[flake8]
max-line-length = 88
extend-ignore = E203,E501,W503
exclude = .git,__pycache__,build,dist,migrations,.venv,venv,.pytest_cache

[mypy]
python_version = 3.12
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True
no_implicit_optional = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True
strict_equality = True
exclude = migrations/,tests/

[mypy-tests.*]
ignore_errors = True