import os
import multiprocessing

app_name = os.environ.get('APP_NAME')

# Number of worker_agent processes
workers = multiprocessing.cpu_count() * 2 + 1
threads = 4
# 监听队列
backlog = 512
# 超时时间
timeout = 300
# 设置守护进程,将进程交给 supervisor 管理
daemon = False
# Worker class to use
worker_class = "uvicorn.workers.UvicornWorker"
# 设置最大并发量
worker_connections = 2000
# Bind address and port
bind = "0.0.0.0:8000"
preload_app = True
autorestart = True
# Log file paths
accesslog = f"/home/<USER>/{app_name}/logs/access.log"
errorlog = f"/home/<USER>/{app_name}/logs/error.log"
loglevel = 'debug'