"""MCP相关的API路由处理模块

本模块包含与MCP工具功能相关的API路由处理函数。
"""

import logging
from fastapi import APIRouter, Depends, HTTPException

from deep_diagnose.security.auth.user_context import get_current_user
from deep_diagnose.api.models.user import UserModel
from deep_diagnose.api.models.mcp import (
    ToolsResponse, RefreshResponse, DescriptionResponse
)
from deep_diagnose.services.mcp.mcp_service import MCPService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/mcp", tags=["mcp"])


@router.get("/tools/enabled", response_model=ToolsResponse)
async def get_mcp_enabled_tools(user: UserModel = Depends(get_current_user)) -> ToolsResponse:
    """获取所有MCP工具信息
    
    要求携带有效 JWT Token。
    
    Args:
        user: 当前用户信息

    Returns:
        ToolsResponse: 包含所有MCP工具信息的响应模型
    """
    try:
        logger.info(f"User {user.user_name or user.access_key} requesting MCP tools")
        
        mcp_service = MCPService()
        result = await mcp_service.get_enabled_tools()
        
        if not result.success:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to get MCP tools: {result.error or 'Unknown error'}"
            )
        
        return result
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_mcp_tools endpoint: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.get("/tools", response_model=ToolsResponse)
async def get_mcp_tools(user: UserModel = Depends(get_current_user)) -> ToolsResponse:
    """获取所有MCP工具信息（包含启用状态）
    
    要求携带有效 JWT Token。
    
    Args:
        user: 当前用户信息

    Returns:
        ToolsResponse: 包含所有MCP工具信息和启用状态的响应模型
    """
    try:
        logger.info(f"User {user.user_name or user.access_key} requesting all MCP tools")
        
        mcp_service = MCPService()
        result = await mcp_service.get_mcp_tools()
        
        if not result.success:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to get MCP tools: {result.error or 'Unknown error'}"
            )
        
        return result
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in get_mcp_tools endpoint: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@router.post("/tools/refresh", response_model=RefreshResponse)
async def refresh_mcp_tools(user: UserModel = Depends(get_current_user)) -> RefreshResponse:
    """刷新MCP工具缓存
    
    要求携带有效 JWT Token。
    
    Args:
        user: 当前用户信息

    Returns:
        RefreshResponse: 包含刷新结果的响应模型
    """
    try:
        logger.info(f"User {user.user_name or user.access_key} requesting MCP tools cache refresh")
        
        mcp_service = MCPService()
        result = await mcp_service.refresh_tools()
        
        if not result.success:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to refresh MCP tools: {result.error or 'Unknown error'}"
            )
        
        return result
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in refresh_mcp_tools endpoint: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )

