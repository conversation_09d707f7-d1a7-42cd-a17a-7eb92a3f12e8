"""
VM 性能 API V1 路由

提供 VM 性能指标查询接口。
"""

import logging
from typing import Optional, List
from fastapi import APIRouter, HTTPException, Query

from deep_diagnose.tools.utils.vm_performance import list_vm_performance_metrics

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1", tags=["Diagnostics V1"])


@router.get(
    "/vm/performance",
    summary="获取VM实例性能指标",
    description="""
    通过VM实例ID（或名称）获取指定的性能指标数据。

    **查询参数：**
    - `name`: 必需，VM实例的ID。
    - `metrics`: 可选，需要查询的性能指标列表，可多次指定 (e.g., `&metrics=cpu&metrics=memory`)。
    - `start_time`: 可选，查询开始时间，格式 "YYYY-MM-DD HH:MM:SS"。
    - `end_time`: 可选，查询结束时间，格式 "YYYY-MM-DD HH:MM:SS"。

    **功能说明：**
    1. 调用底层的 `list_vm_performance_metrics` 工具。
    2. 支持查询多个指标在指定时间范围内的趋势数据。
    3. 如果未提供时间，则使用工具的默认时间范围。

    **响应格式：**
    - 成功：返回包含性能指标数据的JSON对象。
    - 失败：返回JSON格式的错误信息。
    """,
)
async def get_vm_performance(
    name: str = Query(..., description="VM实例的ID"),
    metrics: Optional[List[str]] = Query(None, description="要查询的性能指标列表"),
    start_time: Optional[str] = Query(None, description="查询开始时间"),
    end_time: Optional[str] = Query(None, description="查询结束时间"),
):
    logger.info(f"获取VM性能指标请求 - instance_id: {name}, metrics: {metrics}")
    try:
        result = await list_vm_performance_metrics(
            instance_id=name,
            metrics=metrics,
            start_time=start_time,
            end_time=end_time
        )
        if result is None:
            raise HTTPException(status_code=404, detail="获取VM性能数据失败或无数据")
        return result
    except Exception as e:
        logger.error(f"获取VM性能指标失败 - instance_id: {name}, 错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")
