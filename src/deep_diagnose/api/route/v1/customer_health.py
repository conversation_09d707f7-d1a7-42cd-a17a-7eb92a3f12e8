"""
用户健康检查 API V1 路由

提供用户全量健康检查的接口。
"""

import logging
from typing import Optional
from fastapi import APIRouter, HTTPException, Query

from deep_diagnose.tools.utils.user_health_check import check_customer_health

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1", tags=["Diagnostics V1"])


@router.get(
    "/user/health",
    summary="获取用户全量健康检查",
    description="""
    对指定用户（ali_uid）的所有VM实例进行全面的健康检查。

    **查询参数：**
    - `ali_uid`: 必需，阿里云客户的唯一标识符。
    - `start_time`: 可选，查询开始时间，格式 "YYYY-MM-DD HH:MM:SS"。
    - `end_time`: 可选，查询结束时间，格式 "YYYY-MM-DD HH:MM:SS"。

    **功能说明：**
    1. 调用底层的 `check_customer_health` 工具。
    2. 对指定客户的所有VM进行健康诊断。
    3. 如果未提供时间，则使用工具的默认时间范围。

    **响应格式：**
    - 成功：返回健康检查结果的JSON对象。
    - 失败：返回JSON格式的错误信息。
    """,
)
async def get_user_health(
    ali_uid: str = Query(..., description="阿里云客户的唯一标识符"),
    start_time: Optional[str] = Query(None, description="查询开始时间"),
    end_time: Optional[str] = Query(None, description="查询结束时间"),
):
    logger.info(f"获取用户健康检查请求 - ali_uid: {ali_uid}")
    try:
        result = await check_customer_health(
            ali_uid=ali_uid,
            start_time=start_time,
            end_time=end_time
        )
        if result is None:
            raise HTTPException(status_code=404, detail="获取用户健康检查数据失败或无数据")
        return result
    except Exception as e:
        logger.error(f"获取用户健康检查失败 - ali_uid: {ali_uid}, 错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"服务器内部错误: {str(e)}")
