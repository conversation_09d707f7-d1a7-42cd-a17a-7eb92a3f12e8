from datetime import datetime
from typing import List, Optional
from pydantic import BaseModel, Field
from deep_diagnose.domain.chat.models import MessageType


class ChatRequest(BaseModel):
    """V1 聊天请求模型"""

    question: str = Field(..., description="用户问题", min_length=1, max_length=10000)
    agent: str = Field("ReasoningAgent", description="智能体类型")
    user_id: Optional[str] = Field(None, description="用户ID，如果为空则从认证信息获取")
    session_id: Optional[str] = Field(None, description="会话ID，如果为空则创建新会话")
    additional_info: Optional[dict] = Field(None, description="附加信息，用于传递额外的上下文数据")
    question_type: MessageType = Field(MessageType.HUMAN_QUERY, description="问题类型：input(用户输入)/auto_triggered(自动触发)/manual_triggered(手动触发)")


class ChatSessionItem(BaseModel):
    """聊天会话项模型"""

    session_id: str = Field(..., description="会话ID")
    title: str = Field(..., description="会话主题")
    gmt_create: datetime = Field(..., description="创建时间")


class ChatSessionResponse(BaseModel):
    """聊天会话响应模型"""

    total: int = Field(..., description="会话总数")
    sessions: List[ChatSessionItem] = Field(..., description="会话列表")


class ChatMessageItem(BaseModel):
    """聊天消息项模型"""

    message: str = Field(..., description="消息内容")
    agent: Optional[str] = Field(None, description="智能体类型")
    message_type: str = Field(..., description="消息类型：human_query/ai_response/auto_query")
    gmt_create: datetime = Field(..., description="创建时间")
    ext: Optional[dict] = Field(None, description="扩展信息，用于传递额外的上下文数据")


class ChatMessageResponse(BaseModel):
    """聊天消息响应模型"""

    total: int = Field(..., description="消息总数")
    messages: List[ChatMessageItem] = Field(..., description="消息列表")
