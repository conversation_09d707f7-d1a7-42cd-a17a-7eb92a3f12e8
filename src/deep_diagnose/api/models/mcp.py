from typing import Dict, List, Optional, Any, TypeVar, Generic
from pydantic import BaseModel, Field

# 通用类型变量
T = TypeVar('T')

# ============================================================================
# 通用响应模型 - 可复用的基础结构
# ============================================================================

class ApiResponse(BaseModel, Generic[T]):
    """通用API响应模型"""
    success: bool = Field(..., description="操作是否成功")
    data: Optional[T] = Field(None, description="响应数据")
    error: Optional[str] = Field(None, description="错误信息")


class ApiResponseWithMessage(BaseModel, Generic[T]):
    """带消息的API响应模型"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="操作结果消息")
    data: Optional[T] = Field(None, description="响应数据")
    error: Optional[str] = Field(None, description="错误信息")


# ============================================================================
# 核心数据模型 - 业务实体
# ============================================================================

class Tool(BaseModel):
    """工具信息"""
    name: str = Field(..., description="工具名称")
    description: str = Field(..., description="工具描述")
    args_schema: Optional[Dict[str, Any]] = Field(None, description="工具参数schema")
    enabled: Optional[bool] = Field(None, description="工具是否启用")
    args: Optional[str] = Field(None, description="工具参数说明")
    results: Optional[str] = Field(None, description="工具返回结果说明")


class Server(BaseModel):
    """服务器信息"""
    tools: List[Tool] = Field(..., description="工具列表")
    tool_count: int = Field(..., description="工具数量")


class ServerMetadata(BaseModel):
    """服务器元数据"""
    transport: str = Field(..., description="连接类型 (stdio/sse)")
    command: Optional[str] = Field(None, description="执行命令 (stdio类型)")
    args: Optional[List[str]] = Field(None, description="命令参数 (stdio类型)")
    url: Optional[str] = Field(None, description="服务器URL (sse类型)")
    env: Optional[Dict[str, str]] = Field(None, description="环境变量")
    timeout_seconds: Optional[int] = Field(None, description="超时时间(秒)")
    tools: List = Field(default_factory=list, description="可用工具列表")


# ============================================================================
# 业务数据模型 - 组合的业务数据
# ============================================================================

class ToolsData(BaseModel):
    """工具数据"""
    servers: Dict[str, Server] = Field(..., description="服务器信息映射")
    total_servers: int = Field(..., description="总服务器数量")
    total_tools: int = Field(..., description="总工具数量")


class RefreshData(BaseModel):
    """刷新数据"""
    total_servers: int = Field(..., description="总服务器数量")
    total_tools: int = Field(..., description="总工具数量")
    servers: Dict[str, int] = Field(..., description="各服务器工具数量映射")


class DescriptionData(BaseModel):
    """描述数据"""
    description: str = Field(..., description="工具描述内容")
    format: str = Field(default="markdown", description="描述格式")


# ============================================================================
# API响应类型别名 - 具体的API响应
# ============================================================================

# 工具相关响应
ToolsResponse = ApiResponse[ToolsData]
RefreshResponse = ApiResponseWithMessage[RefreshData]
DescriptionResponse = ApiResponse[DescriptionData]

# 服务器元数据响应
ServerMetadataRequest = ServerMetadata
ServerMetadataResponse = ServerMetadata
