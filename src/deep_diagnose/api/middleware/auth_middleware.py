"""
认证中间件模块 - 处理用户认证

本模块负责验证用户是否已登录，如果未登录则重定向到登录页面
"""

from urllib import parse as url_parse

from fastapi.requests import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import RedirectResponse

from deep_diagnose.security.auth.auth_utils import authenticate
from deep_diagnose.security.buc.buc_sso import BucAuthHandler


class AuthMiddleware(BaseHTTPMiddleware):
    """认证中间件，用于验证用户是否已登录"""

    async def dispatch(self, request: Request, call_next):
        """处理请求

        Args:
            request: 请求对象
            call_next: 下一个处理函数

        Returns:
            响应对象
        """
        authentication = await authenticate(request)
        if authentication is False:
            # 🔧 修复：获取正确的外部URL作为BACK_URL
            # 从请求头中获取原始的外部访问信息
            host = request.headers.get("host", "")
            scheme = request.headers.get("x-forwarded-proto", "https")
            path = request.url.path
            query = f"?{request.url.query}" if request.url.query else ""
            
            # 构建正确的外部URL
            back_url = f"{scheme}://{host}{path}{query}"
            
            return RedirectResponse(
                url=BucAuthHandler().authenticate_redirect_url(back_url)
            )
        # 如果用户已登录，继续处理请求
        response = await call_next(request)
        return response
