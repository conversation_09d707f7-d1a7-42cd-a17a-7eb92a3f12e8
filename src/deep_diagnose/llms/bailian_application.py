"""
Bailian应用LLM包装器

基于LangGraph的Bailian应用包装器，提供统一的LLM接口
支持流式输出和事件推送
"""

import logging
import asyncio
from typing import Any, AsyncIterator, Dict, List, Optional, Iterator

from langchain_core.callbacks import AsyncCallbackManagerForLLMRun, CallbackManagerForLLMRun
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, AIMessageChunk
from langchain_core.outputs import ChatResult, ChatGeneration, ChatGenerationChunk
from langchain_core.runnables import RunnableConfig
from pydantic import BaseModel, Field

from deep_diagnose.llms.app_bailian import app_bailian, BailianAppEnum

logger = logging.getLogger(__name__)


class BailianAppConfig(BaseModel):
    """Bailian应用配置"""
    app_enum: BailianAppEnum = Field(description="百炼应用枚举")
    biz_params: Optional[Dict[str, Any]] = Field(default=None, description="业务参数")
    incremental_output: bool = Field(default=True, description="是否启用增量输出")
    has_thoughts: bool = Field(default=True, description="是否包含思考过程")


class BailianApplication(BaseChatModel):
    """
    Bailian应用LLM包装器
    
    基于LangGraph的Bailian应用包装器，提供统一的LLM接口
    支持流式输出和事件推送
    """
    
    app_config: BailianAppConfig = Field(description="Bailian应用配置")
    
    def __init__(self, app_config: BailianAppConfig, **kwargs):
        """
        初始化Bailian应用LLM包装器
        
        Args:
            app_config: Bailian应用配置
            **kwargs: 其他参数
        """
        super().__init__(app_config=app_config, **kwargs)
    
    @property
    def _llm_type(self) -> str:
        """LLM类型标识"""
        return f"bailian_{self.app_config.app_enum.name}"
    
    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """
        同步生成方法
        
        Args:
            messages: 输入消息列表
            stop: 停止词列表
            run_manager: 回调管理器
            **kwargs: 其他参数
            
        Returns:
            ChatResult: 聊天结果
        """
        try:
            # 提取最后一条人类消息作为提示词
            prompt = self._extract_prompt_from_messages(messages)
            
            # 调用Bailian应用（同步版本）
            full_response = ""
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果已经在事件循环中，使用线程池执行
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(asyncio.run, self._async_call_bailian(prompt))
                    full_response = future.result()
            else:
                # 直接运行异步方法
                full_response = asyncio.run(self._async_call_bailian(prompt))
            
            # 创建AI消息
            ai_message = AIMessage(content=full_response)
            
            # 创建生成结果
            generation = ChatGeneration(message=ai_message)
            result = ChatResult(generations=[generation])
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to invoke Bailian application synchronously: {e}", exc_info=True)
            
            # 返回错误消息
            error_message = AIMessage(content=f"Bailian应用调用失败: {str(e)}")
            generation = ChatGeneration(message=error_message)
            return ChatResult(generations=[generation])
    
    async def _async_call_bailian(self, prompt: str) -> str:
        """异步调用Bailian应用的辅助方法"""
        full_response = ""
        async for chunk in app_bailian.app_stream_call(
            self.app_config.app_enum,
            prompt,
            biz_params=self.app_config.biz_params,
            incremental_output=self.app_config.incremental_output
        ):
            if isinstance(chunk, tuple) and len(chunk) >= 1:
                result_text = chunk[0]
                if result_text:
                    full_response += result_text
        return full_response
    
    async def _agenerate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[AsyncCallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> ChatResult:
        """
        异步生成方法
        
        Args:
            messages: 输入消息列表
            stop: 停止词列表
            run_manager: 异步回调管理器
            **kwargs: 其他参数
            
        Returns:
            ChatResult: 聊天结果
        """
        try:
            # 提取最后一条人类消息作为提示词
            prompt = self._extract_prompt_from_messages(messages)
            
            # 调用Bailian应用
            full_response = ""
            async for chunk in app_bailian.app_stream_call(
                self.app_config.app_enum,
                prompt,
                biz_params=self.app_config.biz_params,
                incremental_output=self.app_config.incremental_output
            ):
                if isinstance(chunk, tuple) and len(chunk) >= 1:
                    result_text = chunk[0]
                    if result_text:
                        full_response += result_text
            
            # 创建AI消息
            ai_message = AIMessage(content=full_response)
            
            # 创建生成结果
            generation = ChatGeneration(message=ai_message)
            result = ChatResult(generations=[generation])
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to invoke Bailian application asynchronously: {e}", exc_info=True)
            
            # 返回错误消息
            error_message = AIMessage(content=f"Bailian应用调用失败: {str(e)}")
            generation = ChatGeneration(message=error_message)
            return ChatResult(generations=[generation])
    
    async def _astream(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager: Optional[AsyncCallbackManagerForLLMRun] = None,
        **kwargs: Any,
    ) -> AsyncIterator[ChatGenerationChunk]:
        """
        异步流式生成方法
        
        Args:
            messages: 输入消息列表
            stop: 停止词列表
            run_manager: 异步回调管理器
            **kwargs: 其他参数
            
        Yields:
            ChatGenerationChunk: 聊天生成块
        """
        try:
            # 提取最后一条人类消息作为提示词
            prompt = self._extract_prompt_from_messages(messages)
            
            # 调用Bailian应用流式接口
            async for chunk in app_bailian.app_stream_call(
                self.app_config.app_enum,
                prompt,
                biz_params=self.app_config.biz_params,
                incremental_output=self.app_config.incremental_output
            ):
                if isinstance(chunk, tuple) and len(chunk) >= 1:
                    result_text = chunk[0]
                    if result_text:
                        # 创建AI消息块
                        ai_message_chunk = AIMessageChunk(content=result_text)
                        generation_chunk = ChatGenerationChunk(message=ai_message_chunk)
                        yield generation_chunk
                        
        except Exception as e:
            logger.error(f"Failed to invoke Bailian application in streaming mode: {e}", exc_info=True)
            
            # 返回错误消息块
            error_chunk = AIMessageChunk(content=f"Bailian应用调用失败: {str(e)}")
            generation_chunk = ChatGenerationChunk(message=error_chunk)
            yield generation_chunk
    
    def _convert_input(self, input_data: Any) -> Any:
        """
        转换输入数据为标准格式
        
        Args:
            input_data: 输入数据
            
        Returns:
            转换后的输入数据
        """
        from langchain_core.prompt_values import ChatPromptValue
        
        if isinstance(input_data, str):
            messages = [HumanMessage(content=input_data)]
            return ChatPromptValue(messages=messages)
        elif isinstance(input_data, list):
            return ChatPromptValue(messages=input_data)
        elif hasattr(input_data, 'to_messages'):
            return input_data
        else:
            raise ValueError(f"不支持的输入数据类型: {type(input_data)}")
    
    def _extract_prompt_from_messages(self, messages: List[BaseMessage]) -> str:
        """
        从消息列表中提取提示词
        
        Args:
            messages: 消息列表
            
        Returns:
            str: 提取的提示词
        """
        if not messages:
            return ""
        
        # 提取所有人类消息的内容
        prompts = []
        for message in messages:
            if isinstance(message, HumanMessage):
                content = message.content
                if isinstance(content, str):
                    prompts.append(content)
                elif isinstance(content, list):
                    # 处理多模态内容
                    text_parts = []
                    for part in content:
                        if isinstance(part, dict) and part.get("type") == "text":
                            text_parts.append(part.get("text", ""))
                    if text_parts:
                        prompts.append(" ".join(text_parts))
        
        # 合并所有提示词
        return "\n".join(prompts) if prompts else ""
    
    def invoke(self, input_data: Any, config: Optional[RunnableConfig] = None) -> Any:
        """
        同步调用方法
        
        Args:
            input_data: 输入数据
            config: 运行配置
            
        Returns:
            Any: 调用结果
        """
        if isinstance(input_data, str):
            messages = [HumanMessage(content=input_data)]
        elif isinstance(input_data, list):
            messages = input_data
        else:
            raise ValueError(f"不支持的输入数据类型: {type(input_data)}")
        
        result = self._generate(messages, stop=None, run_manager=None)
        return result.generations[0].message.content
    

    
    def stream(self, input_data: Any, config: Optional[RunnableConfig] = None) -> Iterator[Any]:
        """
        同步流式调用方法
        
        Args:
            input_data: 输入数据
            config: 运行配置
            
        Yields:
            Any: 流式结果
        """
        if isinstance(input_data, str):
            messages = [HumanMessage(content=input_data)]
        elif isinstance(input_data, list):
            messages = input_data
        else:
            raise ValueError(f"不支持的输入数据类型: {type(input_data)}")
        
        # 将异步流式调用转换为同步流式调用
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # 如果已经在事件循环中，使用线程池执行
            import concurrent.futures
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(
                    asyncio.run,
                    self._astream_to_list(messages, config)
                )
                chunks = future.result()
                for chunk in chunks:
                    yield chunk.content
        else:
            # 直接运行异步方法
            chunks = asyncio.run(self._astream_to_list(messages, config))
            for chunk in chunks:
                yield chunk.content
    

    
    async def _astream_to_list(self, messages: List[BaseMessage], config: Optional[RunnableConfig] = None) -> List[AIMessageChunk]:
        """
        将异步流式调用转换为列表
        
        Args:
            messages: 消息列表
            config: 运行配置
            
        Returns:
            List[AIMessageChunk]: 消息块列表
        """
        chunks = []
        async for chunk in self._astream(messages, stop=None, run_manager=None):
            chunks.append(chunk.message)
        return chunks


# 工厂函数，用于创建不同类型的Bailian应用LLM包装器
def create_bailian_llm(
    app_enum: BailianAppEnum,
    biz_params: Optional[Dict[str, Any]] = None,
    incremental_output: bool = True,
    has_thoughts: bool = True
) -> BailianApplication:
    """
    创建Bailian应用LLM包装器
    
    Args:
        app_enum: Bailian应用枚举
        biz_params: 业务参数
        incremental_output: 是否启用增量输出
        has_thoughts: 是否包含思考过程
        
    Returns:
        BailianApplication: Bailian应用LLM包装器实例
    """
    app_config = BailianAppConfig(
        app_enum=app_enum,
        biz_params=biz_params,
        incremental_output=incremental_output,
        has_thoughts=has_thoughts
    )
    
    return BailianApplication(app_config=app_config)


# 预定义的Bailian应用LLM实例
def get_overview_generator_llm() -> BailianApplication:
    """获取概览生成器LLM"""
    return create_bailian_llm(BailianAppEnum.overview_generator, has_thoughts=False)


def get_recommendation_generator_llm() -> BailianApplication:
    """获取推荐生成器LLM"""
    return create_bailian_llm(BailianAppEnum.recommendation_generator, has_thoughts=False)


def get_question_rewriter_llm() -> BailianApplication:
    """获取重写LLM"""
    return create_bailian_llm(BailianAppEnum.question_rewriter, has_thoughts=False)
