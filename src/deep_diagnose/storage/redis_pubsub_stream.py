from typing import Optional, AsyncGenerator

from deep_diagnose.storage.redis_client import RedisClient


class RedisPubSubStream:
    """A minimal async wrapper for aioredis Pub/Sub to unify subscribe/read/close.
    This class is infrastructure-only and contains no domain logic.
    """

    def __init__(self, redis_client: RedisClient) -> None:
        self._redis_client = redis_client
        self._pubsub = None
        self._channel: Optional[str] = None

    async def subscribe(self, channel: str) -> None:
        self._pubsub = await self._redis_client.get_pubsub()
        await self._pubsub.subscribe(channel)
        self._channel = channel

    async def aiter_messages(self, timeout: float) -> AsyncGenerator[Optional[str], None]:
        if self._pubsub is None:
            raise RuntimeError("PubSub not initialized. Call subscribe() first.")
        while True:
            msg = await self._pubsub.get_message(ignore_subscribe_messages=True, timeout=timeout)
            if msg is None:
                yield None  # allow caller to control pacing/timeout
                continue
            if isinstance(msg, dict) and msg.get("type") == "message":
                data = msg.get("data")
                if data is not None:
                    yield data

    async def close(self) -> None:
        try:
            if self._pubsub is not None:
                if self._channel is not None:
                    try:
                        await self._pubsub.unsubscribe(self._channel)
                    except Exception:
                        pass
                try:
                    await self._pubsub.close()
                except Exception:
                    pass
        finally:
            self._pubsub = None
            self._channel = None
