"""
Chat Agent Manager

处理Agent创建和配置
"""

import logging
from typing import Dict, Any, Optional

from langfuse.callback import Callback<PERSON><PERSON><PERSON>

from deep_diagnose.common.config import get_config
from deep_diagnose.core.agent.agent_factory import AgentFactory
from deep_diagnose.services.chat.managers.session_manager import SessionInfo

logger = logging.getLogger(__name__)


class AgentManager:
    """Agent管理器 - 处理Agent创建和配置"""

    @staticmethod
    def create_langfuse_handler(
        session_id: Optional[str] = None,
        user_id: Optional[str] = None,
        trace_name: Optional[str] = None
    ) -> CallbackHandler:
        """创建Langfuse处理器"""
        return CallbackHandler(
            public_key=get_config().observability.langfuse.public_key,
            secret_key=get_config().observability.langfuse.secret_key,
            host=get_config().observability.langfuse.endpoint,
            session_id=session_id,
            user_id=user_id,
            trace_name=trace_name
        )

    @staticmethod
    def create_agent(
        agent_type: str, 
        message_id: int,
        session_id: Optional[str] = None,
        user_id: Optional[str] = None,
        context = None
    ):
        """创建Agent实例"""
        langfuse_handler = AgentManager.create_langfuse_handler(
            session_id=session_id,
            user_id=user_id,
            trace_name=agent_type  # trace_name对应agent名字
        )
        config = {
            "langfuse_handler": langfuse_handler,
            "request_id": getattr(context, 'request_id', str(message_id))
        }
        agent = AgentFactory.create_agent(agent_type, config)
        logger.info(f"Created agent: {agent_type} with request_id: {config['request_id']}")
        return agent

    @staticmethod
    def prepare_agent_kwargs(context, session_info: SessionInfo) -> Dict[str, Any]:
        """准备Agent调用参数"""
        agent_kwargs = context.kwargs.copy()
        agent_kwargs["request_id"] = getattr(context, "request_id", str(session_info.message_id))
        agent_kwargs["user_id"] = context.user_id

        if context.agent == "InteractiveAgent":
            agent_kwargs["resource_ids"] = session_info.resource_ids
            agent_kwargs["resource_type"] = session_info.resource_type
            agent_kwargs["ext"] = session_info.ext
            context.question = f"资源ID: {(session_info.resource_ids)} 资源类型: {session_info.resource_type}\n\n" + context.question

        elif context.agent == "InspectAgent":
            agent_kwargs["resource_ids"] = session_info.resource_ids
            agent_kwargs["resource_type"] = session_info.resource_type
            agent_kwargs["ext"] = session_info.ext
            agent_kwargs["session_id"] = session_info.session_id

        return agent_kwargs