"""
Chat Service V1 请求处理器

包含快速请求处理器和长时间运行请求处理器
"""

import asyncio
import json
import logging
import time
from abc import ABC, abstractmethod
from datetime import datetime
from typing import AsyncGenerator, Optional, cast, Any, Dict, Tuple, Union

from deep_diagnose.core.events.base_event import ErrorEvent, StatusEvent, BaseAgentOutputEvent
from deep_diagnose.services.dingtalk.dingtalk_service import DingTalkService
from deep_diagnose.storage.redis_client import RedisClient
from .chat_event_bus import ChatEventBus
from .events.chat_event_envelope import parse_envelope
from .managers import SessionManager, AgentManager

logger = logging.getLogger(__name__)

# 配置常量
DEFAULT_POLL_INTERVAL = 0.1  # 100毫秒，降低轮询延迟
DEFAULT_MAX_WAIT_TIME = 3600  # 60分钟


class RequestProcessor(ABC):
    """请求处理器抽象基类"""

    def __init__(self, redis_client: RedisClient):
        self.redis_client = redis_client
        self._current_session_id: Optional[str] = None

    @abstractmethod
    async def process(self, context: Any) -> AsyncGenerator[BaseAgentOutputEvent, None]:
        """处理请求的抽象方法"""
        pass


class SynchronousRequestProcessor(RequestProcessor):
    """同步请求处理器 - 直接流式处理并返回结果"""

    def __init__(self, redis_client: RedisClient):
        super().__init__(redis_client)

    async def process(self, context: Any) -> AsyncGenerator[BaseAgentOutputEvent, None]:
        """处理同步请求（保持原有流程）"""
        session_info: Optional[Any] = None
        final_event: Optional[BaseAgentOutputEvent] = None
        final_result: str = ""
        cancelled: bool = False

        try:
            # 1. 设置会话
            session_info = await SessionManager.setup_session(context)
            self._current_session_id = session_info.session_id

            # 2. 创建Agent
            agent: Any = AgentManager.create_agent(context.agent, session_info.message_id, session_id=session_info.session_id, user_id=context.user_id)
            agent_kwargs: Dict[str, Any] = AgentManager.prepare_agent_kwargs(context, session_info)

            # 3. 调用Agent并流式透传事件
            async for event in agent.astream(context.question, session_info.messages, **agent_kwargs):
                final_event = event
                try:
                    final_result = event.to_sse_format()
                except Exception:
                    pass
                yield event

        except asyncio.CancelledError:
            # SSE 客户端断开或上游取消：转为后台继续执行，最终更新占位消息
            logger.info("Synchronous streaming cancelled by client; continuing in background.")
            try:
                if session_info:
                    asyncio.create_task(self._continue_sync_in_background(context, session_info))
            except Exception as bg_err:
                logger.warning(f"Failed to spawn background continuation: {bg_err}")
            cancelled = True
            raise
        except Exception as e:
            error_msg = f"同步请求执行失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            yield ErrorEvent(error=error_msg)

        finally:
            if not cancelled:
                try:
                    if session_info and final_event:
                        await asyncio.shield(
                            SessionManager.persist_ai_response(
                                session_info.session_id,
                                context.request_id,
                                final_event.to_persist_format(),
                                context.agent,
                            )
                        )
                except asyncio.CancelledError:
                    pass
                except Exception as e:
                    logger.error(f"Failed to persist AI response in finally: {e}")

    async def _continue_sync_in_background(self, context: Any, session_info: Any) -> None:
        """在后台继续执行同步请求的推理流程，并在结束时调用 persist_ai_response。"""
        try:
            # 重新创建Agent执行（与前台相同参数）
            agent: Any = AgentManager.create_agent(context.agent, session_info.message_id, session_id=session_info.session_id, user_id=context.user_id)
            agent_kwargs: Dict[str, Any] = AgentManager.prepare_agent_kwargs(context, session_info)

            final_event: Optional[BaseAgentOutputEvent] = None
            async for event in agent.astream(context.question, session_info.messages, **agent_kwargs):
                final_event = event

            # 结束后持久化最终结果
            if final_event:
                await SessionManager.persist_ai_response(
                    session_info.session_id,
                    context.request_id,
                    final_event.to_persist_format(),
                    context.agent,
                )
        except Exception as e:
            # 后台失败则记录错误
            logger.error(f"Background continuation failed: {e}", exc_info=True)
            # 可选：在失败时也持久化一条失败记录
            try:
                await SessionManager.persist_ai_response(
                    session_info.session_id,
                    context.request_id,
                    {"message": f"处理失败: {str(e)}", "status": "failed", "ext": {}},
                    context.agent,
                )
            except Exception:
                pass


class AsynchronousRequestProcessor(RequestProcessor):
    """异步请求处理器 - 后台执行任务并通过Redis流式返回"""

    async def process(self, context: Any) -> AsyncGenerator[BaseAgentOutputEvent, None]:
        """处理异步请求，优先使用Redis Pub/Sub低延迟流式传输，失败时回退到轮询缓存。"""

        try:
            # 1. 设置会话
            session_info: Any = await SessionManager.setup_session(context)
            self._current_session_id = session_info.session_id

            # 2. 创建AI消息记录（状态为未完成）
            ai_message_id: int = await SessionManager.create_ai_message_placeholder(session_info.session_id, context.request_id, context.agent)

            # 3. 预创建PubSub并订阅频道，避免丢失早期事件
            channel: str = ChatEventBus.channel_name(context.request_id)
            pubsub: Optional[Any] = None
            event_bus: ChatEventBus = ChatEventBus(self.redis_client)
            try:
                stream: Any = await event_bus.subscribe(context.request_id)
                pubsub = stream
                logger.info(f"Subscribed to channel: {channel}")
            except Exception as sub_err:
                logger.warning(f"Pub/Sub subscribe failed, will fallback to polling. Error: {sub_err}")
                pubsub = None

            # 4. 启动异步后台处理
            logger.info(f"Starting new asynchronous task: {context.request_id}")
            asyncio.create_task(self._run_agent_in_background(context, session_info, ai_message_id))

            # 6. 优先通过Pub/Sub流式返回；失败则回退到Redis轮询
            if pubsub is not None:
                try:
                    async for event in self._stream_via_pubsub(context.request_id, pubsub):
                        yield event
                    return
                except Exception as stream_err:
                    logger.warning(f"Pub/Sub streaming failed, falling back to polling. Error: {stream_err}")
                finally:
                    try:
                        await pubsub.close()
                    except Exception:
                        pass

            # 7. 回退：从Redis缓存轮询
            async for event in self._stream_from_redis(context.request_id):
                yield event

        except Exception as e:
            error_msg = f"异步请求执行失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            yield ErrorEvent(error=error_msg)

    async def _run_agent_in_background(self, context: Any, session_info: Any, ai_message_id: int) -> None:
        """在后台异步运行Agent"""
        final_result: str = ""
        final_event: Optional[BaseAgentOutputEvent] = None

        try:
            # 创建Agent
            agent: Any = AgentManager.create_agent(context.agent, session_info.message_id, session_id=session_info.session_id, user_id=context.user_id)
            agent_kwargs: Dict[str, Any] = AgentManager.prepare_agent_kwargs(context, session_info)

            # 事件总线（统一发布/快照）
            event_bus: ChatEventBus = ChatEventBus(self.redis_client)

            # 调用Agent并保存到Redis与Pub/Sub
            seq: int = 0
            channel: str = ChatEventBus.channel_name(context.request_id)
            async for event in agent.astream(context.question, session_info.messages, **agent_kwargs):
                final_result = event.to_sse_format()
                final_event = event  # 保存最后的事件对象
                seq += 1
                # 采用统一事件信封发布
                try:
                    await event_bus.publish(context.request_id, seq, event)
                except Exception as pub_err:
                    logger.debug(f"Publish failed: {pub_err}")
                # 缓存写入分离：降频+异步，不阻塞事件发布
                if seq % 5 == 0:
                    # 兼容旧有 Redis 轮询消费者，写入旧格式的数据结构
                    # 统一通过事件总线写快照
                    asyncio.create_task(event_bus.write_snapshot(context.request_id, seq, event))

            logger.info(f"Background task completed: {context.request_id}")
            # 任务结束，不额外发布“完成”事件，避免破坏 SSE 消费端语义
            # 仅将最后一次事件留存于缓存（上面已按节流写入过）
            pass

        except Exception as e:
            error_msg: str = f"后台Agent执行失败: {str(e)}"
            logger.error(error_msg, exc_info=True)
            # 保存错误信息到Redis与频道
            error_event: str = json.dumps({"error": error_msg}, ensure_ascii=False)
            try:
                await self.redis_client.publish_async(channel, error_event)
            except Exception:
                pass
            await self._update_task_event(context.request_id, error_event)

        finally:
            # 发送钉钉消息卡片
            if final_result.strip() and final_event:
                await self._send_dingtalk_notification(context, session_info, final_event)

            # 更新AI消息内容并标记为完成
            if final_result.strip():
                await SessionManager.update_ai_message_content(ai_message_id, final_result)
            else:
                # 如果没有结果，也要标记为完成（避免永远处于未完成状态）
                await SessionManager.update_ai_message_content(ai_message_id, "处理完成，但无结果内容")

    async def _send_dingtalk_notification(self, context: Any, session_info: Any, final_event: BaseAgentOutputEvent) -> None:
        """发送钉钉通知消息

        Args:
            context: 请求上下文
            session_info: 会话信息
            final_event: 最终事件对象
        """
        try:
            # 从事件中获取第一个URL地址
            report_url: Optional[str] = None
            urls: Optional[Any] = getattr(final_event, "urls", None)
            if urls and isinstance(urls, list) and len(urls) > 0:
                first_url_info = urls[0]
                report_url = first_url_info.get("url", "") if isinstance(first_url_info, dict) else str(first_url_info)
                logger.info(f"从事件中获取到报告URL: {report_url}")

            # 只有当report_url存在且有效时才发送钉钉消息
            if report_url and report_url.strip():
                dingtalk_service: DingTalkService = DingTalkService()
                card_data: Dict[str, str] = {"question": context.question, "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"), "url": report_url}
                await dingtalk_service.send_private_card_message(context.user_id, card_data)
            else:
                logger.debug("未找到有效的报告URL，跳过钉钉消息发送")
        except Exception as e:
            logger.error(f"发送钉钉消息失败: {e}", exc_info=True)

    async def _stream_from_redis(self, request_id: str) -> AsyncGenerator[BaseAgentOutputEvent, None]:
        """从Redis流式获取任务数据（轮询缓存键）"""
        start_time: float = time.time()
        last_event_data: Optional[str] = None

        while time.time() - start_time < DEFAULT_MAX_WAIT_TIME:
            try:
                event_data: Optional[str] = await self._get_task_event(request_id)

                if not event_data:
                    await asyncio.sleep(DEFAULT_POLL_INTERVAL)
                    continue

                # 如果payload带有seq包装，解开并保留原始事件数据
                try:
                    seq: Optional[int]
                    event_data_unwrapped: Optional[str]
                    seq, event_data_unwrapped = parse_envelope(event_data)
                    if event_data_unwrapped is None:
                        event_data_unwrapped = event_data
                except Exception:
                    event_data_unwrapped = event_data

                # 只有数据变化时才返回新事件
                if event_data != last_event_data:
                    event: Optional[BaseAgentOutputEvent] = self._create_event_from_data(event_data_unwrapped)
                    if event:
                        yield event
                        last_event_data = event_data
                    else:
                        # 如果无法创建事件对象，创建一个基础状态事件
                        logger.warning("Failed to create event from data, creating basic status event")
                        yield StatusEvent(status="processing", message="Data received but format unknown")

                # 检查是否为错误或完成状态
                if self._is_final_event(event_data_unwrapped):
                    logger.info(f"Task {request_id} finished")
                    break

                await asyncio.sleep(DEFAULT_POLL_INTERVAL)

            except Exception as e:
                logger.error(f"Error streaming from Redis for task {request_id}: {e}")
                await asyncio.sleep(DEFAULT_POLL_INTERVAL)

        logger.info(f"Finished streaming from Redis for task {request_id}")

    async def _update_task_event(self, request_id: str, event_data: str) -> None:
        """更新任务事件快照（通过 ChatEventBus 封装，统一写入格式）。
        会尝试将 event_data 解析为 BaseAgentOutputEvent；若失败，降级为 StatusEvent。
        """
        from .chat_event_bus import ChatEventBus

        try:
            event: Optional[BaseAgentOutputEvent] = BaseAgentOutputEvent.from_sse_format(event_data)
            if event is None:
                try:
                    data: Dict[str, Any] = json.loads(event_data)
                    if isinstance(data, dict) and "error" in data:
                        event = ErrorEvent(error=str(data.get("error")))
                    else:
                        event = StatusEvent(status="processing", message="Data received but format unknown")
                except Exception:
                    event = StatusEvent(status="processing", message="Data received but format unknown")
            event_bus: ChatEventBus = ChatEventBus(self.redis_client)
            await event_bus.write_snapshot(request_id, 0, event)
            logger.debug(f"Updated task event snapshot for request {request_id}")
        except Exception as e:
            logger.error(f"Failed to update task event for {request_id}: {e}")

    async def _get_task_event(self, request_id: str) -> Optional[str]:
        """获取任务事件数据（通过 ChatEventBus 封装读取最新快照）。"""
        from .chat_event_bus import ChatEventBus

        try:
            event_bus: ChatEventBus = ChatEventBus(self.redis_client)
            return cast(Optional[str], await event_bus.read_snapshot(request_id))
        except Exception as e:
            logger.error(f"Failed to get task event for {request_id}: {e}")
            return None

    async def _stream_via_pubsub(self, request_id: str, pubsub: Any) -> AsyncGenerator[BaseAgentOutputEvent, None]:
        """通过Redis Pub/Sub低延迟流式传输事件，保证有序处理（使用seq）。"""
        start_time: float = time.time()
        expected_seq: int = 1
        buffer: Dict[int, str] = {}

        while time.time() - start_time < DEFAULT_MAX_WAIT_TIME:
            try:
                # 通过封装后的流接口获取原始数据字符串（可能返回 None 表示超时）
                async for data in pubsub.aiter_messages(DEFAULT_POLL_INTERVAL):
                    if not data:
                        break  # 超时，跳出到 while，继续下一轮计时检查

                    # 解析信封，提取 seq 和事件 JSON 字符串
                    seq: Optional[int]
                    payload: Optional[str]
                    seq, payload = parse_envelope(data)
                    if seq is None:
                        seq = expected_seq
                    if payload is None:
                        continue

                    # 有序处理：按expected_seq输出，乱序缓冲
                    buffer[seq] = payload
                    while expected_seq in buffer:
                        event_data_unwrapped: str = buffer.pop(expected_seq)
                        event: Optional[BaseAgentOutputEvent] = self._create_event_from_data(event_data_unwrapped)
                        if event:
                            yield event
                        else:
                            yield StatusEvent(status="processing", message="Data received but format unknown")
                        if self._is_final_event(event_data_unwrapped):
                            logger.info(f"Task {request_id} finished via pubsub")
                            return
                        expected_seq += 1

            except asyncio.CancelledError:
                raise
            except Exception as e:
                logger.warning(f"Error in pubsub streaming for {request_id}: {e}")
                await asyncio.sleep(DEFAULT_POLL_INTERVAL)
        logger.info(f"Finished streaming via pubsub for task {request_id}")

    def _create_event_from_data(self, event_data: str) -> Optional[BaseAgentOutputEvent]:
        """从事件数据创建事件对象（完全基于event_type动态创建）"""
        try:
            # 首先尝试解析外层数据结构
            outer_data: Dict[str, Any] = json.loads(event_data)

            # 检查是否有嵌套的data字段（常见的包装格式）
            actual_event_data: str = event_data
            if isinstance(outer_data, dict) and "data" in outer_data:
                # 如果data字段是字符串，使用它作为实际的事件数据
                if isinstance(outer_data["data"], str):
                    actual_event_data = outer_data["data"]
                # 如果data字段是字典，将其序列化为JSON字符串
                elif isinstance(outer_data["data"], dict):
                    actual_event_data = json.dumps(outer_data["data"], ensure_ascii=False)

            # 使用基类的多态反序列化方法（基于event_type自动构建对应类并反序列化）
            event: Optional[BaseAgentOutputEvent] = BaseAgentOutputEvent.from_sse_format(actual_event_data)
            if event:
                return event

            # 如果多态反序列化失败，说明可能是旧格式数据或损坏数据
            # 尝试基础解析作为降级处理
            try:
                data: Dict[str, Any] = json.loads(actual_event_data)

                # 如果有event_type但多态失败，说明是未注册的类型，返回None
                if "event_type" in data:
                    logger.warning(f"Unknown event_type: {data['event_type']}, cannot create event")
                    return None

                # 如果没有event_type，尝试基于内容推断（仅作为降级处理）
                if "error" in data:
                    return ErrorEvent(error=data["error"])
                elif "status" in data:
                    return StatusEvent(status=data["status"], message=data.get("message", ""))
                else:
                    # 其他情况返回None，让调用方处理
                    logger.warning(f"Cannot determine event type from data: {actual_event_data[:100]}...")
                    return None

            except json.JSONDecodeError:
                # 非JSON数据，返回None让调用方处理
                logger.warning(f"Invalid JSON data: {actual_event_data[:100]}...")
                return None

        except json.JSONDecodeError:
            # 外层JSON解析失败，尝试直接使用原始数据
            logger.debug(f"Outer JSON parse failed, trying direct parse: {event_data[:100]}...")
            try:
                event = BaseAgentOutputEvent.from_sse_format(event_data)
                if event:
                    return event
            except Exception:
                pass
            return None
        except Exception as e:
            logger.error(f"Failed to create event from data: {e}")
            return None

    def _is_final_event(self, event_data: str) -> bool:
        """判断是否为最终事件（使用事件对象的判断逻辑）"""
        try:
            # 尝试创建事件对象并使用其自身的判断逻辑
            event: Optional[BaseAgentOutputEvent] = self._create_event_from_data(event_data)
            if event:
                return event._is_final_event(event_data)

            # 如果无法创建事件对象，使用基础判断逻辑
            try:
                data: Dict[str, Any] = json.loads(event_data)
                # 基础判断：包含错误或明确的完成标识认为是最终事件
                status: str = str(data.get("status", "")).lower()
                return bool(data.get("error")) or status in ["completed", "failed", "finished", "done"]
            except json.JSONDecodeError:
                # 非JSON数据认为是最终事件
                return True
        except Exception as e:
            # 任何异常都认为是最终事件，避免无限循环
            logger.error(f"Exception in _is_final_event: {e}", exc_info=True)
            return True
