from celery.schedules import crontab

from deep_diagnose.common.config import get_config

broker_connection_retry_on_startup = True
CELERY_BROKER_URL = get_config().infrastructure.redis.broker_url
result_backend = get_config().infrastructure.redis.result_backend_url
result_serializer = 'json'  # 结果序列化方案
result_expires = 60 * 60 * 24  # 任务过期时间
timezone = 'Asia/Shanghai'  # 时区配置
imports = (  # 指定导入的任务模块,可以指定多个
    "deep_diagnose.services.task.celery_tasks",
    "deep_diagnose.tools.mcp.celery_tasks",  # 添加MCP任务模块
)

# Celery Beat定期任务配置
beat_schedule = {
    # MCP工具缓存刷新任务 - 每30分钟执行一次
    'refresh-mcp-cache': {
        'task': 'refresh_mcp_cache',
        'schedule': crontab(minute='*/5'),
    }
}
