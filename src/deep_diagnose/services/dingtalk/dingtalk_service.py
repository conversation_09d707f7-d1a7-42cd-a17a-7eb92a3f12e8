"""
钉钉服务模块

提供钉钉消息推送相关的业务逻辑
"""

import json
import logging
import uuid
from typing import Dict, Any, Optional
import asyncio

from alibabacloud_dingtalk.card_1_0 import models as dingtalkcard__1__0_models
from alibabacloud_dingtalk.card_1_0.client import Client as dingtalkcard_1_0Client
from alibabacloud_dingtalk.oauth2_1_0 import models as dingtalkoauth_2__1__0_models
from alibabacloud_dingtalk.oauth2_1_0.client import Client as dingtalkoauth2_1_0Client
from alibabacloud_dingtalk.robot_1_0 import models as dingtalkrobot__1__0_models
from alibabacloud_dingtalk.robot_1_0.client import Client as dingtalkrobot_1_0Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_tea_util import models as util_models

from deep_diagnose.common.config import get_config

logger = logging.getLogger(__name__)


class DingTalkService:
    """钉钉消息服务"""

    def __init__(self):
        """
        初始化钉钉服务
        """
        config = get_config()
        self.corp_id = config.dingtalk.corp_id
        self.corp_secret = config.dingtalk.corp_secret
        self.agent_id = config.dingtalk.agent_id
        self.card_template_id = config.dingtalk.card_template_id

    def _create_token_client(self) -> dingtalkoauth2_1_0Client:
        """
        创建获取访问令牌的客户端

        Returns:
            dingtalkoauth2_1_0Client: 认证客户端实例
        """
        config = open_api_models.Config()
        config.protocol = 'https'
        config.region_id = 'central'
        return dingtalkoauth2_1_0Client(config)

    def _create_message_client(self) -> dingtalkrobot_1_0Client:
        """
        创建发送消息的客户端

        Returns:
            dingtalkrobot_1_0Client: 消息发送客户端实例
        """
        config = open_api_models.Config()
        config.protocol = 'https'
        config.region_id = 'central'
        return dingtalkrobot_1_0Client(config)

    def _create_card_message_client(self) -> dingtalkcard_1_0Client:
        """
        创建发送Card消息的客户端

        Returns:
            dingtalkcard_1_0Client: Card消息发送客户端实例
        """
        config = open_api_models.Config()
        config.protocol = 'https'
        config.region_id = 'central'
        return dingtalkcard_1_0Client(config)

    async def get_access_token(self) -> Optional[str]:
        """
        获取钉钉访问令牌

        Returns:
            Optional[str]: 访问令牌，获取失败时返回None
        """
        try:
            client = self._create_token_client()
            request = dingtalkoauth_2__1__0_models.GetAccessTokenRequest(
                app_key=self.corp_id,
                app_secret=self.corp_secret
            )
            response = client.get_access_token(request)
            access_token = response.body.access_token
            logger.info("成功获取钉钉访问令牌")
            return access_token
        except Exception as err:
            logger.error(f"获取钉钉访问令牌失败: {err}")
            return None

    async def send_private_message(self, user_id: str, title: str, text: str) -> Dict[str, Any]:
        """
        发送钉钉私信消息

        Args:
            user_id (str): 目标用户ID
            title (str): 消息标题
            text (str): 消息内容

        Returns:
            Dict[str, Any]: 发送结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return {"success": False, "error": "无法获取访问令牌"}

            client = self._create_message_client()
            headers = dingtalkrobot__1__0_models.BatchSendOTOHeaders()
            headers.x_acs_dingtalk_access_token = access_token
            
            msg_param = json.dumps({
                "title": title,
                "text": text
            }, ensure_ascii=False)
            
            request = dingtalkrobot__1__0_models.BatchSendOTORequest(
                robot_code=self.corp_id,
                user_ids=[user_id],
                msg_key='sampleMarkdown',
                msg_param=msg_param
            )
            
            response = client.batch_send_otowith_options(
                request, 
                headers, 
                util_models.RuntimeOptions()
            )

            result = response.body.to_map() if response.body else {}
            logger.info(f"发送钉钉消息给用户 {user_id} 成功")
            return {"success": True, "data": result}
        except Exception as err:
            logger.error(f"发送钉钉消息失败: {err}")
            return {"success": False, "error": str(err)}

    async def send_private_card_message(self, user_id: str, card_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送钉钉私信卡片消息

        Args:
            user_id (str): 目标用户ID
            card_template_id (str): 卡片模板ID
            card_data (Dict[str, Any]): 卡片数据

        Returns:
            Dict[str, Any]: 发送结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return {"success": False, "error": "无法获取访问令牌"}

            client = self._create_card_message_client()
            headers = dingtalkcard__1__0_models.CreateAndDeliverHeaders()
            headers.x_acs_dingtalk_access_token = access_token
            
            # 构建卡片参数
            card_param_map = {}
            for key, value in card_data.items():
                card_param_map[key] = str(value) if not isinstance(value, str) else value
                
            card_data_obj = dingtalkcard__1__0_models.CreateAndDeliverRequestCardData(
                card_param_map=card_param_map
            )

            # 构建机器人投递模型
            im_robot_open_deliver_model = dingtalkcard__1__0_models.CreateAndDeliverRequestImRobotOpenDeliverModel(
                robot_code=self.corp_id
            )

            # 构建机器人空间模型
            im_robot_open_space_model = dingtalkcard__1__0_models.CreateAndDeliverRequestImRobotOpenSpaceModel(
                support_forward=True
            )

            # 生成唯一的 out_track_id
            out_track_id = f"card_{uuid.uuid4().hex}"
            
            # 构建请求
            request = dingtalkcard__1__0_models.CreateAndDeliverRequest(
                user_id=user_id,
                card_template_id=self.card_template_id,
                out_track_id=out_track_id,
                card_data=card_data_obj,
                im_robot_open_deliver_model=im_robot_open_deliver_model,
                im_robot_open_space_model=im_robot_open_space_model,
                open_space_id=f"dtv1.card//IM_ROBOT.{user_id}",
                user_id_type=1
            )
            
            response = client.create_and_deliver_with_options(
                request,
                headers,
                util_models.RuntimeOptions()
            )

            result = response.body.to_map() if response.body else {}
            logger.info(f"发送钉钉卡片消息给用户 {user_id} 成功")
            return {"success": True, "data": result}
        except Exception as err:
            logger.error(f"发送钉钉卡片消息失败: {err}")
            return {"success": False, "error": str(err)}


# 测试代码
async def _test():
    """测试函数"""
    service = DingTalkService()
    # 测试普通消息
    # result = await service.send_private_message("WB766120", "测试标题", "测试内容")
    # print(result)

    # 测试卡片消息
    card_data = {
        "question": "测试卡片",
        "time": "这是一个测试卡片",
        "url": "https://pre-cloudbot2.alibaba-inc.com/cloudbot/ng2/?pageVersion=0.0.001#/operations/diagnostic-aid?sessionId=76833e48-7746-4be3-bc57-fd43aef064d1"
    }
    result = await service.send_private_card_message("WB766120", card_data)
    print(result)


if __name__ == '__main__':
    asyncio.run(_test())