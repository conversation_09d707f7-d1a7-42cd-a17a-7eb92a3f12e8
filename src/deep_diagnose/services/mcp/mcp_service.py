"""
MCP服务

提供MCP工具相关的业务逻辑
"""

import logging
import re
from typing import Tuple, Optional

# Moved import inside the class to avoid circular imports
from deep_diagnose.api.models.mcp import (
    ToolsResponse, RefreshResponse, DescriptionResponse,
    ToolsData, RefreshData, DescriptionData,
    Server, Tool
)

logger = logging.getLogger(__name__)


class MCPService:
    """MCP工具服务"""
    
    def __init__(self):
        from deep_diagnose.tools.mcp.mcp_tool_cache import MCPCacheManager
        from deep_diagnose.tools.mcp.mcp_tool_client import MCPToolClient
        from deep_diagnose.tools.mcp.mcp_tool_config import MCPToolConfig
        self.cache_manager = MCPCacheManager()
        self.mcp_tool_client = MCPToolClient()
        self.mcp_tool_config = MCPToolConfig()
    
    def _parse_description(self, full_description: str) -> Tuple[str, Optional[str], Optional[str]]:
        """
        解析工具描述，拆分为主要描述、Args和Returns三部分
        
        Args:
            full_description (str): 完整的工具描述
            
        Returns:
            Tuple[str, Optional[str], Optional[str]]: (description, args, results)
        """
        if not full_description:
            return full_description, None, None
            
        # 使用正则表达式匹配Args和Returns部分
        # 匹配Args部分：\nArgs:\n或\n\nArgs:\n
        args_pattern = r'\n\s*Args?\s*:\s*\n(.*?)(?=\n\s*Returns?\s*:|\Z)'
        returns_pattern = r'\n\s*Returns?\s*:\s*\n(.*?)\Z'
        
        # 提取Args部分
        args_match = re.search(args_pattern, full_description, re.DOTALL | re.IGNORECASE)
        args_content = args_match.group(1).strip() if args_match else None
        
        # 提取Returns部分
        returns_match = re.search(returns_pattern, full_description, re.DOTALL | re.IGNORECASE)
        results_content = returns_match.group(1).strip() if returns_match else None
        
        # 提取主要描述（去除Args和Returns部分）
        description = full_description
        if args_match:
            description = description[:args_match.start()]
        elif returns_match:
            description = description[:returns_match.start()]
            
        description = description.strip()
        
        return description, args_content, results_content
    
    async def get_enabled_tools(self) -> ToolsResponse:
        """
        获取所有MCP工具信息
        
        Returns:
            ToolsResponse: 包含工具信息的响应模型
        """
        try:
            logger.info("Getting MCP tools from service")
            
            # 从缓存管理器获取工具
            tools_dict = await self.cache_manager.get_tools()
            
            if tools_dict:
                # 统计信息
                total_tools = 0
                servers_info = {}
                
                for server_name, tools in tools_dict.items():
                    # 防护检查：确保tools不为None
                    if tools is None:
                        logger.warning(f"Tools from server {server_name} is None, using empty list")
                        tools = []
                    
                    tool_list = []
                    for tool in tools:
                        # 拆解description
                        description, tool_args, tool_results = self._parse_description(tool.description)
                        
                        tool_info = Tool(
                            name=tool.name,
                            description=description,
                            args_schema=tool.args if hasattr(tool, 'args') else None,
                            args=tool_args,
                            results=tool_results
                        )
                        tool_list.append(tool_info)
                    
                    servers_info[server_name] = Server(
                        tools=tool_list,
                        tool_count=len(tools)
                    )
                    total_tools += len(tools)
                
                data = ToolsData(
                    servers=servers_info,
                    total_servers=len(tools_dict),
                    total_tools=total_tools
                )
                
                logger.info(f"Retrieved {total_tools} tools from {len(tools_dict)} servers")
                return ToolsResponse(success=True, data=data)
            else:
                logger.warning("No MCP tools found")
                data = ToolsData(servers={}, total_servers=0, total_tools=0)
                return ToolsResponse(success=True, data=data)
            
        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            logger.error(f"Error getting MCP tools: {e}\nTraceback: {error_traceback}")
            return ToolsResponse(success=False, error=str(e), data=None)
    
    async def get_mcp_tools(self) -> ToolsResponse:
        """
        获取所有MCP工具信息（包含启用状态）
        
        Returns:
            ToolsResponse: 包含所有工具信息和启用状态的响应模型
        """
        try:
            logger.info("Getting all MCP tools with enabled status from service")
            
            # 获取所有工具
            results = await self.mcp_tool_client.get_all_tools()
            
            # 获取启用工具映射
            enabled_tools_map = self.mcp_tool_config.get_enabled_tools_map()
            
            if results:
                # 统计信息
                total_tools = 0
                servers_info = {}
                
                for server_name, tools in results:
                    # 防护检查：确保tools不为None
                    if tools is None:
                        logger.warning(f"Tools from server {server_name} is None, using empty list")
                        tools = []
                    
                    tool_list = []
                    enabled_tools = enabled_tools_map.get(server_name, [])
                    
                    for tool in tools:
                        # 检查工具是否启用
                        is_enabled = tool.name in enabled_tools
                        
                        # 拆解description
                        description, tool_args, tool_results = self._parse_description(tool.description)
                        
                        tool_info = Tool(
                            name=tool.name,
                            description=description,
                            args_schema=tool.args if hasattr(tool, 'args') else None,
                            enabled=is_enabled,
                            args=tool_args,
                            results=tool_results
                        )
                        tool_list.append(tool_info)
                    
                    servers_info[server_name] = Server(
                        tools=tool_list,
                        tool_count=len(tools)
                    )
                    total_tools += len(tools)
                
                data = ToolsData(
                    servers=servers_info,
                    total_servers=len(results),
                    total_tools=total_tools
                )
                
                logger.info(f"Retrieved {total_tools} tools from {len(results)} servers with enabled status")
                return ToolsResponse(success=True, data=data)
            else:
                logger.warning("No MCP tools found")
                data = ToolsData(servers={}, total_servers=0, total_tools=0)
                return ToolsResponse(success=True, data=data)
            
        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            logger.error(f"Error getting all MCP tools: {e}\nTraceback: {error_traceback}")
            return ToolsResponse(success=False, error=str(e), data=None)
    
    async def refresh_tools(self) -> RefreshResponse:
        """
        刷新MCP工具缓存
        
        Returns:
            RefreshResponse: 刷新结果响应模型
        """
        try:
            logger.info("Refreshing MCP tools cache")
            
            # 刷新缓存
            tools_dict = await self.cache_manager.refresh_tools()
            
            # 统计刷新结果
            total_tools = sum(len(tools or []) for tools in tools_dict.values()) if tools_dict else 0
            total_servers = len(tools_dict) if tools_dict else 0
            
            message = f"Successfully refreshed {total_tools} tools from {total_servers} servers"
            data = RefreshData(
                total_servers=total_servers,
                total_tools=total_tools,
                servers={
                    server_name: len(tools or []) 
                    for server_name, tools in tools_dict.items()
                } if tools_dict else {}
            )
            
            logger.info(f"Cache refresh completed: {total_tools} tools from {total_servers} servers")
            return RefreshResponse(success=True, message=message, data=data)
            
        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            logger.error(f"Error refreshing MCP tools: {e}\nTraceback: {error_traceback}")
            return RefreshResponse(
                success=False,
                error=str(e),
                message="Failed to refresh MCP tools cache",
                data=None
            )
    
    async def get_tools_description(self) -> DescriptionResponse:
        """
        获取MCP工具的Markdown描述
        
        Returns:
            DescriptionResponse: 包含工具描述的响应模型
        """
        try:
            from deep_diagnose.tools.mcp.mcp_tool_manager import MCPToolManager
            
            logger.info("Getting MCP tools description")
            
            tool_manager = MCPToolManager()
            description = await tool_manager.get_enabled_mcp_tools_description()
            
            data = DescriptionData(
                description=description,
                format="markdown"
            )
            
            return DescriptionResponse(success=True, data=data)
            
        except Exception as e:
            import traceback
            error_traceback = traceback.format_exc()
            logger.error(f"Error getting MCP tools description: {e}\nTraceback: {error_traceback}")
            return DescriptionResponse(success=False, error=str(e), data=None)