"""
CRUD operations for database models - Tortoise ORM
"""

import logging
from typing import Optional, List, Dict, Any
from functools import wraps

from .models import CloudbotAgentUser
from .schemas import UserCreate, UserUpdate

logger = logging.getLogger(__name__)


def ensure_db_connection(func):
    """装饰器：确保数据库连接在方法执行前已初始化"""

    @wraps(func)
    async def wrapper(self, *args, **kwargs):
        await self._ensure_db_connection()
        return await func(self, *args, **kwargs)

    return wrapper


class UserCRUD:
    """用户CRUD操作 - Tortoise ORM"""

    def __init__(self):
        """初始化仓储"""
        self._db_manager = None

    @property
    def db_manager(self):
        """延迟导入数据库管理器，避免循环导入"""
        if self._db_manager is None:
            from deep_diagnose.data.database import db_manager

            self._db_manager = db_manager
        return self._db_manager

    async def _ensure_db_connection(self):
        """确保数据库连接已初始化"""
        from tortoise import connections

        try:
            # 检查默认连接是否存在且可用
            conn = connections.get("default")
            if conn is None or not self.db_manager.is_initialized:
                raise Exception("Connection not properly initialized")

            # 测试连接是否可用
            await conn.execute_query("SELECT 1")

        except Exception as e:
            # 如果连接不存在或不可用，重新初始化数据库
            logger.warning(f"Database connection issue detected: {e}, reinitializing...")

            try:
                # 如果已经初始化过，先关闭现有连接
                if self.db_manager.is_initialized:
                    await self.db_manager.close_database()

                # 重新初始化数据库连接
                await self.db_manager.init_database(generate_schemas=False)
                logger.info("Database connection reinitialized successfully")

            except Exception as init_error:
                logger.error(f"Failed to reinitialize database connection: {init_error}")
                raise

    @ensure_db_connection
    async def get(self, user_id: int) -> Optional[CloudbotAgentUser]:
        """根据ID获取用户"""
        return await CloudbotAgentUser.get_or_none(id=user_id)

    @ensure_db_connection
    async def get_by_username(self, username: str) -> Optional[CloudbotAgentUser]:
        """根据用户名获取用户"""
        return await CloudbotAgentUser.get_or_none(username=username)

    @ensure_db_connection
    async def get_multi(self, skip: int = 0, limit: int = 100) -> List[CloudbotAgentUser]:
        """获取多个用户"""
        return await CloudbotAgentUser.all().offset(skip).limit(limit)

    @ensure_db_connection
    async def create(self, user_data: UserCreate) -> CloudbotAgentUser:
        """创建用户（密码会被哈希）"""
        user = await CloudbotAgentUser.create(username=user_data.username, password=user_data.password)
        return user

    @ensure_db_connection
    async def update(self, user_id: int, user_data: UserUpdate) -> Optional[CloudbotAgentUser]:
        """更新用户"""
        user = await CloudbotAgentUser.get_or_none(id=user_id)
        if not user:
            return None

        update_data = user_data.dict(exclude_unset=True)
        if "password" in update_data:
            update_data["password"] = update_data["password"]

        await user.update_from_dict(update_data)
        await user.save()
        return user

    @ensure_db_connection
    async def delete(self, user_id: int) -> bool:
        """删除用户"""
        user = await CloudbotAgentUser.get_or_none(id=user_id)
        if user:
            await user.delete()
            return True
        return False

    @ensure_db_connection
    async def count(self) -> int:
        """获取用户总数"""
        return await CloudbotAgentUser.all().count()

    @ensure_db_connection
    async def verify_password(self, username: str, password: str) -> Optional[CloudbotAgentUser]:
        """验证用户密码"""
        user = await CloudbotAgentUser.get_or_none(username=username)
        if user and password == user.password:
            return user
        return None

    @ensure_db_connection
    async def update_password(self, user_id: int, new_password: str) -> Optional[CloudbotAgentUser]:
        """更新用户密码"""
        user = await CloudbotAgentUser.get_or_none(id=user_id)
        if user:
            user.password = new_password
            await user.save()
            return user
        return None


# 创建CRUD实例
user_crud = UserCRUD()
