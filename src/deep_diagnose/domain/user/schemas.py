"""
用户领域数据结构
"""
from typing import Optional
from datetime import datetime
from pydantic import BaseModel, Field


class UserBase(BaseModel):
    """用户基础模型"""
    username: str = Field(..., max_length=64, description="用户名/员工工号")


class UserCreate(UserBase):
    """创建用户模型"""
    password: str = Field(..., max_length=255, description="密码")


class UserUpdate(BaseModel):
    """更新用户模型"""
    username: Optional[str] = Field(None, max_length=64, description="用户名/员工工号")
    password: Optional[str] = Field(None, max_length=255, description="密码")


class User(UserBase):
    """用户响应模型"""
    id: int
    gmt_create: datetime
    gmt_modified: datetime
    
    class Config:
        from_attributes = True