"""
聊天领域模型定义
"""

from enum import Enum

from tortoise.models import Model
from tortoise import fields


class SessionTargetType(str, Enum):
    INSTANCE_ID = "instance_id"
    NC_ID = "nc_id"
    NC_IP = "nc_ip"
    ALI_UID = "ali_uid"


class MessageType(str, Enum):
    AI_RESPONSE = "ai_response"
    HUMAN_QUERY = "human_query"
    AUTO_QUERY = "auto_query"


class MessageStatus(str, Enum):
    SUCCESS = "success"
    FAILED = "failed"
    EXECUTING = "executing"


class MessageSource(str, Enum):
    CLOUDBOT_DIAGNOSIS = "cloudbot_diagnosis"
    CLOUDBOT_AGENT = "cloudbot_agent"
    DINGTALK = "dingtalk"


# Redis 键前缀常量
class RedisKeyPrefix:
    CHAT_EVENT = "chat_event"


class CloudbotAgentChatSession(Model):
    """机器人会话表"""

    id = fields.BigIntField(pk=True, description="自增主键 (内部使用)")
    session_id = fields.CharField(max_length=64, unique=True, db_index=True, description="会话业务ID (外部使用)")
    user_id = fields.CharField(max_length=16, description="员工工号")
    subject = fields.CharField(max_length=512, default="", description="会话主题")
    source = fields.CharField(
        max_length=32, default=MessageSource.CLOUDBOT_AGENT.value, description="消息初始来源: 关联MessageSource 枚举字段。第一次开启会话的消息来源"
    )
    resource_ids = fields.TextField(default="", description="诊断资源ID列表，逗号分隔")
    resource_types = fields.CharField(max_length=256, default="", description="诊断资源类型：VM/NC/UID，逗号分隔")
    ext = fields.JSONField(default={}, description="扩展信息")
    gmt_create = fields.DatetimeField(auto_now_add=True, description="创建时间")
    gmt_modified = fields.DatetimeField(auto_now=True, description="更新时间")

    # 反向关系
    messages: fields.ReverseRelation["CloudbotAgentChatMessage"]

    class Meta:
        table = "cloudbot_agent_chat_session"
        table_description = "机器人会话表"
        indexes = [("user_id",)]

    def __str__(self):
        return f"<CloudbotAgentChatSession(id={self.id}, session_id='{self.session_id}', user_id='{self.user_id}')>"


class CloudbotAgentChatMessage(Model):
    """机器人聊天记录表"""

    id = fields.BigIntField(pk=True, auto_increment=True, description="自增主键")
    # session_id = fields.ForeignKeyField("models.CloudbotAgentChatSession", to_field="session_id", related_name="messages", db_index=True)
    session_id = fields.CharField(max_length=64, db_index=True, description="会话业务ID (外部使用)")
    message = fields.TextField(description="聊天信息")
    message_type = fields.CharField(max_length=16, default=MessageType.AI_RESPONSE.value, description="消息类型")
    request_id = fields.CharField(max_length=64, description="请求ID")
    agent = fields.CharField(max_length=32, description="消息处理 Agent 名称")
    status = fields.CharField(max_length=16, default=MessageStatus.EXECUTING.value, description="状态: 关联MessageStatus 枚举字段")
    ext = fields.JSONField(default={}, description="扩展信息")
    gmt_create = fields.DatetimeField(auto_now_add=True, description="创建时间")
    gmt_modified = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "cloudbot_agent_chat_message"
        table_description = "机器人聊天记录表"
        indexes = [("status", "gmt_create")]

    def __str__(self):
        return f"<CloudbotAgentChatMessage(id={self.id}, session_id='{self.session_id}', type='{self.message_type}')>"
