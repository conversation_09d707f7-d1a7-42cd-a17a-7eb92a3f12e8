### 示例：性能指标采集与保存（纯数据处理，无可视化）

函数说明
- list_vm_performance_metrics(instance_id, metrics=None, start_time=None, end_time=None)
  - 用途：查询 VM 实例在时间范围内的性能指标
  - 参数要点：metrics 可为 None/list[str]/逗号分隔字符串；时间格式 YYYY-MM-DD HH:MM:SS
  - 返回：dict，键是指标名，值是时间序列列表（包含 timestamp/datetime/value）
  - 限制：默认最近 2 小时；最大 6 小时（超出会自动裁剪）

- save_and_register_data(request_id, instance_id, data, file_title=None, file_purpose=None)
  - 用途：将数据保存为 JSON 并登记到 file_resource.csv
  - 输出：vm_data_output/file_system/req_<request_id>/vm_performance_<instance_id>_<ts>.json
  - 返回：dict，包含 json_file、success 等关键信息

可执行示例代码

```python
import asyncio
import json
from datetime import datetime

# 主应用模块导入
from deep_diagnose.tools.utils.vm_performance import list_vm_performance_metrics
from deep_diagnose.tools.utils.file_resource_manager import save_and_register_data


async def main():
    print("🚀 开始执行性能数据分析...")

    # 1. 定义采集任务 (这个列表由外部传入，此处为示例)
    metrics = ['VmPpsBps/tx_pps', 'VmPpsBps/rx_pps', 'VmNetworkRetryMetric/tx_retry', 'VmNetworkRetryMetric/rx_retry', 'VmSessionDetail/session_count', 'VMLLCContenstionMetric/llc_hit_ratio', 'VMLLCMetric/llc', 'VmArpPingMetric/arp_drop_ratio', 'VmArpPingMetric/arp_timeout_ratio', 'VmStealMetric/vmsteal', 'VmVportDropMetric/drop_ratio', 'VmStealMetric/vcpucpu', 'VMCPUFreq/freq', 'VmStorageIOLatency/read_lat_ms', 'VmStorageIOLatency/write_lat_ms', 'VmMemBWMetric/memory_bw', 'VmStorageIOLatency/write_iops', 'VmStorageIOLatency/read_iops']
    metrics=None
    collect_data_tasks = [
        {
            "instance_id": "i-xxx", #实例ID从任务获取
            "start_time": "2025-08-30 21:00:00", #时间范围从任务获取
            "end_time": "2025-08-31 01:00:00",#时间范围从任务获取
            "metrics": metrics #metrics从任务获取
        }
    ]

    request_id = "req_a808036c-c3d8-463f-8a45-4aa69ff5cfc5"
    if not request_id or not request_id.strip():
        request_id = "default_request"

    print(f"📋 使用请求ID: {request_id}")

    # 2. 循环执行每个独立的采集任务
    for task in collect_data_tasks:
        instance_id = task["instance_id"]
        start_time = task["start_time"]
        end_time = task["end_time"]
        task_metrics = task["metrics"]

        try:
            print(f"📊 正在执行任务: 实例 {instance_id} 从 {start_time} 到 {end_time}")

            # 2a. 获取性能数据 (函数返回值为 dict)
            performance_data = await list_vm_performance_metrics(
                instance_id=instance_id,
                metrics=task_metrics,
                start_time=start_time,
                end_time=end_time
            )

            # 2b. 检查返回是否为空
            if not performance_data:
                print(f"❌ 未获取到数据: 实例 {instance_id}, 时间 {start_time}-{end_time}")
                continue

            save_result = save_and_register_data(request_id, instance_id, performance_data)

            if save_result.get("success"):
                print(f"✅ 任务结果已保存: {save_result.get('json_file')}")
            else:
                print(f"❌ 任务结果保存失败: {save_result.get('error')}")

        except Exception as e:
            print(f"❌ 执行任务时发生严重错误 (实例 {instance_id}): {e}")

    print("✅ 所有任务执行完成！")

# 执行主函数
asyncio.run(main())
```

注意事项
- 仅进行数据采集与保存，严禁添加可视化/GUI 相关代码
- list_vm_performance_metrics 的 metrics 参数支持 None/list[str]/逗号分隔字符串
- 时间范围建议控制在 6 小时以内，超出会被自动裁剪
