### 示例：客户健康检查与结果保存（纯数据处理，无可视化）

本例演示如何调用客户（aliuid）健康检查并将结果保存为 JSON 文件，同时登记文件资源，便于后续查询与报告生成。

函数说明（简版）
- check_customer_health(ali_uid, start_time=None, end_time=None)
  - 用途：对指定阿里云客户（ali_uid）进行健康检查，可选时间范围
  - 时间格式：支持 YYYYMMDD / YYYY-MM-DD / YYYY-MM-DD HH:MM:SS
  - 返回：诊断结果（通常为结构化列表/字典，具体由底层工具返回）

- save_and_register_data(request_id, instance_id, data, file_title=None, file_purpose=None)
  - 用途：将数据保存为 JSON，并登记到 file_resource.csv
  - 输出路径：vm_data_output/file_system/req_<request_id>/
  - 返回：包含 json_file、success 等关键信息

可执行示例代码

```python
import asyncio
import json

from deep_diagnose.tools.utils.user_health_check import check_customer_health
from deep_diagnose.tools.utils.file_resource_manager import save_and_register_data

async def main():
    try:
        # 1) 准备入参
        request_id = "{{ request_id }}".strip() or "default_request"
        ali_uid = "1781574661016173"  # 示例客户UID（请替换为实际值）
        # 可选时间范围：支持 YYYYMMDD / YYYY-MM-DD / YYYY-MM-DD HH:MM:SS
        start_time = "2025-09-01"
        end_time = "2025-09-02"

        print(f"开始客户健康检查: ali_uid={ali_uid}, range={start_time}~{end_time}")

        # 2) 执行健康检查
        result = await check_customer_health(ali_uid, start_time, end_time)
        if not result:
            print("未返回健康检查结果")
            return

        # 若返回为字符串，尽量解析为JSON结构
        if isinstance(result, str):
            try:
                result = json.loads(result)
            except Exception:
                pass

        # 3) 保存并登记结果
        # 使用 ali_uid 作为 instance_id 的一部分，便于区分来源
        instance_id = f"customer_{ali_uid}"
        save_res = save_and_register_data(
            request_id=request_id,
            instance_id=instance_id,
            data=result,
            file_title=f"客户健康检查_{ali_uid}",
            file_purpose="客户健康检查"
        )

        print(json.dumps(save_res, ensure_ascii=False))
    except Exception as e:
        print(f"执行失败: {e}")

asyncio.run(main())
```

注意事项
- 本示例仅做数据采集与保存，不引入可视化/GUI 相关代码
- 时间范围若未提供，将自动推导或默认最近2天；超过上限会自动裁剪
- 结果文件与 file_resource.csv 会写入 request_id 对应目录，便于后续检索
