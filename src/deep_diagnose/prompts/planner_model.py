

from enum import Enum
from typing import List, Optional

from pydantic import BaseModel, Field


class StepType(str, Enum):
    RESEARCH = "research"
    PROCESSING = "processing"


class StepStatus(str, Enum):
    NOT_START = "not_start"
    SUCCESS = "success"
    FAILED = "failed"
    SKIP = "skip"


class Step(BaseModel):
    title: str
    description: str = Field(..., description="Specify exactly what data to collect")
    step_type: StepType = Field(..., description="Indicates the nature of the step")
    step_order: int = Field(..., description="Step execution order, starting from 1")
    prerequisite_steps: List[int] = Field(
        default_factory=list, 
        description="List of step_order numbers that this step depends on"
    )
    dependency_context: str = Field(
        default="", 
        description="Explanation of how prerequisite step results will be used for replanning this step"
    )
    step_status: StepStatus = Field(
        default=StepStatus.NOT_START, 
        description="Current execution status of the step"
    )
    execution_res: Optional[str] = Field(
        default=None, description="The Step execution result"
    )
    observation: Optional[str] = Field(
        default=None, description="Observation content from step execution"
    )


class Plan(BaseModel):
    
    has_enough_context: bool
    thought: str
    title: str
    steps: List[Step] = Field(
        default_factory=list,
        description="Research & Processing steps to get more context",
    )

    class Config:
        json_schema_extra = {
            "examples": [
                {
                    "has_enough_context": False,
                    "thought": (
                        "To understand the current market trends in AI, we need to gather comprehensive information."
                    ),
                    "title": "AI Market Research Plan",
                    "steps": [
                        {
                            "title": "Current AI Market Analysis",
                            "description": (
                                "Collect data on market size, growth rates, major players, and investment trends in AI sector."
                            ),
                            "step_type": "research",
                            "step_order": 1,
                            "prerequisite_steps": [],
                            "dependency_context": "",
                            "step_status": "not_start"
                        }
                    ],
                }
            ]
        }
