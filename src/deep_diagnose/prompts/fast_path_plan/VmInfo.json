[{"id": 1, "tool": "getVmBasicInfo", "dependence": [], "input": "实例ID ${vm}", "analysis": "查询该VM的基本信息，获取其当前所在的NC IP以及回复所需的其它关键信息", "fastParams": {"instanceIds": ["${vm}"]}}, {"id": 2, "tool": "getNcBasicInfo", "dependence": [1], "input": "第一步查询得到的NC IP", "analysis": "查询该NC的基本信息，提取关键信息以回复客户问题", "fastParams": {"ncs": ["${output[0][0].get('ncIp','')}"]}}, {"id": 3, "tool": "getUserInfo", "dependence": [1], "input": "第一步查询得到的AliUid", "analysis": "查询该AliUid的基本信息，提取关键信息以回复客户问题", "fastParams": {"uid": "${output[0][0].get('aliUid','')}"}}]