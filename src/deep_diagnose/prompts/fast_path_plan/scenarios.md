### VmInfo
场景描述：查询单个VM当前的基本信息（规格、地域、可用区、状态等），包括VM所在宿主机和所属客户的信息。注意，
限制性条件：问题不能查询过去的信息，也不能包含对VM的运维记录、迁移记录、客户侧事件等运维相关记录的查询。

问题示例：
+ i-bp122j1unf42sq61awir的规格是什么？
+ i-bp122j1unf42sq61awir的CPU型号是什么？
+ i-bp122j1unf42sq61awir的TAM是谁？

关键参数：
+ vm: String类型，表示要查询的VM的ID，是以i-或eci-或acs-开头的一串字母和数字组成的标识。


### NcInfo
场景描述：查询单个宿主机当前的基本信息。
限制性条件：问题不能查询过去的信息。

问题示例：
+ *************的机型是什么？
+ *************上面有哪些VM？

关键参数：
+ nc: String类型，表示要查询的宿主机的IP地址，符合IPv4格式。

### UserInfo
场景描述：查询单个客户的基本信息。

问题示例：
+ 用户0123456789101112是谁对接？
+ 0123456789101112有多少实例？
+ 0123456789101112的GC等级是多少？

关键参数：
+ uid: String类型，表示要查询的客户的ID，是以5位或16位纯数字组成的标识。

### OpsEvent
场景描述：查询指定VM的客户侧事件记录。
限制性条件：不能包含对客户侧事件的操作，如事件延期等。

问题示例：
+ 实例i-adfaafdda昨天有发过什么事件
+ 查询实例i-aweffadsf的未闭合事件
+ 实例i-xxxxx有哪些执行中的客户侧事件

关键参数：
+ vm: String类型，表示要查询的VM的ID，是以i-或eci-或acs-开头的一串字母和数字组成的标识。
+ start_time: String类型，表示查询的开始时间，格式为yyyy-MM-dd HH:mm:ss。
+ end_time: String类型，表示查询的结束时间，格式为yyyy-MM-dd HH:mm:ss。

### Screenshot
场景描述：对单个实例进行截屏，利用截屏信息进行诊断。

问题示例：
+ 截屏诊断一下i-bp122j1unf42sq61awir
+ 对实例i-bp122j1unf42sq61awir进行截屏，并给出诊断结果

关键参数：
+ vm: String类型，表示要查询的VM的ID，是以i-或eci-或acs-开头的一串字母和数字组成的标识。

### StartStop
场景描述：单个实例遭遇了启动或停止失败，诊断其失败原因。

问题示例：
+ i-bp122j1unf42sq61awir实例启动失败
+ i-bp122j1unf42sq61awir实例一直停止不了
+ i-bp122j1unf42sq61awir实例重启失败，请给出失败原因

关键参数：
+ vm: String类型，表示要查询的VM的ID，是以i-或eci-或acs-开头的一串字母和数字组成的标识。
+ start_time: String类型，表示查询的开始时间，格式为yyyy-MM-dd HH:mm:ss。
+ end_time: String类型，表示查询的结束时间，格式为yyyy-MM-dd HH:mm:ss。

### ActionTrail
场景描述：查询单个实例在某段时间的资源操作记录。

问题示例：
+ 前两天客户操作过这个实例吗？
+ 上周实例i-caf213所有控制台操作记录
+ 实例i-af123cvzxc重启记录

关键参数：
+ vm: String类型，表示要查询的VM的ID，是以i-或eci-或acs-开头的一串字母和数字组成的标识。
+ start_time: String类型，表示查询的开始时间，格式为yyyy-MM-dd HH:mm:ss。
+ end_time: String类型，表示查询的结束时间，格式为yyyy-MM-dd HH:mm:ss。

### VmHostHistory
场景描述：查询单个实例在某段时间内部署在哪些宿主机上。
限制性条件：问题**不能**涉及对邻居实例的查询。

问题示例：
+ i-bp122j1unf42sq61awir昨天在哪台NC上
+ 今天9点i-bp122j1unf42sq61awir部署在哪里

关键参数：
+ vm: String类型，表示要查询的VM的ID，是以i-或eci-或acs-开头的一串字母和数字组成的标识。
+ start_time: String类型，表示查询的开始时间，格式为yyyy-MM-dd HH:mm:ss。
+ end_time: String类型，表示查询的结束时间，格式为yyyy-MM-dd HH:mm:ss。

### NcDeployment
场景描述：查询单台宿主机在某段时间内部署了哪些实例。

问题示例：
+ ************今天早上运行了哪些实例
+ ************部署了哪些实例

关键参数：
+ nc: String类型，表示要查询的宿主机的IP地址，符合IPv4格式。
+ start_time: String类型，表示查询的开始时间，格式为yyyy-MM-dd HH:mm:ss。
+ end_time: String类型，表示查询的结束时间，格式为yyyy-MM-dd HH:mm:ss。

### MigrationInfo
场景描述：查询单个实例的迁移记录，包括冷迁移和热迁移。
限制性条件：问题中必须包含关键词“迁移”，不能包含对部署情况的查询。

问题示例：
+ 迁移记录i-bp122j1unf42sq61awir
+ i-bp122j1unf42sq61awir实例今天做过冷迁移吗
+ 昨天对i-bp122j1unf42sq61awir实例有热迁移吗
+ 昨天对i-bp122j1unf42sq61awir实例的热迁移的Downtime是多少
+ i-bp122j1unf42sq61awir上午的热迁移使用了什么高级特性

关键参数：
+ vm: String类型，表示要查询的VM的ID，是以i-或eci-或acs-开头的一串字母和数字组成的标识。
+ start_time: String类型，表示查询的开始时间，格式为yyyy-MM-dd HH:mm:ss。
+ end_time: String类型，表示查询的结束时间，格式为yyyy-MM-dd HH:mm:ss。

### MigrateDryRun
场景描述：查询单个实例是否可以迁移（冷迁移或热迁移），以及有哪些候选目标宿主机。

问题示例：
+ i-bp122j1unf42sq61awir实例可以迁移吗
+ i-bp122j1unf42sq61awir实例的热迁移risk level是多少
+ i-bp122j1unf42sq61awir实例可以热迁移到哪些宿主机
+ 推荐一下实例i-adfaf123adf迁移目标

关键参数：
+ vm: String类型，表示要查询的VM的ID，是以i-或eci-或acs-开头的一串字母和数字组成的标识。

### Performance
场景描述：诊断单个实例运行过程中遭遇的性能问题（包括共享资源争抢、网络丢包、CPU异常上升等）。
限制性条件：问题中必须包含关键词“性能”或“争抢”。

问题示例：
+ i-8vb4227oa22v4aw7lasz是否存在性能争抢
+ 诊断下i-8vb4227oa22v4aw7lasz性能情况
+ 这个实例是否存在split lock争抢i-8vb4227oa22v4aw7lasz

关键参数：
+ vm: String类型，表示要查询的VM的ID，是以i-或eci-或acs-开头的一串字母和数字组成的标识。
+ start_time: String类型，表示查询的开始时间，格式为yyyy-MM-dd HH:mm:ss。
+ end_time: String类型，表示查询的结束时间，格式为yyyy-MM-dd HH:mm:ss。

### VmDown
场景描述：诊断单个实例的宕机原因（异常重启原因）。

问题示例：
+ 实例i-123sadfa3宕机原因
+ i-123sadf宕机重启是为什么

关键参数：
+ vm: String类型，表示要查询的VM的ID，是以i-或eci-或acs-开头的一串字母和数字组成的标识。
+ start_time: String类型，表示查询的开始时间，格式为yyyy-MM-dd HH:mm:ss。
+ end_time: String类型，表示查询的结束时间，格式为yyyy-MM-dd HH:mm:ss。

### NcDown
场景描述：诊断单台宿主机的宕机原因。

问题示例：
+ *************宕机了，请给出原因
+ 为什么*************会宕机

关键参数：
+ nc: String类型，表示要查询的宿主机的IP地址，符合IPv4格式。
+ start_time: String类型，表示查询的开始时间，格式为yyyy-MM-dd HH:mm:ss。
+ end_time: String类型，表示查询的结束时间，格式为yyyy-MM-dd HH:mm:ss。

### SubmitOps
场景描述：对单个实例或物理机进行运维操作，包括对虚拟机进行冷迁移或热迁移，对物理机进行锁定或恢复上线。

问题示例：
+ ************有cpu故障，需要锁定
+ 锁定************作为预留的资源
+ 恢复************上线
+ 下线物理机************
+ 热迁移i-8vb4227oa22v4aw7lasz
+ 冷迁移i-8vb4227oa22v4aw7lasz

关键参数：
+ machineId: String类型，表示要操作的机器ID。对VM实例，是以i-或eci-或acs-开头的一串字母和数字组成的标识；对物理机，是其IP地址，符合IPv4格式。
+ operation: String类型，表示要执行的操作。可选值有mlock（基于故障运维目的锁定物理机）、nc_reserve（基于资源预留目的锁定物理机）、offline（物理机轮转下线）、online（恢复物理机上线）、live_migrate（对VM进行热迁移）、cold_migrate（对VM进行冷迁移）。

### HealthStatus
场景描述：查询指定实例的健康状态或休眠记录。

问题示例：
+ 实例i-afadasdf健康状态
+ i-afadasdf健康状态变化
+ i-afadasdf近期是否休眠过

关键参数：
+ vm: String类型，表示要查询的VM的ID，是以i-或eci-或acs-开头的一串字母和数字组成的标识。

### Duty
场景描述：查询各团队或各组件的值班人员名单。

问题示例：
+ 今天虚拟化的值班是谁
+ NC值班

关键参数：无

### DiskInfo
场景描述：查询单个磁盘当前的基本信息。
限制性条件：问题不能查询过去的信息。

问题示例：
+ d-uf60xq8nw5mbel8i0v5r信息查询
+ 这个磁盘d-uf60xq8nw5mbel8i0v5r挂载在哪台机器

关键参数：
+ disk: String类型，表示要查询的磁盘的ID，是以d-开头的一串字母和数字组成的标识。

### OpsRecord
场景描述：查询单个机器（VM实例或宿主机）的运维操作记录。

问题示例：
+ ************这台机器昨天有过运维操作吗
+ 查看************运维记录
+ ************这台机器的下线运维执行成功了吗

关键参数：
+ machineId: String类型，表示要操作的机器ID。对VM实例，是以i-或eci-或acs-开头的一串字母和数字组成的标识；对物理机，是其IP地址，符合IPv4格式。
+ start_time: String类型，表示查询的开始时间，格式为yyyy-MM-dd HH:mm:ss。
+ end_time: String类型，表示查询的结束时间，格式为yyyy-MM-dd HH:mm:ss。


### OpsRule
场景描述：查询单个机器（VM实例或宿主机）的运维规则命中记录。

问题示例：
+ ************这台机器昨天命中了什么规则
+ 查看************运维规则命中记录

关键参数：
+ machineId: String类型，表示要操作的机器ID。对VM实例，是以i-或eci-或acs-开头的一串字母和数字组成的标识；对物理机，是其IP地址，符合IPv4格式。
+ start_time: String类型，表示查询的开始时间，格式为yyyy-MM-dd HH:mm:ss。
+ end_time: String类型，表示查询的结束时间，格式为yyyy-MM-dd HH:mm:ss。


### ChangeRecord
场景描述：查询单个机器（VM实例或宿主机）的高危变更。

问题示例：
+ 昨天*******有哪些发布
+ i-8vb4227oa22v4aw7lasz变更情况

关键参数：
+ machineId: String类型，表示要操作的机器ID。对VM实例，是以i-或eci-或acs-开头的一串字母和数字组成的标识；对物理机，是其IP地址，符合IPv4格式。
+ start_time: String类型，表示查询的开始时间，格式为yyyy-MM-dd HH:mm:ss。
+ end_time: String类型，表示查询的结束时间，格式为yyyy-MM-dd HH:mm:ss。

### KnowledgeQuery
场景描述：咨询与特定的资源无关的通用ECS知识，类似错误码的含义、概念解释、专有名词等。一般而言，问题中不包含资源ID。

问题示例：
+ 热迁移错误码60014的含义
+ 什么是FPGA
+ 如何执行指定目标机器的冷迁移

关键参数：无


### Invalid
场景描述：无意义的提问，包括问候语、与ECS无关的问题以及因逻辑缺陷、语义缺失或违反基本认知原则而无法产生有效信息交互的问题。注意，该场景也属于一个高频场景，需要返回场景名称。

问题示例：
+ 测试
+ 你好
+ 如何用声音发电？
+ 是否存在绝对真理？

关键参数：无