[{"id": 1, "tool": "listLiveMigrationRecords", "dependence": [], "input": "实例ID ${vm} 和时间范围 ${start_time} ~ ${end_time}", "analysis": "查询该VM在该时间段的热迁移记录。如果客户问题与此相关，则基于此回复客户问题。", "fastParams": {"instanceId": "${vm}", "startTime": "${start_time}", "endTime": "${end_time}"}}, {"id": 2, "tool": "query_live_migration_performance_metrics", "dependence": [], "input": "实例ID ${vm} 和时间范围 ${start_time} ~ ${end_time}", "analysis": "查询该VM在该时间段的热迁移的性能和耗时数据。如果客户问题与此相关，则基于此回复客户问题。", "fastParams": {"instance_id": "${vm}", "start_time": "${start_time}", "end_time": "${end_time}"}}, {"id": 3, "tool": "query_live_migration_advanced_features", "dependence": [], "input": "实例ID ${vm} 和时间范围 ${start_time} ~ ${end_time}", "analysis": "查询该VM在该时间段的热迁移使用的高级特性。如果客户问题与此相关，则基于此回复客户问题。", "fastParams": {"instance_id": "${vm}", "start_time": "${start_time}", "end_time": "${end_time}"}}, {"id": 4, "tool": "listColdMigrationRecords", "dependence": [], "input": "实例ID ${vm} 和时间范围 ${start_time} ~ ${end_time}", "analysis": "查询该VM在该时间段的冷迁移记录。如果客户问题与此相关，则基于此回复客户问题。", "fastParams": {"instanceId": "${vm}", "startTime": "${start_time}", "endTime": "${end_time}"}}]