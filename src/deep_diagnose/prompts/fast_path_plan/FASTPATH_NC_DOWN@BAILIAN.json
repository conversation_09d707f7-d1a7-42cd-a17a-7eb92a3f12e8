[{"id": 1, "tool": "listOperationRuleMatchRecords", "dependence": [], "input": "宿主机 ${arg1} 和时间范围${offset_timestamp(arg2, -3600)} 至 ${offset_timestamp(arg2, 3600)}", "analysis": "查询该NC的运维规则命中记录，获取可能的宕机原因及对应的运维动作。", "fastParams": {"machineId": "${arg1}", "startTime": "${offset_timestamp(arg2, -3600)}", "endTime": "${offset_timestamp(arg2, 3600)}"}}, {"id": 2, "tool": "runDiagnose", "dependence": [], "input": "宿主机 ${arg1}和时间范围${offset_timestamp(arg2, -3600)} 至 ${offset_timestamp(arg2, 3600)} 和诊断类型 NC可用性问题", "analysis": "诊断该NC在指定时间段内是否存在可用性问题，并获取相关的异常描述及其根因分析。", "fastParams": {"machineId": "${arg1}", "startTime": "${offset_timestamp(arg2, -3600)}", "endTime": "${offset_timestamp(arg2, 3600)}", "type": "REASON_NC_DOWN"}}, {"id": 3, "tool": "listChangeRecords", "dependence": [], "input": "宿主机 ${arg1}和时间范围${offset_timestamp(arg2, -3600)} 至 ${offset_timestamp(arg2, 3600)}", "analysis": "查询该NC在最近24小时内是否有高危变更操作，以排查是否存在由变更导致的宕机风险。", "fastParams": {"machineIds": ["${arg1}"], "startTime": "${offset_timestamp(arg2, -3600)}", "endTime": "${offset_timestamp(arg2, 3600)}"}}, {"id": 4, "tool": "getNcBasicInfo", "dependence": [], "input": "宿主机 ${arg1}", "analysis": "查询该NC的管控状态、锁定类型和锁定原因，确认该NC当前是否已经恢复正常。", "fastParams": {"ncs": ["${arg1}"]}}]