[{"id": 1, "tool": "listColdMigrationRecords", "dependence": [], "input": "实例ID ${arg1} 和时间范围${offset_timestamp(arg2, -3600)} 至 ${offset_timestamp(arg2, 3600)}", "analysis": "检查在指定时间范围内是否发生过冷迁移。如果有与宕机时间吻合的冷迁移记录，那么它可能是为了从非预期异常中恢复实例。", "fastParams": {"instanceId": "${arg1}", "startTime": "${offset_timestamp(arg2, -3600)}", "endTime": "${offset_timestamp(arg2, 3600)}"}}, {"id": 2, "tool": "listMonitorExceptions", "dependence": [], "input": "实例ID ${arg1}、时间范围${offset_timestamp(arg2, -3600)} 至 ${offset_timestamp(arg2, 3600)}", "analysis": "查询指定时间段内该实例是否存在主动运维事件相关异常监控项。", "fastParams": {"machineId": "${arg1}", "startTime": "${offset_timestamp(arg2, -3600)}", "endTime": "${offset_timestamp(arg2, 3600)}"}}, {"id": 3, "tool": "listReportedOperationalEvents", "dependence": [], "input": "实例ID ${arg1} 和时间范围${offset_timestamp(arg2, -3600)} 至 ${offset_timestamp(arg2, 3600)}", "analysis": "查找结束时间吻合的运维事件通知。如果存在状态为已执行的计划运维事件，则重启可能是该事件执行导致的；如果存在状态为已响应的计划运维事件，则重启可能是为了响应该事件而进行的；如果存在非预期运维事件，则重启可能是由于非预期的故障，此事件仅起到通知作用。", "fastParams": {"instanceId": "${arg1}", "startTime": "${offset_timestamp(arg2, -3600)}", "endTime": "${offset_timestamp(arg2, 3600)}"}}, {"id": 4, "tool": "listOperationRuleMatchRecords", "dependence": [], "input": "实例ID ${arg1} 和时间范围${offset_timestamp(arg2, -3600)} 至 ${offset_timestamp(arg2, 3600)}", "analysis": "查找时间吻合的运维规则命中记录，尝试确定宕机原因。", "fastParams": {"machineId": "${arg1}", "startTime": "${offset_timestamp(arg2, -3600)}", "endTime": "${offset_timestamp(arg2, 3600)}"}}, {"id": 5, "tool": "runDiagnose", "dependence": [], "input": "实例ID ${arg1}、时间范围${offset_timestamp(arg2, -3600)} 至 ${offset_timestamp(arg2, 3600)}及诊断类型VM可用性问题", "analysis": "对实例进行可用性诊断，以发现可能引起非预期宕机的具体原因。", "fastParams": {"machineId": "${arg1}", "startTime": "${offset_timestamp(arg2, -3600)}", "endTime": "${offset_timestamp(arg2, 3600)}", "type": "REASON_VM_APPLICABILITY"}}, {"id": 6, "tool": "getVmBasicInfo", "dependence": [], "input": "实例ID ${arg1}及查询选项实例状态", "analysis": "查询实例当前的状态，确定其目前是否已经恢复正常。如果返回为空，则实例已经释放；如果状态显示未恢复正常，建议向NC值班同学寻求帮助。", "fastParams": {"instanceIds": ["${arg1}"]}}, {"id": 7, "tool": "listHealthStatus", "dependence": [], "input": "实例ID ${arg1}及查询选项不展示历史", "analysis": "查询实例当前的健康状态，确定其目前是否已经恢复正常。注意，如果实例未释放且未恢复正常，建议向NC值班同学寻求帮助。", "fastParams": {"instanceId": "${arg1}", "showHistory": false}}]