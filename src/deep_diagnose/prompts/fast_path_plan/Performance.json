[{"id": 1, "tool": "runPerformanceDiagnose", "dependence": [], "input": "实例ID ${vm} 和时间范围 ${start_time} ~ ${end_time}", "analysis": "诊断该VM在该时间段的性能问题，以回复客户问题", "fastParams": {"instanceId": "${vm}", "startTime": "${start_time}", "endTime": "${end_time}"}}, {"id": 2, "tool": "listLiveMigrationRecords", "dependence": [], "input": "实例ID ${vm} 和时间范围 ${start_time} ~ ${end_time}", "analysis": "检查该VM在该时间段是否有实际执行的热迁移（可能引发短时性能抖动），以回复客户问题", "fastParams": {"instanceId": "${vm}", "startTime": "${start_time}", "endTime": "${end_time}"}}, {"id": 3, "tool": "runDiagnose", "dependence": [], "input": "实例ID ${vm} 和时间范围 ${start_time} ~ ${end_time} 和诊断选项VM性能", "analysis": "诊断该VM在该时间段的性能问题，以回复客户问题", "fastParams": {"machineId": "${vm}", "startTime": "${start_time}", "endTime": "${end_time}", "type": "REASON_VM_PERF"}}]