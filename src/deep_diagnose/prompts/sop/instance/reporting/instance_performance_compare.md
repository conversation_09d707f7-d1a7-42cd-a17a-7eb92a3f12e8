## 性能对比报告（模板约束）

输出结构：

- # [对比主题] 性能对比分析报告
- ## 对比概述
  - 对比目标：
  - 对比对象 A：
  - 对比对象 B：
  - 核心对比指标：
- ## 核心指标对比数据
  - 用表格对齐展示：指标 | A | B | 变化率
- ## 详细数据与图表（如有）
- ## 分析结论
  - 明确指出提升/下降/持平；量化差异；建议。

示例：

```markdown
# 新/旧内核版本 压测 性能对比分析报告

## 对比概述
- 对比目标：评估内核升级前后 P99 延迟与 CPU 使用率变化
- 对比对象 A：旧版本 5.10.x
- 对比对象 B：新版本 5.15.x
- 核心对比指标：CPU 平均使用率、P99 响应延迟

## 核心指标对比数据
| 指标 | A | B | 变化率 |
|---|---|---|---|
| CPU平均使用率 | 35% | 20% | ↓ 42.8% |
| P99 响应延迟 | 120ms | 80ms | ↓ 33.3% |

## 详细数据与图表
- A 快照：![A](URL_A)
- B 快照：![B](URL_B)

## 分析结论
- 综合数据，新版本在 CPU 使用率与延迟方面均显著改善，建议在灰度验证后推进全面升级。
```

**注意事项：**
- 核心指标表至少包含3个关键指标，必须有变化率计算
- 对显著变化的指标使用加粗或箭头符号标注
