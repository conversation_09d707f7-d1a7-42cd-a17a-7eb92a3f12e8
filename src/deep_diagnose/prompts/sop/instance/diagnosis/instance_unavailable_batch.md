# SOP：批量实例不可用诊断方案

## 诊断步骤（强制遵循，不得增减）

### 步骤1：查询实例在问题时间所在物理机
**目标**：排查故障实例是否存在物理机（NC）层面的聚集性，分析是否多个问题实例在故障时位于同一物理机
- **工具**：`listVmHostHistory`
- **输入参数**：完整实例ID列表，问题发生的具体时间范围
- **分析要点**：
  - 统计实例分布的物理机数量
  - 识别是否存在物理机聚集性故障
  - 分析物理机与实例问题的关联性
- **关键输出内容**：
  - 涉及物理机总数：X台NC
  - 物理机分布情况：每台NC上的实例数量和ID列表
  - 聚集性分析：是否存在多个实例集中在少数NC上
  - 物理机层面结论：是否为NC层面的批量故障

### 步骤2：逐一诊断实例不可用根本原因
**目标**：诊断每个实例的具体不可用根本原因，识别共同故障模式
- **工具**：`runVmUnavailableDiagnose`
- **输入参数**：完整实例ID列表，问题发生的具体时间范围
- **分析要点**：
  - 诊断每个实例的具体不可用原因
  - 识别实例间的共同故障模式
  - 分析故障原因的一致性和差异性
- **关键输出内容**：
  - 实例诊断结果汇总：每个实例的具体不可用原因
  - 共同故障模式：相同原因导致的实例数量和类型
  - 故障原因分类：网络/存储/计算/系统等维度的分类统计
  - 批量故障特征：是否存在统一的根本原因

### 步骤3：排查实例关联的高危变更记录
**目标**：检查是否有变更操作导致批量实例问题
- **工具**：`listChangeRecords`
- **输入参数**：完整实例ID列表，问题发生前后的时间范围（建议前后各2小时）
- **分析要点**：
  - 检查实例相关的所有变更记录
  - 识别高危变更操作类型
  - 分析变更时间与问题时间的关联性
  - 评估变更操作对批量实例的影响
- **关键输出内容**：
  - 变更记录总数：X条变更记录
  - 高危变更类型统计：热迁移/重启/配置变更/网络变更等
  - 变更时间分布：变更操作的时间分布与问题时间的关联
  - 变更影响评估：哪些变更可能导致批量实例问题

