# SOP：实例性能数据采集与性能异常诊断

## 诊断步骤（必须按顺序执行）

### 步骤1：实例性能异常诊断
**目标**：分析实例性能异常，获取初步诊断结果
- **工具**：`runPerformanceDiagnose`
- **输入参数**：实例ID（用户提供的实例标识）,时间范围（建议为用户报告时间前6小时，时间范围不超过6小时）
- **分析要点**：
  - 识别性能异常类型（CPU、内存、IO、网络）
  - 确定异常时间范围
  - 评估异常严重程度
- **关键输出内容**：
  - 性能异常类型：CPU高使用率/内存不足/IO瓶颈/网络异常/其他
  - 异常时间段：YYYY-MM-DD HH:MM:SS ~ YYYY-MM-DD HH:MM:SS
  - 异常严重程度：轻微/中等/严重
  - 初步诊断结论：性能异常的可能原因

### 步骤2：查询实例对应的NC信息
**目标**：获取实例所在的物理机（NC）信息
- **工具**：`getVmBasicInfo`
- **输入参数**：实例ID
- **分析要点**：
  - 提取NC IP地址
  - 确认实例与NC的映射关系
  - 记录实例基本配置信息
- **关键输出内容**：
  - NC IP地址：xxx.xxx.xxx.xxx
  - 实例规格：CPU核数、内存大小、磁盘配置
  - 实例状态：运行中/已停止/其他
  - 实例创建时间：YYYY-MM-DD HH:MM:SS

### 步骤3：检查实例异常事件列表
**目标**：查询实例是否存在监控异常或告警
- **工具**：`listMonitorExceptions`
- **输入参数**：实例ID、时间范围（基于步骤1获取的异常时间段）
- **分析逻辑**：
  - 返回内容为空 → 监控层面无异常记录
  - 返回内容不为空 → 分析监控异常与性能问题的关联性
- **关键输出内容**：
  - 监控异常状态：有异常/无异常
  - 异常类型：CPU告警/内存告警/磁盘告警/网络告警/其他
  - 异常详情：具体的监控指标和阈值
  - 异常时间与性能问题关联：时间匹配度分析


### 步骤4：检查NC异常事件列表
**目标**：依赖步骤1的NC IP，检查步骤2的NC是否存在监控异常或告警
- **工具**：`listMonitorExceptions`
- **输入参数**：NC IP、时间范围（如果存在异常事件段，则异常时间段前后扩展（建议扩展2小时，总时间长度不超过6小时）。如果没有异常时间段，则采纳用户需求给出时间段）
- **分析逻辑**：
  - 返回内容为空 → 监控层面无异常记录
  - 返回内容不为空 → 分析监控异常与性能问题的关联性
- **关键输出内容**：
  - 监控异常状态：有异常/无异常
  - 异常类型：CPU告警/内存告警/磁盘告警/网络告警/其他
  - 异常时间与性能问题关联：时间匹配度分析

### 步骤5：智能采集实例性能数据
**目标**：基于步骤1的诊断结果，智能选择性能指标进行精准数据采集
- **工具**：`coder` (code agent)

#### 5.1 指标选择策略（基于步骤1结果）

**情况A：步骤1识别到性能异常**
- **策略**：聚焦受损指标类型，进行针对性深度分析
- **指标选择逻辑**：
  - **CPU异常检测到** → 重点采集CPU相关指标，减少其他指标采集
    - 核心指标：`["VmStealMetric/vcpucpu", "VmStealMetric/vmsteal"]`
    - 辅助指标：`["VmCpuUtilization/cpu_util"]` （验证CPU使用率趋势）
  
  - **内存异常检测到** → 重点采集内存相关指标
    - 核心指标：`["VmMemBWMetric/memory_bw"]`
    - 辅助指标：`["VmMemoryUtilization/memory_util"]` （验证内存使用率）
  
  - **IO异常检测到** → 重点采集存储IO指标
    - 核心指标：`["VmStorageIOLatency/read_lat_ms", "VmStorageIOLatency/write_lat_ms", "VmStorageIOLatency/read_iops", "VmStorageIOLatency/write_iops"]`
    - 辅助指标：`["VmStorageIOBandwidth/read_bps", "VmStorageIOBandwidth/write_bps"]`
  
  - **网络异常检测到** → 重点采集网络相关指标
    - 核心指标：`["VmPpsBps/tx_pps", "VmPpsBps/rx_pps", "VmVportDropMetric/drop_ratio"]`
    - 辅助指标：`["VmNetworkBandwidth/tx_bps", "VmNetworkBandwidth/rx_bps"]`

**情况B：步骤1未检测到明确异常**
- **策略**：全面采集所有关键性能指标，进行综合分析
- **完整指标集合**：
  ```json
  [
    "VmPpsBps/tx_pps", "VmPpsBps/rx_pps",
    "VmNetworkRetryMetric/tx_retry", "VmNetworkRetryMetric/rx_retry",
    "VmSessionDetail/session_count",
    "VMLLCContenstionMetric/llc_hit_ratio", "VMLLCMetric/llc",
    "VmArpPingMetric/arp_drop_ratio", "VmArpPingMetric/arp_timeout_ratio",
    "VmStealMetric/vmsteal", "VmVportDropMetric/drop_ratio",
    "VmStealMetric/vcpucpu", "VMCPUFreq/freq",
    "VmStorageIOLatency/read_lat_ms", "VmStorageIOLatency/write_lat_ms",
    "VmMemBWMetric/memory_bw",
    "VmStorageIOLatency/write_iops", "VmStorageIOLatency/read_iops"
  ]
  ```

#### 5.2 执行参数
- **实例ID**：来源用户问题中的实例ID
- **性能指标**：根据上述策略智能选择的指标列表
- **时间范围**：用户需求给出时间范围，如步骤1识别到性能异常，则用步骤1的性能异常的时间范围前4小时+异常后1小时，如果没有异常，则用用户请求的时间范围

