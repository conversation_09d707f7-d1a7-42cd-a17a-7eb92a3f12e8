# SOP：实例启停问题诊断方案

## 开始前
- 必须按顺序执行。步骤1是全局时间与诊断基准，千万不要删除。
- 步骤1会产出：问题发生时间 T0 和时间窗口（以 T0 为中心，前后各 2 小时，含边界）。后续步骤全部使用它。

## 诊断步骤（必须按顺序执行）

### 步骤1：识别实例启停时间并确定统一时间基准（关键步骤，禁止删除）
**目标**：识别是否发生过启动/停止/重启（启停）或其他关键异常，并给出 T0 与时间窗口。
- 工具：`listMonitorExceptions`
- 输入参数：
  - 实例ID（可多个，取用户明确提及的实例ID集合）
  - 查询时间范围：用户问题包含时间，时间范围为【用户提供的时间前1天～用户提供的时间后1天】；若无，默认“当前时间向前2天 ~ 当前时间”
- 分析要点：
  - 是否存在启停相关事件（启动/停止/重启），记录精确发生时间；
  - 识别其他关键异常事件；
  - 有多条候选事件时，与用户描述比对，选择“最相关启停事件时间”。
- 关键输出（形成诊断计划，必须保留）：
  - 启停/重启事件列表（启动/停止/重启，时间升序，标注“最相关事件”）；
  - 关键异常清单（异常类型、时间、简要描述）；
  - 确定 T0：
    - 若识别到启停/重启事件：取“最相关启停事件时间”为 T0；
    - 若未识别到事件但用户提供了时间：取用户时间为 T0；
    - 若用户也未提供：T0=当前时间；
  - 时间窗口：以 T0 为中心，前后各 2 小时（含边界）；
  - 迁移时间：如果确认发生过迁移（由后续步骤校验），记录迁移时间并与 T0 的关系；
  - 说明：后续所有步骤必须使用上述 T0 与时间窗口，不得修改或删除。

### 步骤2：查询实例迁移记录信息（如有迁移则输出迁移前/后NC）
**目标**：查询实例在 T0 附近是否发生热迁移，并提取迁移时间与迁移前/后 NC IP。
- 工具：`listVmHostHistory`
- 输入参数：
  - 实例ID：用户提供实例ID
  - 时间范围：以 T0 为中心，前后各 1 小时（含边界）；若用户提供了更准确的迁移时间，也可使用“迁移时间±1小时”
- 分析要点：
  - 是否存在热迁移事件；
  - 若存在多次迁移，选择与 T0 最相关的一次；
  - 提取迁移前后 NC 标识（IP/ID）。
- 关键输出：
  - 迁移事件列表（时间升序）；
  - 最相关迁移事件时间（用于与步骤1的 T0 对齐）；
  - 迁移前 NC IP（记为 NC_before）；
  - 迁移后 NC IP（记为 NC_after）。
- 使用约定：
  - 后续凡是“依赖 NC”的步骤，均应优先基于本步骤的 NC_before/NC_after 分别执行；
  - 若未检测到迁移，则回退为“步骤3 获取的当前 NC”。

### 步骤3：获取实例基本信息
**目标**：获取实例基础信息，确定所在物理机（NC）。
- 工具：`getVmBasicInfo`
- 时间说明：本步骤不需要时间范围。
- 输入参数：
  - 实例ID
- 关键输出：
  - 实例基本信息：规格、状态、创建时间；
  - 所在物理机：当前 NC IP 地址（若步骤2发现迁移，请注意此处为迁移后 NC，仍需保留步骤2中的 NC_before/NC_after 以供后续使用）；
  - 实例当前状态：运行中/已停止/其他；
  - 实例配置信息：CPU、内存、磁盘等。

### 步骤4：分析物理机异常事件
**目标**：检查实例所在物理机（NC）是否存在异常事件。
- 工具：`listMonitorExceptions`
- 时间范围：T0±2小时（其中 T0 来自步骤1）
- 输入参数：
  - NC IP：
    - 优先使用步骤2产出的 NC_before 与 NC_after，并分别执行；
    - 若未检测到迁移，则使用步骤3获取的当前 NC；
  - 时间范围：T0±2小时（其中 T0 来自步骤1）
- 分析要点：
  - 是否存在 NC 宕机/严重异常；
  - 硬件故障告警；
  - 与实例问题的关联性。
- 关键输出：
  - 按 NC 分别输出：
    - NC 异常事件总数：X 条；
    - 异常事件类型：宕机/硬件故障/网络异常/其他；
    - 异常时间 vs T0；
    - NC 层面影响评估：是否影响实例运行。

### 步骤5：分析物理机运维记录
**目标**：检查物理机是否有运维操作影响实例。
- 工具：`listOperationRecords`
- 输入参数：
  - NC IP：
    - 优先使用步骤2产出的 NC_before 与 NC_after，并分别执行；
    - 若未检测到迁移，则使用步骤3获取的当前 NC；
  - 时间范围：T0±2小时（其中 T0 来自步骤1）
- 分析要点：
  - NC 重启或维护操作；
  - 运维时间与 T0 的关系；
  - 对实例的影响。
- 关键输出：
  - 按 NC 分别输出：
    - NC 运维记录总数：X 条；
    - 运维操作类型：重启/维护/升级/其他；
    - 运维时间 vs T0；
    - 运维影响评估：是否导致实例重启或宕机。

### 步骤6：分析物理机变更记录
**目标**：检查物理机配置或硬件变更。
- 工具：`listChangeRecords`
- 输入参数：
  - NC IP：
    - 优先使用步骤2产出的 NC_before 与 NC_after，并分别执行；
    - 若未检测到迁移，则使用步骤3获取的当前 NC；
  - 时间范围：T0±2小时（其中 T0 来自步骤1）
- 分析要点：
  - 硬件或配置变更；
  - 变更时间与 T0 的关系；
  - 对稳定性的影响。
- 关键输出：
  - 按 NC 分别输出：
    - NC 变更记录总数：X 条；
    - 变更类型：硬件变更/配置变更/系统更新/其他；
    - 变更时间 vs T0；
    - 变更影响评估：是否可能导致实例问题。

### 步骤7：分析实例异常事件
**目标**：检查实例层面是否有异常事件记录。
- 工具：`listMonitorExceptions`
- 输入参数：
  - 实例ID
  - 时间范围：T0±2小时（其中 T0 来自步骤1）
- 分析要点：
  - 实例重启/宕机记录；
  - 实例层面异常告警；
  - 与问题的直接关联性。
- 关键输出：
  - 实例异常事件总数：X 条；
  - 异常事件类型：重启/宕机/性能异常/其他；
  - 异常时间 vs T0；
  - 严重程度和影响评估。

### 步骤8：分析控制台操作记录
**目标**：检查是否有人为的控制台操作导致实例重启/停止/启动。
- 工具：`listActionTrail`
- 输入参数：
  - 实例ID
  - 时间范围：T0±2小时（其中 T0 来自步骤1）
- 分析要点：
  - 控制台重启、停止、启动操作；
  - 操作时间与 T0 的关系；
  - 操作用户和来源。
- 关键输出：
  - 控制台操作总数：X 条；
  - 操作类型：重启/停止/启动/配置变更/其他；
  - 操作时间 vs T0；
  - 操作来源：用户操作/系统自动。

### 步骤9：分析客户侧运维事件
**目标**：检查是否有计划性的运维事件通知。
- 工具：`listReportedOperationalEvents`
- 输入参数：
  - 实例ID
  - 时间范围：T0±2小时（其中 T0 来自步骤1）
- 分析要点：
  - 计划性维护通知；
  - 事件与实例问题的关联性；
  - 是否为预期的运维操作。
- 关键输出：
  - 运维事件总数：X 条；
  - 事件类型：计划维护/紧急维护/系统升级/其他；
  - 事件时间 vs T0；
  - 是否为计划内操作。

### 步骤10：启停专项诊断（新增）
**目标**：围绕实例启停问题进行专项诊断与归因。
- 工具：`runVmStartStopDiagnose`
- 输入参数：
  - 实例ID
  - 时间范围：T0±2小时（其中 T0 来自步骤1）
- 分析要点：
  - 聚焦实例在时间窗口内的停止/启动/重启行为；
  - 结合控制面、运维记录、异常事件进行交叉验证；
  - 定位启停原因：人为操作、系统策略、告警触发、资源不足、迁移配合、其他。
- 关键输出：
  - 启停事件诊断结论：归因类型与可信度；
  - 关键证据链：相关事件、操作、监控指标与时间对齐关系；
  - 修复与预防建议：控制面配置、告警与策略优化、资源与容量评估建议。
