## 基于 VMCore 的 NC 宕机根因诊断报告（模板约束）

输出结构：

- # [对象/核心问题] 深度诊断报告
- ## 诊断信息
  - 实例/对象：
  - 时间范围：
  - 问题描述：
- ## 关键要点
  - 列出3-5条基于证据的关键发现（按重要性排序）
- ## 推断过程
  1. [检查项目1]
     - 证据与结果：
     - 结论：
  2. [检查项目2]
     - 证据与结果：
     - 结论：
- ## 总结及建议
  - 根因归纳：
  - 修复方案：
  - 预防措施：

示例（基于VMCore的NC宕机根因诊断场景）：

```markdown


## 诊断信息
**NC IP**: ************
**时间范围**: 2025-06-09 10:00:00 至 2025-06-09 18:00:00
**问题描述**: 实例出现宕机，需要基于VMCore分析宕机根因

## 关键要点

************最近6个月发生7次宕机，最近一宕机的时间是2025/07/24 03:51分钟 

宕机根因：
  - 一级分类：[硬件故障（置信度 90%）]
  - 二级分类：[CPU故障（置信度 80%）]

是否已知问题：[是（存在历史bug ）]  
  

## 推断过程
1. 运获取宕机日志基础信息
   -  通过 `getNcDownRecord` 发现 ************最近6个月发生7次宕机，最近一宕机的时间是2025/07/24 03:51分钟 
2. 内核层面vmcore分析
   - 通过 `analyzeVmcoreFromCore` 没有查询任何内核问题，排除因为内核导致宕机可能性。
3. 虚拟化层面vmcore分析
   - 通过 `analyzeVmcoreFromVirt`  没有查询任何虚拟化问题，排除因为虚拟化导致宕机可能性。
4. 硬件层面vmcore分析
   - 分析 `analyzeVmcoreFromJinlun` 发现是因为CPU故障导致宕机。

5. 分析VM变更操作
   - 没有发现VM变更导致宕机可能性

6. 分析NC变更操作
   - 没有发现NC变更导致宕机可能性
```