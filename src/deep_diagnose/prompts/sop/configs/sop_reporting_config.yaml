# 报告SOP配置文件
reporting_sops:
  - sop_id: "nc_vmcore_crash_report"
    display_name: "NC宕机根因（VMCore）诊断模板"
    template_file: "nc/reporting/nc_vmcore_crash_report.md"
    related_sop_names:
      - "物理机宕机分析方案"
    example_queries:
      - "基于vmcore分析NC宕机根因"
      - "NC宕机vmcore诊断"

  - sop_id: "instance_root_cause_analysis"
    display_name: "深度诊断报告模板"
    template_file: "instance/reporting/instance_root_cause_analysis.md"
    related_sop_names:
      - "单实例重启或宕机诊断方案"
      - "物理机宕机分析方案"
    example_queries:
      - "实例i-xxx异常重启了"
      - "通过vmcore分析物理机宕机根因"

  - sop_id: "performance_comparison_report"
    display_name: "性能对比报告模板"
    template_file: "instance/reporting/instance_performance_compare.md"
    related_sop_names:
      - "其他问题"
    example_queries:
      - "升级前后性能对比分析"
      - "不同规格实例性能差异评估"