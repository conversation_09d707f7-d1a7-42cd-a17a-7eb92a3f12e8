# SOP：客户稳定报告产出流程

## 报告产出步骤（必须按顺序执行）

### 步骤1：客户VM健康度检查
**目标**：对客户所有VM实例进行全面健康度检查和分析
- **工具**：`checkHealthForCustomerVms`
- **输入参数**：客户ID（aluid，如：1781574661016173）,时间范围（默认最近2天）
- **分析要点**：
  - 检查客户下所有VM实例的健康状态
  - 分析实例运行状况和性能指标
  - 识别潜在的稳定性风险
  - 统计异常实例和正常实例数量
- **关键输出内容**：
  - `checkHealthForCustomerVms`工具输出转化csv 结构
  - `checkHealthForCustomerVms`工具内容转化为csv结构之后，表头每个字段名字和段描述
  - 原始工具输出数据
  
  ### 步骤2：数据分析
- **目标**：对前面输出数据根据用户需求做数据分析
- **工具**：`analyzeData`
- **分析要点**：
  - 针对用户需求输出对应图表

## 报告质量检查清单
- [ ] 参数来源检查：客户ID来自用户提供或历史消息，禁止编造
- [ ] 编造检查：所有数据和分析结果均来自工具返回，不得虚构
- [ ] 数据完整性：健康检查结果完整，包含所有必要的健康指标
- [ ] 逻辑一致性：健康状态判断逻辑合理，结论与数据匹配
- [ ] 建议可操作性：改进建议针对具体发现的问题，具有可执行性
- [ ] 客户针对性：报告内容针对该客户的实际VM健康状况

## 输出格式要求
- 报告语言：中文
- 数据精度：百分比保留2位小数
- 结构化输出：包含摘要、详细分析、问题清单、改进建议
- 重点突出：异常实例和高风险项目需要重点标注