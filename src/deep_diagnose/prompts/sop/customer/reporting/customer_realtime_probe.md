## 实时探测/启动停止/截图诊断报告（模板约束）

输出结构：

- # [探测对象] [探测类型] 实时探测报告
- ## 探测信息
  - 探测时间：{{ CURRENT_TIME }}
  - 探测目标：
  - 探测结果：成功/失败
- ## 关键数据
  - 响应延迟：
  - 返回状态码：
  - 截图链接（如适用）：
- ## 原始输出

示例：

```markdown
# ECS 实例 i-bp1xxxx 端口探测 实时探测报告

## 探测信息
- 探测时间：2025-06-09 10:00:00
- 探测目标：i-bp1xxxx 的 80 端口
- 探测结果：成功

## 关键数据
- 响应延迟：15ms
- 返回状态码：200 OK
- 截图链接：未提供

## 原始输出
<粘贴核心原始输出片段或链接>
```

**注意事项：**
- 将关键数值以项目符号列表列出
- 对异常或失败用加粗突出
