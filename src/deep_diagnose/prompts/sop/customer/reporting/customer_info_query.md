## 信息查询/状态检查报告（模板约束）

输出结构：

- # [查询对象] [查询主题] 信息报告
- ## 查询请求
  - 查询时间：{{ CURRENT_TIME }}
  - 查询对象：
  - 查询内容：
- ## 查询结果
  - 使用 Key-Value 表格展示核心信息。
  - 对缺失项使用“未提供/无/无法确认”。

示例：

```markdown
# ECS 实例 i-bp1xxxx 状态 信息报告

## 查询请求
- 查询时间：2025-06-09 10:00:00
- 查询对象：ECS 实例 i-bp1xxxx
- 查询内容：实例健康状态

## 查询结果
| 属性 | 值 |
|---|---|
| 实例ID | i-bp1xxxx |
| 实例状态 | Running |
| 健康检查状态 | OK |
| 系统事件 | 无 |
| 是否可迁移 | 是 |
| 不可迁移原因 | 未提供 |
```

**注意事项：**
- 对缺失项明确标注"无/未提供"，避免留空
- 异常项可加粗突出或置于表格顶部
