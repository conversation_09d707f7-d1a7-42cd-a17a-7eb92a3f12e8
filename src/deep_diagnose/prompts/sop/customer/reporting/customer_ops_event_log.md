## 操作与事件记录报告（模板约束）

输出结构：

- # [对象/事件] [操作/事件] 记录报告
- ## 摘要
  - 事件/操作名称：
  - 对象：
  - 结果：成功/失败/进行中
  - 时间范围：
- ## 关键阶段与日志
  - 用表格按时间顺序展示：时间戳 | 阶段 | 状态 | 摘要

示例：

```markdown
# ECS 实例 i-bp1xxxx 热迁移 记录报告

## 摘要
- 事件/操作名称：热迁移
- 对象：ECS 实例 i-bp1xxxx
- 结果：成功
- 时间范围：2025-06-09 10:00:00 至 2025-06-09 10:20:00

## 关键阶段与日志
| 时间戳 | 操作阶段 | 状态 | 详情/日志摘要 |
|---|---|---|---|
| 10:00:01 | 迁移任务创建 | Success | Task ID: a-xxxx |
| 10:02:10 | 数据预同步 | In-Progress | Copied 10GB/50GB |
| 10:10:30 | 服务切换 (Downtime) | Success | Downtime: 1.5s |
| 10:11:00 | 迁移完成 | Success | Instance running on target NC |
```

**注意事项：**
- 长日志请摘要化并截断，保留关键字段与ID
- 对失败或异常使用加粗强调
