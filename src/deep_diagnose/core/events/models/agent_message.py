"""
Agent消息模型

Agent消息记录的数据结构，重命名自 MessageRecord
"""

from dataclasses import dataclass, field
from typing import ClassVar
from typing import List, Optional
from .message_chunk import MessageChunk
from .tool_execution import ToolExecution, ToolExecutionStatus


def merge_incremental_text(existing: str, new_text: str, window: int = 80) -> str:
    """Robustly merge streaming text without losing information.
    Supports both delta-stream (chunks are new tokens) and snapshot-stream (each chunk is the full text so far).
    - If new_text starts with existing, treat as snapshot and replace with new_text.
    - Else, if existing ends with new_text, it's a duplicate tail: keep existing.
    - Else, find the longest overlap (suffix of existing vs prefix of new_text) within `window` and append the non-overlap.
    - Else, append new_text.
    Notes:
    - Do NOT drop content when new_text appears somewhere earlier in existing; prefer duplication over loss.
    """
    if not new_text:
        return existing or ""
    if not existing:
        return new_text

    # Snapshot pattern: provider sends the full text accumulated so far
    if new_text.startswith(existing):
        return new_text

    # Duplicate tail: sometimes providers resend the last tokens
    if existing.endswith(new_text):
        return existing

    # Overlap-aware append (suffix(existing) == prefix(new_text))
    max_overlap = min(len(existing), len(new_text), window)
    for k in range(max_overlap, 0, -1):
        if existing[-k:] == new_text[:k]:
            return existing + new_text[k:]

    # Fallback: conservative append (prefer duplication over loss)
    return existing + new_text


@dataclass
class AgentMessage:
    """Agent消息记录 - 更准确地反映这是Agent产生的消息"""
    run_id: str
    agent: str
    langgraph_id: str
    tags: List[str]
    metadata: Optional[int] = None

    # 累积的内容
    content: str = ""
    reasoning_content: str = ""

    # 工具执行
    tool_executions: List[ToolExecution] = field(default_factory=list)

    # 原始数据片段
    message_chunks: List[MessageChunk] = field(default_factory=list)

    # 状态信息
    is_finished: bool = False
    finish_reason: Optional[str] = None

    # 稳定排序的序号（可用 SSE seq 或内部自增）
    order: int = 0
    _next_order: ClassVar[int] = 1

    def add_chunk(self, chunk: MessageChunk):
        """添加消息片段"""
        self.message_chunks.append(chunk)

    def add_tool_execution(self, tool_execution: ToolExecution):
        """添加工具执行"""
        self.tool_executions.append(tool_execution)

    def update_content(self, content: str = "", reasoning_content: str = ""):
        """更新内容，并在首次写入时分配稳定序号"""
        if self.order == 0:
            self.order = AgentMessage._next_order
            AgentMessage._next_order += 1
        if content:
            self.content = merge_incremental_text(self.content, content)
        if reasoning_content:
            self.reasoning_content = merge_incremental_text(self.reasoning_content, reasoning_content)

    def set_finished(self, finish_reason: Optional[str] = None):
        """设置完成状态"""
        self.is_finished = True
        self.finish_reason = finish_reason

    def get_completed_tool_executions(self) -> List[ToolExecution]:
        """获取已完成的工具执行"""
        return [te for te in self.tool_executions if te.status == ToolExecutionStatus.COMPLETED]

    def find_tool_execution(self, call_id: str) -> Optional[ToolExecution]:
        """查找工具执行"""
        for tool_execution in self.tool_executions:
            if tool_execution.call_id == call_id:
                return tool_execution
        return None
    
    def __hash__(self):
        """使AgentMessage可哈希，基于run_id"""
        return hash(self.run_id)
    
    def __eq__(self, other):
        """基于run_id判断相等性"""
        if not isinstance(other, AgentMessage):
            return False
        return self.run_id == other.run_id