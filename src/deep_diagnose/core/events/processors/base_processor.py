"""
基础消息处理器

定义消息处理器的抽象接口，重命名自 BaseAgentEventProcessor
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional

from deep_diagnose.core.reasoning.workflow.types import ReasoningState
from ..models import AgentMessage

logger = logging.getLogger(__name__)


class BaseMessageProcessor(ABC):
    """基础消息处理器 - 更准确地反映处理的是消息而非事件"""

    def __init__(self, request_id: Optional[str] = None):
        """
        初始化基础消息处理器
        
        Args:
            request_id: 请求ID，用于标识处理上下文
        """
        self.request_id = request_id

    @abstractmethod
    def process_messages(self, messages: List[AgentMessage]) -> Dict[str, Any]:
        """处理特定Agent的消息，并可访问当前图的完整状态。"""
        pass

    @abstractmethod
    def get_agent_name(self) -> str:
        """获取处理的Agent名称"""
        pass
