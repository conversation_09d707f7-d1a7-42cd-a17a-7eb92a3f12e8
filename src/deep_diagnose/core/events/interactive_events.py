"""
Agent V1 事件模型

简化的事件模型，主要包含一个 InteractiveAgentEvent 类。
"""

import json
from dataclasses import dataclass

from deep_diagnose.core.events.base_event import BaseAgentOutputEvent


@dataclass
@BaseAgentOutputEvent.register_event_type("interactive")
class InteractiveAgentEvent(BaseAgentOutputEvent):
    """事件 - 累积式状态容器"""
    result: str = ""  # 累积所有结果内容
    finished: bool = False # 输出结束标识

    def parse(self, raw_event: str) -> bool:
        return False

    def parse_graph_event(self, event_data, finished) -> bool:
        """
        保存 LangGraph 事件
        
        Args:
            event_data: LangGraph 事件数据
            finished: 是否完成的标识
            
        Returns:
            bool: 是否成功更新了状态
        """
        self.result = event_data
        self.finished = finished
        return True


    def to_sse_format(self) -> str:
        """
        将事件序列化为 SSE 格式
        """
        payload = {
            "result": self.result,
        }

        return json.dumps(payload, ensure_ascii=False)
