"""
消息仓库

负责Agent消息的存储和检索，重命名自 MessageStore
"""

from typing import List, Optional, Dict
from collections import defaultdict
from ..models import AgentMessage


class MessageRepository:
    """Agent消息仓库 - 更准确地反映数据访问层的职责（索引化）"""
    
    def __init__(self):
        # 原始顺序（兼容 get_all_messages 调用）
        self.agent_messages: List[AgentMessage] = []
        # 索引
        self._by_run_id: Dict[str, AgentMessage] = {}
        self._by_agent: Dict[str, List[AgentMessage]] = defaultdict(list)
        self._by_tool_call_id: Dict[str, AgentMessage] = {}

    def get_or_create_agent_message(self, agent_name: str, run_id: str, langgraph_id: str, tags: list,metadata) -> Optional[AgentMessage]:
        """获取或创建Agent消息（按 run_id 唯一），O(1) 查找"""
        # 优先命中 run_id 索引
        existing = self._by_run_id.get(run_id)
        if existing is not None:
            return existing
        # 创建并回填索引
        new_message = AgentMessage(
            agent=agent_name,
            run_id=run_id,
            langgraph_id=langgraph_id,
            tags=tags,
            metadata=metadata
        )
        # 为新消息分配稳定序号（用于跨消息排序），避免仅在有 content 时才赋值导致顺序不稳定
        if getattr(new_message, "order", 0) == 0:
            try:
                from ..models import AgentMessage as _AM
                new_message.order = _AM._next_order
                _AM._next_order += 1
            except Exception:
                # 兜底：保持 0，但通常不会进入
                pass
        self.agent_messages.append(new_message)
        self._by_run_id[run_id] = new_message
        self._by_agent[agent_name].append(new_message)
        return new_message

    def get_messages_by_agent(self, agent_name: str) -> List[AgentMessage]:
        """根据Agent名称获取消息列表（O(1)）"""
        return list(self._by_agent.get(agent_name, []))

    def find_message_by_tool_execution(self, tool_call_id: str) -> Optional[AgentMessage]:
        """根据工具执行ID查找消息（O(1) 缓存 + 首次 O(n) 回填）"""
        cached = self._by_tool_call_id.get(tool_call_id)
        if cached is not None:
            return cached
        # 首次未命中：扫描并回填索引
        for agent_message in self.agent_messages:
            if agent_message.tool_executions:
                for tool_execution in agent_message.tool_executions:
                    if tool_execution.call_id:
                        self._by_tool_call_id.setdefault(tool_execution.call_id, agent_message)
                        if tool_execution.call_id == tool_call_id:
                            return agent_message
        return None

    def get_all_messages(self) -> List[AgentMessage]:
        """获取所有消息"""
        return list(self.agent_messages)