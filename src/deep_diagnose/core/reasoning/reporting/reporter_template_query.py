"""
Reporter 模板查询模块 - 类似于 SOP 查询

负责加载 reporter_sop.yaml 并按需读取模板内容
"""

import logging
from pathlib import Path
from typing import Dict, Any, List

import yaml

logger = logging.getLogger(__name__)


class ReporterTemplateQuery:
    """Reporter 模板查询"""

    def __init__(self, config_path: str = "deep_diagnose/prompts/sop/configs/sop_reporting_config.yaml"):
        self.config_path = Path(config_path)
        self.template_dir = self.config_path.parent
        self._configs: List[Dict[str, Any]] | None = None

    def get_configs(self) -> List[Dict[str, Any]]:
        """获取所有Reporter模板配置"""
        if self._configs is None:
            try:
                with open(self.config_path, "r", encoding="utf-8") as f:
                    data = yaml.safe_load(f) or {}
                    # 使用新的字段名
                    self._configs = data.get("reporting_sops", [])
            except Exception as e:
                logger.error(f"Failed to load reporter_sop configs: {e}")
                self._configs = []
        return self._configs

    def list_templates(self) -> List[str]:
        configs = self.get_configs()
        return [c.get("display_name", "") for c in configs]

    def get_template_config(self, name: str) -> Dict[str, Any]:
        for c in self.get_configs():
            if c.get("display_name") == name:
                return c
        return {}

    def load_template_content(self, file_name: str) -> str:
        if not file_name:
            return ""
        try:
            # 使用配置中的完整相对路径
            base_prompts_dir = self.config_path.parent.parent
            file_path = base_prompts_dir / file_name
            
            if file_path.exists():
                with open(file_path, "r", encoding="utf-8") as f:
                    return f.read()
            
            # 兼容旧路径：如果完整路径不存在，尝试在旧目录中查找
            legacy_file_path = self.template_dir / Path(file_name).name
            if legacy_file_path.exists():
                logger.warning(f"Using legacy path for template file: {file_name}")
                with open(legacy_file_path, "r", encoding="utf-8") as f:
                    return f.read()
            
            logger.warning(f"Reporter template file not found: {file_name}")
            return ""
        except Exception as e:
            logger.error(f"Failed to load reporter template content: {e}")
            return ""


# 全局单例
_query_instance: ReporterTemplateQuery | None = None

def get_reporter_template_query() -> ReporterTemplateQuery:
    global _query_instance
    if _query_instance is None:
        _query_instance = ReporterTemplateQuery()
    return _query_instance
