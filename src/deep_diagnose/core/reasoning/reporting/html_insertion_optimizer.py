#!/usr/bin/env python3
"""
HTML插入优化器

## 角色与目标
专门负责优化HTML报告中性能数据section的插入逻辑，确保插入位置准确、HTML结构完整。

## 核心功能
1. **智能位置检测**：准确识别总结建议section的结束位置
2. **结构完整性**：确保插入后HTML结构完整
3. **脚本去重**：避免重复的CSS/JS引用
4. **错误恢复**：提供多种插入策略的回退机制
"""

import re
import logging
from typing import Optional, Tuple, List
from bs4 import BeautifulSoup, Comment

logger = logging.getLogger(__name__)


class HTMLInsertionOptimizer:
    """HTML插入优化器"""
    
    def __init__(self):
        self.insertion_strategies = [
            self._strategy_append_as_appendix,  # 修改为将性能数据作为附录添加
            self._strategy_before_body_end,
            self._strategy_append_to_content
        ]
    
    def optimize_performance_insertion(self, html_content: str, performance_section: str, request_id: str) -> str:
        """
        优化性能数据section的插入
        
        Args:
            html_content: 原始HTML内容
            performance_section: 性能数据section HTML
            request_id: 请求ID
            
        Returns:
            str: 优化后的HTML内容
        """
        try:
            # 1. 预处理性能section，移除重复的脚本引用
            cleaned_performance_section = self._clean_performance_section(performance_section, html_content)
            
            # 2. 尝试各种插入策略
            for i, strategy in enumerate(self.insertion_strategies):
                try:
                    result = strategy(html_content, cleaned_performance_section)
                    if result:
                        logger.info(f"成功使用策略 {i+1} 插入性能数据，请求ID: {request_id}")
                        
                        # 3. 验证插入结果
                        if self._validate_html_structure(result):
                            return result
                        else:
                            logger.warning(f"策略 {i+1} 插入后HTML结构验证失败")
                            continue
                            
                except Exception as e:
                    logger.warning(f"策略 {i+1} 执行失败: {e}")
                    continue
            
            # 4. 所有策略都失败，返回原始内容
            logger.error(f"所有插入策略都失败，返回原始HTML，请求ID: {request_id}")
            return html_content
            
        except Exception as e:
            logger.error(f"HTML插入优化失败: {e}")
            return html_content
    
    def _clean_performance_section(self, performance_section: str, original_html: str) -> str:
        """
        清理性能section，移除重复的脚本引用
        
        Args:
            performance_section: 原始性能section
            original_html: 原始HTML内容
            
        Returns:
            str: 清理后的性能section
        """
        try:
            # 检查原始HTML中是否已包含相关脚本
            has_tailwind = 'cdn.tailwindcss.com' in original_html
            has_chartjs = 'cdn.jsdelivr.net/npm/chart.js' in original_html
            
            cleaned_section = performance_section
            
            # 如果原始HTML已包含Tailwind CSS，移除重复引用
            if has_tailwind:
                cleaned_section = re.sub(
                    r'<script[^>]*cdn\.tailwindcss\.com[^>]*></script>\s*',
                    '',
                    cleaned_section,
                    flags=re.IGNORECASE
                )
            
            # 如果原始HTML已包含Chart.js，移除重复引用
            if has_chartjs:
                cleaned_section = re.sub(
                    r'<script[^>]*cdn\.jsdelivr\.net/npm/chart\.js[^>]*></script>\s*',
                    '',
                    cleaned_section,
                    flags=re.IGNORECASE
                )
            
            # 确保Chart.js在需要时被添加
            if not has_chartjs and 'Chart(' in cleaned_section:
                chart_script = '<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>\n        '
                # 在第一个<script>标签前插入Chart.js
                script_match = re.search(r'<script[^>]*>', cleaned_section)
                if script_match:
                    insert_pos = script_match.start()
                    cleaned_section = (
                        cleaned_section[:insert_pos] + 
                        chart_script + 
                        cleaned_section[insert_pos:]
                    )
                else:
                    # 如果没有其他script标签，在section开始处添加
                    cleaned_section = chart_script + cleaned_section
            
            return cleaned_section
            
        except Exception as e:
            logger.warning(f"清理性能section失败: {e}")
            return performance_section
    
    def _strategy_append_as_appendix(self, html_content: str, performance_section: str) -> Optional[str]:
        """
        策略1: 将性能数据作为附录添加到报告末尾
        """
        # 查找</body>标签位置
        body_end = html_content.rfind('</body>')
        if body_end != -1:
            # 在</body>标签前插入性能数据section
            result = (
                html_content[:body_end] + 
                '\n    ' + 
                performance_section + 
                '\n    ' + 
                html_content[body_end:]
            )
            return result
        
        return None
    
    def _strategy_before_body_end(self, html_content: str, performance_section: str) -> Optional[str]:
        """
        策略2: 在</body>标签之前插入
        """
        body_end = html_content.rfind('</body>')
        if body_end != -1:
            result = (
                html_content[:body_end] + 
                '\n    ' + 
                performance_section + 
                '\n    ' + 
                html_content[body_end:]
            )
            return result
        
        return None
    
    def _strategy_before_body_end(self, html_content: str, performance_section: str) -> Optional[str]:
        """
        策略3: 在</body>标签之前插入
        """
        body_end = html_content.rfind('</body>')
        if body_end != -1:
            result = (
                html_content[:body_end] + 
                '\n    ' + 
                performance_section + 
                '\n    ' + 
                html_content[body_end:]
            )
            return result
        
        return None
    
    def _strategy_append_to_content(self, html_content: str, performance_section: str) -> Optional[str]:
        """
        策略4: 直接追加到HTML末尾（最后的回退策略）
        """
        # 在</html>之前插入
        html_end = html_content.rfind('</html>')
        if html_end != -1:
            result = (
                html_content[:html_end] + 
                '\n' + 
                performance_section + 
                '\n' + 
                html_content[html_end:]
            )
            return result
        else:
            # 直接追加
            return html_content + '\n' + performance_section
    
    def _validate_html_structure(self, html_content: str) -> bool:
        """
        验证HTML结构的完整性
        
        Args:
            html_content: HTML内容
            
        Returns:
            bool: 结构是否完整
        """
        try:
            # 基本标签配对检查
            basic_checks = [
                (html_content.count('<html'), html_content.count('</html>')),
                (html_content.count('<head'), html_content.count('</head>')),
                (html_content.count('<body'), html_content.count('</body>')),
            ]
            
            for open_count, close_count in basic_checks:
                if open_count != close_count:
                    logger.warning(f"HTML标签配对不匹配: {open_count} vs {close_count}")
                    return False
            
            # 检查是否包含基本结构
            required_elements = ['<!DOCTYPE', '<html', '<head', '<body']
            for element in required_elements:
                if element not in html_content:
                    logger.warning(f"缺少必需的HTML元素: {element}")
                    return False
            
            # 检查性能数据是否成功插入
            if "附录A. 性能数据分析" not in html_content:
                logger.warning("性能数据section未成功插入")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"HTML结构验证失败: {e}")
            return False
    
    def analyze_html_structure(self, html_content: str) -> dict:
        """
        分析HTML结构，提供调试信息
        
        Args:
            html_content: HTML内容
            
        Returns:
            dict: 结构分析结果
        """
        try:
            analysis = {
                'total_length': len(html_content),
                'sections_found': [],
                'script_tags': [],
                'structure_issues': []
            }
            
            # 查找所有section
            section_patterns = [
                r'<h2[^>]*>([^<]+)</h2>',
                r'<h1[^>]*>([^<]+)</h1>',
            ]
            
            for pattern in section_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                analysis['sections_found'].extend(matches)
            
            # 查找所有script标签
            script_matches = re.findall(r'<script[^>]*src="([^"]+)"[^>]*>', html_content, re.IGNORECASE)
            analysis['script_tags'] = script_matches
            
            # 检查结构问题
            if html_content.count('<html') != html_content.count('</html>'):
                analysis['structure_issues'].append('HTML标签不匹配')
            
            if html_content.count('<body') != html_content.count('</body>'):
                analysis['structure_issues'].append('Body标签不匹配')
            
            # 检查重复脚本
            script_counts = {}
            for script in script_matches:
                script_counts[script] = script_counts.get(script, 0) + 1
            
            for script, count in script_counts.items():
                if count > 1:
                    analysis['structure_issues'].append(f'重复脚本: {script} ({count}次)')
            
            return analysis
            
        except Exception as e:
            logger.error(f"HTML结构分析失败: {e}")
            return {'error': str(e)}


# 创建全局实例
html_insertion_optimizer = HTMLInsertionOptimizer()