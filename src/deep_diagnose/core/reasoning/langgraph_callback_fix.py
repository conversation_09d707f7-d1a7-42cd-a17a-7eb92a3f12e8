"""
LangGraph Callback Fix

This module provides a fix for the LangGraph callback issue where
'NoneType' object is not callable in FuturesDict.on_done.
"""

import logging
from typing import Any, Callable, Optional
from functools import wraps

logger = logging.getLogger(__name__)


def safe_callback_wrapper(callback: Optional[Callable]) -> Callable:
    """
    Wrap a callback to handle None cases and exceptions safely.
    
    Args:
        callback: The original callback function (may be None)
        
    Returns:
        A safe wrapper function that handles None callbacks
    """
    @wraps(callback if callback else lambda: None)
    def wrapper(*args, **kwargs):
        if callback is not None and callable(callback):
            try:
                return callback(*args, **kwargs)
            except Exception as e:
                logger.error(f"Callback execution error: {e}", exc_info=True)
        else:
            logger.debug("Callback is None or not callable, skipping")
    
    return wrapper


def patch_langgraph_futures():
    """
    Patch LangGraph's FuturesDict to handle None callbacks safely.
    This should be called before using LangGraph components.
    """
    try:
        from langgraph.pregel.runner import FuturesDict
        
        # Store original on_done method
        original_on_done = FuturesDict.on_done
        
        def safe_on_done(self, task, exception_handler=None):
            """Safe version of on_done that handles None callbacks"""
            try:
                # Ensure callback is not None before calling
                if hasattr(self, 'callback') and callable(self.callback):
                    return original_on_done(self, task, exception_handler)
                else:
                    logger.debug("FuturesDict callback is None, skipping on_done")
                    return None
            except Exception as e:
                logger.error(f"Error in FuturesDict.on_done: {e}", exc_info=True)
                return None
        
        # Patch the method
        FuturesDict.on_done = safe_on_done
        logger.info("Successfully patched LangGraph FuturesDict.on_done")
        
    except ImportError:
        logger.warning("LangGraph not available, skipping patch")
    except Exception as e:
        logger.error(f"Failed to patch LangGraph FuturesDict: {e}")


def apply_langgraph_fixes():
    """
    Apply all LangGraph-related fixes.
    Call this function early in your application startup.
    """
    patch_langgraph_futures()
    logger.info("LangGraph fixes applied")


# Auto-apply fixes when module is imported
apply_langgraph_fixes()