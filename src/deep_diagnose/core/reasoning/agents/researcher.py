"""
研究员Agent - 负责执行研究任务的核心业务逻辑
"""

from typing import Literal, Optional
from langchain_core.runnables import RunnableConfig
from langgraph.types import Command

from .base import SubAgent
from .step_executor import StepExecutor
from deep_diagnose.core.reasoning.workflow.types import ReasoningState


class ResearcherAgent(SubAgent):
    """研究员Agent - 系统诊断的核心执行者"""
    
    def __init__(self):
        super().__init__("researcher", "researcher")
        self.step_executor = StepExecutor(self)
    
    async def _do_execute(self, state: ReasoningState, config: Optional[RunnableConfig] = None) -> Command:
        """执行研究任务的核心逻辑"""
        
        # 验证执行条件
        if not self._validate_execution_conditions(state):
            self.logger.warning("Execution conditions not met")
            return Command(goto="research_team")
        
        # 执行当前步骤
        return await self.step_executor.execute_current_step(state, config)
    
    def _handle_error(self, error: Exception, state: ReasoningState) -> Command:
        """研究员特定的错误处理"""
        self.logger.error(f"Research execution failed: {error}")
        # 研究失败时返回研究团队重新分配
        return Command(goto="research_team")
    
    def _validate_execution_conditions(self, state: ReasoningState) -> bool:
        """验证是否具备执行条件"""
        current_plan = state.get("current_plan")
        
        # 检查是否有有效的计划
        if not current_plan:
            self.logger.error("No current plan found")
            return False
        
        # 检查是否有待执行的步骤
        if not hasattr(current_plan, 'steps') or not current_plan.steps:
            self.logger.error("No steps found in current plan")
            return False
        
        # 检查是否有未完成的研究步骤
        unfinished_steps = [step for step in current_plan.steps if not step.execution_res]
        if not unfinished_steps:
            self.logger.info("All research steps completed")
            return False
        
        return True
    
    def get_capabilities(self) -> dict:
        """获取研究员的能力描述"""
        return {
            "primary_role": "系统诊断和问题分析",
            "capabilities": [
                "执行系统诊断命令",
                "分析日志和配置文件", 
                "调用MCP工具进行深度检查",
                "生成结构化的诊断报告"
            ],
            "tools": "动态从MCP配置获取",
            "output_format": "结构化的中文诊断结果"
        }


# 创建实例和节点函数
_researcher = ResearcherAgent()

async def researcher_node(state: ReasoningState, config: RunnableConfig) -> Command[Literal["research_team"]]:
    """研究员节点函数"""
    return await _researcher.execute(state, config)