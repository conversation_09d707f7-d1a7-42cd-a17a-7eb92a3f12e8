"""
研究团队Agent - 负责协调研究任务分配
"""

from typing import Literal, Optional
from langchain_core.runnables import RunnableConfig
from langgraph.types import Command

from .base import SubAgent
from deep_diagnose.core.reasoning.workflow.types import ReasoningState
from deep_diagnose.prompts.planner_model import StepType, StepStatus


class ResearchTeamAgent(SubAgent):
    """研究团队Agent"""
    
    def __init__(self):
        super().__init__("research_team", "research_team")
    
    async def _do_execute(self, state: ReasoningState, config: Optional[RunnableConfig] = None) -> Command:
        current_plan = state.get("current_plan")
        if not current_plan or not current_plan.steps:
            return Command(goto="planner")
        
        # 检查是否需要重新规划
        if self._should_trigger_replanning(current_plan):
            return Command(goto="replanner")
        
        # 检查是否所有步骤都完成
        if all(self._is_step_completed(step) for step in current_plan.steps):
            return Command(goto="planner")
        
        # 找到下一个可执行的步骤
        for step in current_plan.steps:
            if not self._is_step_completed(step) and self._can_execute_step(step, current_plan.steps):
                if step.step_type == StepType.PROCESSING:
                    return Command(goto="coder")
                else:
                    return Command(goto="researcher")
        
        return Command(goto="planner")
    
    def _should_trigger_replanning(self, current_plan) -> bool:
        """检查是否应该触发重新规划
        
        策略：只有当刚刚完成的步骤（相比上次检查是新完成的）有依赖时才触发replan
        """
        # 获取当前所有已完成步骤的step_order
        current_completed_orders = set()
        for step in current_plan.steps:
            if (hasattr(step, 'step_status') and step.step_status == StepStatus.SUCCESS and
                hasattr(step, 'execution_res') and step.execution_res):
                current_completed_orders.add(getattr(step, 'step_order', 0))
        
        # 获取上次记录的已完成步骤（存储在plan的一个临时属性中）
        last_completed_orders = getattr(current_plan, '_last_completed_orders', set())
        
        # 找到新完成的步骤
        newly_completed_orders = current_completed_orders - last_completed_orders
        
        if not newly_completed_orders:
            return False
        
        # 检查是否有未开始的步骤依赖于新完成的步骤
        has_dependent_steps = False
        for step in current_plan.steps:
            if (hasattr(step, 'step_status') and step.step_status == StepStatus.NOT_START and
                getattr(step, 'prerequisite_steps', []) and
                any(dep in newly_completed_orders for dep in getattr(step, 'prerequisite_steps', []))):
                has_dependent_steps = True
                break
        
        # 更新记录的已完成步骤
        current_plan._last_completed_orders = current_completed_orders
        
        return has_dependent_steps
    
    def _is_step_completed(self, step) -> bool:
        """检查步骤是否已完成"""
        if hasattr(step, 'step_status'):
            return step.step_status in [StepStatus.SUCCESS, StepStatus.FAILED, StepStatus.SKIP]
        return hasattr(step, 'execution_res') and step.execution_res
    
    def _can_execute_step(self, step, all_steps) -> bool:
        """检查步骤是否可以执行（依赖已满足）"""
        prerequisite_steps = getattr(step, 'prerequisite_steps', [])
        if not prerequisite_steps:
            return True
        
        # 检查所有依赖是否已完成
        completed_orders = {getattr(s, 'step_order', 0) for s in all_steps 
                          if hasattr(s, 'step_status') and s.step_status == StepStatus.SUCCESS}
        
        return all(dep in completed_orders for dep in prerequisite_steps)


# 创建实例和节点函数
_research_team = ResearchTeamAgent()

async def research_team_node(state: ReasoningState) -> Command[Literal["planner", "researcher", "coder", "replanner"]]:
    return await _research_team.execute(state)