"""
步骤执行服务 - 专门处理计划步骤的执行逻辑
"""

import logging
from typing import Optional
from langchain_core.runnables import RunnableConfig
from langchain_core.messages import HumanMessage, AIMessage
from langgraph.types import Command

from deep_diagnose.core.reasoning.workflow.types import ReasoningState
from .react_agent_factory import setup_mcp_tools, create_agent_with_tools, get_recursion_limit
from deep_diagnose.prompts.planner_model import StepStatus

logger = logging.getLogger(__name__)


class StepExecutor:
    """步骤执行器 - 负责执行计划中的具体步骤"""
    
    def __init__(self, agent_instance):
        self.agent = agent_instance
        self.logger = agent_instance.logger
    
    async def execute_current_step(self, state: ReasoningState, config: RunnableConfig) -> Command:
        """执行当前步骤"""
        
        try:
            # 1. 找到当前步骤
            current_step, step_num = self._find_current_step(state)
            if not current_step:
                return Command(goto="research_team")
            
            # 设置步骤状态为执行中（如果需要的话，可以添加 "running" 状态）
            # 这里我们直接执行，成功后设置为 success，失败后设置为 failed
            
            # 2. 准备工具和创建执行器
            tools = await self._prepare_tools(config)
            
            executor = await create_agent_with_tools(
                self.agent.agent_type, 
                tools, 
                config,
                step_title=current_step.title,
                step_description=current_step.description,
                request_id=state.get("request_id", "")
            )
            
            # 3. 准备输入并执行
            agent_input = self._prepare_input(state, current_step)
            
            result = await executor.ainvoke(
                input=agent_input, 
                config={
                    "recursion_limit": get_recursion_limit(),
                    "tags": [f"plan_step:{step_num}"]
                }
            )
            
            # 4. 更新结果
            if not result or "messages" not in result or not result["messages"]:
                raise ValueError("Agent execution returned empty or invalid result")
            
            response_content = result["messages"][-1].content
            current_step.execution_res = response_content
            # 设置步骤状态为成功
            if hasattr(current_step, 'step_status'):
                current_step.step_status = StepStatus.SUCCESS
            # 设置observation字段
            if hasattr(current_step, 'observation'):
                current_step.observation = response_content
            
            return Command(
                update={
                    "messages": [HumanMessage(content=response_content, name=self.agent.agent_type)],
                    "observations": state.get("observations", []) + [response_content],
                },
                goto="research_team",
            )
            
        except Exception as e:
            self.logger.error(f"Error executing step: {e}")
            import traceback
            self.logger.error(f"Full traceback:\n{traceback.format_exc()}")
            
            # 返回错误信息而不是崩溃
            error_message = f"Step execution failed: {str(e)}"
            if 'current_step' in locals():
                current_step.execution_res = error_message
                # 设置步骤状态为失败
                if hasattr(current_step, 'step_status'):
                    current_step.step_status = StepStatus.FAILED
                # 设置observation字段
                if hasattr(current_step, 'observation'):
                    current_step.observation = error_message
            
            return Command(
                update={
                    "messages": [HumanMessage(content=error_message, name=self.agent.agent_type)],
                    "observations": state.get("observations", []) + [error_message],
                },
                goto="research_team",
            )
    
    def _find_current_step(self, state: ReasoningState):
        """找到当前需要执行的步骤
        
        基于step_order排序，检查prerequisite_steps，只返回依赖已满足的步骤
        
        Returns:
            tuple: (current_step, step_num) 或 (None, None) 如果没有找到
        """
        current_plan = state.get("current_plan")
        if not current_plan or not hasattr(current_plan, 'steps') or not current_plan.steps:
            return None, None
        
        # 按step_order排序步骤
        sorted_steps = sorted(current_plan.steps, key=lambda x: getattr(x, 'step_order', 0))
        
        # 构建已完成步骤的step_order集合
        completed_step_orders = set()
        for step in sorted_steps:
            # 检查步骤是否成功完成（优先使用step_status，回退到execution_res）
            is_completed = False
            if hasattr(step, 'step_status'):
                is_completed = step.step_status == StepStatus.SUCCESS
            elif hasattr(step, 'execution_res') and step.execution_res:
                is_completed = True
                
            if is_completed:
                completed_step_orders.add(getattr(step, 'step_order', 0))
        
        # 查找第一个未执行且依赖已满足的步骤
        for step in sorted_steps:
            # 检查步骤是否已执行（优先使用step_status，回退到execution_res）
            is_executed = False
            if hasattr(step, 'step_status'):
                is_executed = step.step_status in [StepStatus.SUCCESS, StepStatus.FAILED, StepStatus.SKIP]
            elif hasattr(step, 'execution_res') and step.execution_res:
                is_executed = True
                
            if is_executed:
                continue
                
            # 检查依赖是否满足
            prerequisite_steps = getattr(step, 'prerequisite_steps', [])
            if prerequisite_steps:
                # 检查所有依赖步骤是否已完成
                dependencies_satisfied = all(
                    dep_order in completed_step_orders 
                    for dep_order in prerequisite_steps
                )
                if not dependencies_satisfied:
                    continue
            
            # 找到可执行的步骤
            step_order = getattr(step, 'step_order', 0)
            return step, step_order
            
        return None, None
    
    async def _prepare_tools(self, config: RunnableConfig):
        """准备工具"""
        try:
            default_tools = self.agent.get_default_tools()
            
            # 对于coder代理，只使用默认工具（python_repl_tool），不加载MCP工具
            if self.agent.agent_type == "coder":
                return default_tools
            
            # 其他代理正常加载MCP工具
            mcp_tools = await setup_mcp_tools(self.agent.agent_type, config)
            return default_tools + mcp_tools
            
        except Exception as e:
            self.logger.error(f"Error preparing tools: {e}")
            import traceback
            self.logger.error(f"Tools preparation traceback:\n{traceback.format_exc()}")
            
            # 如果 MCP 工具失败，至少返回默认工具
            try:
                default_tools = self.agent.get_default_tools()
                return default_tools
            except Exception as fallback_error:
                self.logger.error(f"Even default tools failed: {fallback_error}")
                import traceback
                self.logger.error(f"Fallback traceback:\n{traceback.format_exc()}")
                return []
    
    def _prepare_input(self, state: ReasoningState, current_step):
        """准备执行输入"""
        context_builder = StepContextBuilder(state, current_step)
        input_data = context_builder.build_input(self.agent.agent_type)

        # 添加必要的状态字段
        input_data["request_id"] = state.get("request_id", "")
        input_data["sop_name"] = state.get("sop_id", "")

        return input_data


class StepContextBuilder:
    """步骤上下文构建器 - 负责构建清晰的执行上下文"""
    
    def __init__(self, state: ReasoningState, current_step):
        self.state = state
        self.current_step = current_step
        self.current_plan = state.get("current_plan")
    
    def build_input(self, agent_type: str) -> dict:
        """构建agent输入"""
        messages = []
        
        # 添加历史步骤上下文（按每个已完成步骤拆分为 user/ai 对）
        history_messages = self._build_history_messages()
        if history_messages:
            messages.extend(history_messages)
        
        # 添加当前任务
        current_task = self._build_current_task()
        messages.append(HumanMessage(content=current_task))
        
        # 添加特定agent的指令
        # agent_instruction = self._build_agent_instruction(agent_type)
        # if agent_instruction:
        #     messages.append(HumanMessage(content=agent_instruction, name="system"))
        
        return {
            "messages": messages
        }
    
    def _build_history_messages(self):
        """构建历史步骤消息对（User/AI）"""
        completed_steps = [
            step for step in self.current_plan.steps 
            if step.execution_res and step != self.current_step
        ]
        
        if not completed_steps:
            return []
        
        messages = []
        for step in completed_steps:
            # User message: title + description
            user_content = f"{step.title}\n\n{step.description}"
            messages.append(HumanMessage(content=user_content))
            
            # AI message: execution result
            ai_content = step.execution_res
            messages.append(AIMessage(content=ai_content))
        
        return messages
    
    def _build_current_task(self) -> str:
        """构建当前任务描述"""
        return f"""# 当前任务

## 任务
{self.current_step.title}

## 任务详情  
{self.current_step.description}
"""
    
    def _build_agent_instruction(self, agent_type: str) -> Optional[str]:
        """构建特定agent的指令"""
        instructions = {
            "researcher": """**重要提示（必须严格遵守）:**
- 当工具返回空数据时（如 {"data": []}, {"content": ""}, null 等），必须明确说明"未查询到相关数据"
- 严禁基于空数据编造任何具体信息、数值、状态或配置
- 必须准确引用工具的原始返回内容，不得添加不存在的信息
- 空数据本身就是重要的诊断信息，应如实报告
- 所有结论必须基于工具的实际输出，标注具体来源
- 请用中文回答，保持客观准确""",
            
            "coder": """**代码分析要求:**
- 仔细分析代码逻辑和潜在问题
- 提供具体的修复建议
- 如需执行代码，请确保安全性
- 基于实际代码内容进行分析，不得编造"""
        }
        
        return instructions.get(agent_type)