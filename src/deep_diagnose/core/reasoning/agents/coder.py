"""
编码员Agent - 负责执行代码分析任务
"""

from typing import Literal, Optional
from langchain_core.runnables import RunnableConfig
from langgraph.types import Command

from .base import SubAgent
from .step_executor import StepExecutor
from deep_diagnose.core.reasoning.workflow.types import ReasoningState


class CoderAgent(SubAgent):
    """编码员Agent - 专门处理代码分析和处理任务"""
    
    def __init__(self):
        super().__init__("coder", "coder")
        self.step_executor = StepExecutor(self)
    
    def get_default_tools(self):
        """获取编码员的默认工具"""
        from deep_diagnose.tools import python_repl_tool
        return [python_repl_tool]
    
    async def _do_execute(self, state: ReasoningState, config: Optional[RunnableConfig] = None) -> Command:
        """执行代码分析任务"""
        
        # 验证是否有代码处理步骤
        if not self._has_processing_steps(state):
            self.logger.info("No processing steps found")
            return Command(goto="research_team")
        
        # 执行当前步骤
        return await self.step_executor.execute_current_step(state, config)
    
    def _handle_error(self, error: Exception, state: ReasoningState) -> Command:
        """编码员特定的错误处理"""
        self.logger.error(f"Code processing failed: {error}")
        # 代码处理失败时返回研究团队
        return Command(goto="research_team")
    
    def _has_processing_steps(self, state: ReasoningState) -> bool:
        """检查是否有代码处理步骤"""
        current_plan = state.get("current_plan")
        if not current_plan or not hasattr(current_plan, 'steps'):
            return False
        
        # 检查是否有未完成的处理步骤
        from deep_diagnose.prompts.planner_model import StepType
        unfinished_processing_steps = [
            step for step in current_plan.steps 
            if not step.execution_res and step.step_type == StepType.PROCESSING
        ]
        
        return len(unfinished_processing_steps) > 0


# 创建实例和节点函数
_coder = CoderAgent()

async def coder_node(state: ReasoningState, config: RunnableConfig) -> Command[Literal["research_team"]]:
    """编码员节点函数"""
    return await _coder.execute(state, config)