"""
背景调查Agent - 负责搜索和收集背景信息
"""

import json
from typing import Literal, Optional
from langchain_core.runnables import RunnableConfig
from langgraph.types import Command

from .base import SubAgent
from deep_diagnose.core.reasoning.workflow.types import ReasoningState
from deep_diagnose.common.config.core.configuration import Configuration
from deep_diagnose.tools import get_web_search_tool


class BackgroundInvestigatorAgent(SubAgent):
    """背景调查Agent"""
    
    def __init__(self):
        super().__init__("background_investigator", "background_investigator")
    
    async def _do_execute(self, state: ReasoningState, config: Optional[RunnableConfig] = None) -> Command:
        configurable = Configuration.from_runnable_config(config)
        query = state["messages"][-1].content
        
        # 执行搜索
        search_results = await get_web_search_tool(configurable.max_search_results).ainvoke(query)
        
        return Command(
            update={"background_investigation_results": json.dumps(search_results, ensure_ascii=False)},
            goto="planner",
        )


# 创建实例和节点函数
_background_investigator = BackgroundInvestigatorAgent()

async def background_investigation_node(state: ReasoningState, config: RunnableConfig) -> Command[Literal["planner"]]:
    return await _background_investigator.execute(state, config)