"""
规划器Agent - 负责生成和管理诊断计划
"""

import json
from typing import Literal, Optional
from langchain_core.messages import AIMessage
from langchain_core.runnables import RunnableConfig
from langgraph.types import Command

from .base import SubAgent
from deep_diagnose.core.reasoning.workflow.types import ReasoningState
from deep_diagnose.common.config.constants.agents import AGENT_LLM_MAP
from deep_diagnose.common.config.core.configuration import Configuration
from deep_diagnose.llms.llm import get_llm_by_type
from deep_diagnose.prompts.template import apply_prompt_template
from deep_diagnose.prompts.planner_model import Plan
from deep_diagnose.common.utils.json_utils import repair_json_output
from deep_diagnose.tools.mcp.mcp_tool_manager import MCPToolManager
from deep_diagnose.core.reasoning.planning import create_sop_service


class PlannerAgent(SubAgent):
    """规划器Agent"""
    
    def __init__(self, config_obj: Configuration):
        super().__init__("planner", "planner")
        # Initialize SOPService
        self.sop_service = create_sop_service()
    
    async def _do_execute(self, state: ReasoningState, config: Optional[RunnableConfig] = None) -> Command:
        configurable = Configuration.from_runnable_config(config)
        plan_iterations = state.get("plan_iterations", 0)
        
        # 检查是否超过最大迭代次数
        if plan_iterations >= configurable.max_plan_iterations:
            self.logger.info("Max plan iterations reached, going to reporter")
            return Command(goto="reporter")
        
        # 准备状态和SOP
        await self._prepare_state(state, configurable)
        
        # 生成计划
        plan_response = await self._generate_plan(state, configurable)
        
        # 处理计划响应
        return self._process_response(plan_response, plan_iterations, state)
    
    async def _prepare_state(self, state: ReasoningState, configurable: Configuration):
        # 注入SOP
        user_query = self._extract_user_query(state)
        selected_sop_result = await self.sop_service.find_sop(user_query)
        
        if selected_sop_result.success and selected_sop_result.sop:
            self.logger.info(f"SOP selection successful: {selected_sop_result.reason} (SOP: {selected_sop_result.sop.name})")
            # Inject state - using planner.md expected state keys
            state["operation_sop_content"] = selected_sop_result.sop.content
            state["sop_id"] = selected_sop_result.sop.sop_id
            state["sop_reason"] = selected_sop_result.reason
        else:
            self.logger.warning(f"SOP selection failed: {selected_sop_result.error_message}")
            # Fallback to empty SOP state
            state["operation_sop_content"] = ""
            state["sop_id"] = ""
            state["sop_reason"] = selected_sop_result.error_message
        
        # 根据SOP获取工具列表并添加MCP服务器描述
        tool_names = None
        sop_id = state.get("sop_id", "")
        
        if selected_sop_result.success and selected_sop_result.sop:
            tool_names = selected_sop_result.sop.tool_dependencies
        
        mcp_tool_manager = MCPToolManager()
        state["mcp_servers_description"] = await mcp_tool_manager.get_enabled_mcp_tools_description(tool_names)
        
        if tool_names:
            self.logger.info(f"Filtered MCP tools by SOP '{sop_id}': {tool_names}")
        else:
            self.logger.info("No SOP tools filter applied, using all MCP tools")
    
    def _extract_user_query(self, state: ReasoningState) -> str:
        if not state.get("messages"):
            return ""
        for msg in reversed(state["messages"]):
            if hasattr(msg, 'content') and msg.content:
                return msg.content
        return ""
    
    async def _generate_plan(self, state: ReasoningState, configurable: Configuration) -> str:
        messages = apply_prompt_template("agent_planner", state, configurable)
        
        # 添加背景调查结果（如果有）
        if (state.get("plan_iterations", 0) == 0 and 
            state.get("enable_background_investigation") and 
            state.get("background_investigation_results")):
            messages.append({
                "role": "user",
                "content": f"background investigation results of user query:\n{state['background_investigation_results']}\n"
            })
        
        llm = get_llm_by_type(AGENT_LLM_MAP["planner"])
        
        if AGENT_LLM_MAP["planner"] == "basic":
            structured_llm = llm.with_structured_output(Plan, method="json_mode")
            response = await structured_llm.ainvoke(messages)
            return response.model_dump_json(indent=4, exclude_none=True)
        else:
            # 使用异步流式调用
            response = llm.astream(messages)
            full_response = ""
            async for chunk in response:
                full_response += chunk.content
            return full_response
    
    def _process_response(self, plan_response: str, plan_iterations: int, state: ReasoningState) -> Command:
        self.logger.info(f"Plan response generated: {len(plan_response)} characters")
        
        try:
            plan_data = json.loads(repair_json_output(plan_response))
            
            if plan_data.get("has_enough_context"):
                self.logger.info("Plan has enough context, going to reporter")
                return Command(
                    update={
                        "messages": [AIMessage(content=plan_response, name="planner")],
                        "current_plan": Plan.model_validate(plan_data),
                        "operation_sop_content": state.get("operation_sop_content", ""),
                        "sop_id": state.get("sop_id", ""),
                        "mcp_servers_description": state.get("mcp_servers_description", ""),
                    },
                    goto="reporter",
                )
            
            return Command(
                update={
                    "messages": [AIMessage(content=plan_response, name="planner")],
                    "current_plan": plan_response,
                    "operation_sop_content": state.get("operation_sop_content", ""),
                    "sop_id": state.get("sop_id", ""),
                    "mcp_servers_description": state.get("mcp_servers_description", ""),
                },
                goto="human_feedback",
            )
            
        except json.JSONDecodeError:
            self.logger.warning("Invalid JSON in plan response")
            goto = "reporter" if plan_iterations > 0 else "__end__"
            return Command(goto=goto)


async def planner_node(state: ReasoningState, config: RunnableConfig) -> Command[Literal["human_feedback", "reporter"]]:
    configurable = Configuration.from_runnable_config(config)
    _planner = PlannerAgent(configurable)
    return await _planner.execute(state, config)
