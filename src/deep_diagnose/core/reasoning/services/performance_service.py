#!/usr/bin/env python3
"""
性能数据服务 V3 - 简化和稳健性修复
"""

import json
import logging
import asyncio
import yaml
import re
from typing import Optional, Dict, Any, List
from pathlib import Path
from jinja2 import Template

logger = logging.getLogger(__name__)


class PerformanceServiceError(Exception):
    """性能服务异常"""
    pass


class PerformanceConfig:
    """性能配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        self.config_path = Path(config_path) if config_path else Path(__file__).parent / "config" / "performance_metrics.yaml"
        self._config_cache = None
    
    def load_config(self, scenario: str = "vm_metrics") -> Dict[str, Any]:
        if self._config_cache is None:
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self._config_cache = yaml.safe_load(f)
            except Exception as e:
                raise PerformanceServiceError(f"加载配置文件失败 {self.config_path}: {e}")
        return self._config_cache.get(scenario, {})


class DataLoader:
    """数据加载器 - 合并一个实例的所有时间范围数据"""
    
    def __init__(self, base_dir: str = "./vm_data_output"):
        self.base_dir = Path(base_dir)
    
    async def load_performance_data(self, request_id: str) -> Optional[Dict[str, Any]]:
        try:
            return await asyncio.get_event_loop().run_in_executor(
                None, self._load_data_sync, request_id
            )
        except Exception as e:
            logger.error(f"加载数据失败: {e}", exc_info=True)
            return None

    def _load_data_sync(self, request_id: str) -> Optional[Dict[str, Any]]:
        import csv
        data_dir = self.base_dir / "file_system" / request_id
        csv_path = data_dir / "file_resource.csv"
        if not csv_path.exists():
            logger.warning(f"清单文件不存在: {csv_path}")
            return None

        structured_data = {}
        instance_id_re = re.compile(r'(i-[a-zA-Z0-9]+)')

        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                file_purpose = row.get('file_purpose', '').lower()
                if 'vm性能数据' not in file_purpose and 'performance' not in file_purpose:
                    continue

                source_for_id = row.get('file_title') or row.get('file_name', '')
                match = instance_id_re.search(source_for_id)
                if not match:
                    continue
                
                instance_id = match.group(1)
                file_name = row.get('file_name')
                file_path = data_dir / file_name

                if file_path.exists():
                    try:
                        with open(file_path, 'r', encoding='utf-8') as data_f:
                            performance_data = json.load(data_f)
                            if not isinstance(performance_data, dict):
                                continue

                            if instance_id not in structured_data:
                                structured_data[instance_id] = {}
                            
                            for metric_name, data_points in performance_data.items():
                                if isinstance(data_points, list):
                                    if metric_name not in structured_data[instance_id]:
                                        structured_data[instance_id][metric_name] = []
                                    structured_data[instance_id][metric_name].extend(data_points)

                    except Exception as e:
                        logger.warning(f"加载或合并JSON失败 {file_path}: {e}")

        for instance_id, metrics in structured_data.items():
            for metric_name, data_points in metrics.items():
                try:
                    # 去重并排序
                    unique_points = {p['datetime']: p for p in data_points}.values()
                    structured_data[instance_id][metric_name] = sorted(unique_points, key=lambda p: p['datetime'])
                except (KeyError, TypeError):
                    logger.warning(f"无法为指标 {metric_name} 的数据点排序或去重")

        return structured_data if structured_data else None

class MetricsAnalyzer:
    """指标分析器 - 简化版"""
    
    def analyze_metrics(self, data: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        analysis = {}
        for instance_id, metrics_data in data.items():
            analysis[instance_id] = self._analyze_metric_set(metrics_data, config)
        return analysis

    def _analyze_metric_set(self, metrics_data: Dict[str, Any], config: Dict[str, Any]) -> Dict[str, Any]:
        metrics_summary = {}
        for metric_name, data_points in metrics_data.items():
            if not isinstance(data_points, list) or not data_points:
                continue
            try:
                values = [p['value'] for p in data_points if 'value' in p]
                if values:
                    metrics_summary[metric_name] = {
                        'count': len(values),
                        'min': min(values),
                        'max': max(values),
                        'avg': sum(values) / len(values),
                        'latest': values[-1],
                        'trend': self._calculate_trend(values),
                        'status': self._get_status(metric_name, values[-1], config)
                    }
            except Exception as e:
                logger.warning(f"分析指标失败 {metric_name}: {e}")
        return metrics_summary

    def _calculate_trend(self, values: List[float]) -> str:
        if len(values) < 2: return "stable"
        recent = values[-min(5, len(values)):]
        if len(recent) < 2: return "stable"
        slope = (recent[-1] - recent[0]) / len(recent) if len(recent) > 1 else 0
        avg_val = sum(recent) / len(recent)
        threshold = avg_val * 0.1 if avg_val != 0 else 0.1
        if slope > threshold: return "increasing"
        elif slope < -threshold: return "decreasing"
        else: return "stable"

    def _get_status(self, metric_name: str, value: float, config: Dict[str, Any]) -> Dict[str, str]:
        thresholds = config.get('thresholds', {}).get(metric_name, {})
        if not thresholds: return {'level': 'normal', 'text': '正常'}
        critical = thresholds.get('critical')
        warning = thresholds.get('warning')
        if critical is not None and warning is not None:
            if critical > warning:
                if value >= critical: return {'level': 'critical', 'text': '严重'}
                elif value >= warning: return {'level': 'warning', 'text': '警告'}
            else:
                if value <= critical: return {'level': 'critical', 'text': '严重'}
                elif value <= warning: return {'level': 'warning', 'text': '警告'}
        return {'level': 'normal', 'text': '正常'}

class DynamicHtmlGenerator:
    """动态HTML生成器 - 支持增强版模板"""
    
    def __init__(self, template_path: Optional[str] = None, use_enhanced: bool = True):
        if template_path:
            self.template_path = Path(template_path)
        else:
            template_name = "performance_report_enhanced.html" if use_enhanced else "performance_report.html"
            self.template_path = Path(__file__).parent / "templates" / template_name
        self.template = self._load_template(self.template_path)

    def _load_template(self, template_path: Path) -> Template:
        if not template_path.exists(): raise PerformanceServiceError(f"模板不存在: {template_path}")
        try:
            with open(template_path, 'r', encoding='utf-8') as f: return Template(f.read())
        except Exception as e:
            raise PerformanceServiceError(f"加载模板失败 {template_path}: {e}")

    def generate_html(self, data: Dict[str, Any], analysis: Dict[str, Any], config: Dict[str, Any]) -> str:
        """生成动态HTML"""
        try:
            template_data = {
                'title': config.get('title', '性能分析报告'),
                'description': config.get('description', '基于监控数据的性能分析'),
                'report_config': config.get('report', {}),
                'metrics_config': config.get('metrics', {}),
                'charts_config': config.get('charts', {}),
                'analysis_data': analysis,
                'raw_data': data  # Pass raw data for charts
            }
            return self.template.render(**template_data)
        except Exception as e:
            logger.error(f"生成HTML失败: {e}", exc_info=True)
            return f"<h1>报告生成失败</h1><p>{e}</p>"

class PerformanceService:
    """性能数据服务"""
    
    def __init__(self, base_dir: str = "./vm_data_output", config_path: Optional[str] = None, scenario: str = "vm_metrics", use_enhanced_ui: bool = True):
        self.config_loader = PerformanceConfig(config_path)
        self.data_loader = DataLoader(base_dir)
        self.analyzer = MetricsAnalyzer()
        self.html_generator = DynamicHtmlGenerator(use_enhanced=use_enhanced_ui)
        self.scenario = scenario
        self.scenario_config = self.config_loader.load_config(scenario)

    def _filter_charts_with_data(self, data: Dict[str, Any], charts_config: Dict[str, Any]) -> Dict[str, Any]:
        """过滤掉没有数据的图表配置，并按display_order排序"""
        filtered_charts = {}
        
        for chart_id, chart_config in charts_config.items():
            chart_has_data = False
            metrics_in_chart = chart_config.get('metrics', [])
            
            # 检查是否有任何实例包含该图表所需的指标数据
            for instance_id, instance_data in data.items():
                for metric_key in metrics_in_chart:
                    if (metric_key in instance_data and 
                        isinstance(instance_data[metric_key], list) and 
                        len(instance_data[metric_key]) > 0):
                        # 进一步检查数据点是否有效
                        valid_data_points = [
                            point for point in instance_data[metric_key] 
                            if isinstance(point, dict) and 'value' in point and 'datetime' in point
                        ]
                        if valid_data_points:
                            chart_has_data = True
                            break
                
                if chart_has_data:
                    break
            
            # 只有当图表有数据时才包含在配置中
            if chart_has_data:
                filtered_charts[chart_id] = chart_config
                logger.debug(f"图表 {chart_id} 有数据，将包含在报告中")
            else:
                logger.info(f"图表 {chart_id} 没有数据，已从报告中过滤掉")
        
        # 按display_order排序图表
        sorted_charts = dict(sorted(
            filtered_charts.items(), 
            key=lambda item: item[1].get('display_order', 999)
        ))
        
        return sorted_charts

    async def generate_report(self, request_id: str) -> Optional[str]:
        try:
            data = await self.data_loader.load_performance_data(request_id)
            if not data:
                logger.info(f"未找到性能数据: {request_id}")
                return None

            analysis = self.analyzer.analyze_metrics(data, self.scenario_config)
            if not analysis:
                logger.warning(f"数据分析结果为空: {request_id}")
                return None

            # 过滤掉没有数据的图表配置
            original_charts = self.scenario_config.get('charts', {})
            filtered_charts = self._filter_charts_with_data(data, original_charts)
            
            # 创建过滤后的配置
            filtered_config = self.scenario_config.copy()
            filtered_config['charts'] = filtered_charts
            
            # 如果没有任何图表有数据，记录警告但仍生成报告（可能有其他内容）
            if not filtered_charts:
                logger.warning(f"所有图表都没有数据: {request_id}")
            else:
                logger.info(f"过滤后包含 {len(filtered_charts)} 个有数据的图表: {request_id}")

            html_report = self.html_generator.generate_html(data, analysis, filtered_config)
            logger.info(f"成功生成性能报告: {request_id}")
            return html_report

        except Exception as e:
            logger.error(f"生成性能报告失败 {request_id}: {e}", exc_info=True)
            return None

# 工厂函数
def create_vm_performance_service(base_dir: str = "./vm_data_output", use_enhanced_ui: bool = True) -> PerformanceService:
    return PerformanceService(base_dir=base_dir, scenario="vm_metrics", use_enhanced_ui=use_enhanced_ui)
