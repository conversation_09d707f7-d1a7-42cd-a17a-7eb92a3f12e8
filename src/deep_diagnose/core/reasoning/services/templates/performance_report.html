<!-- 性能报告HTML模板 V5.0 - 优化时间序列显示和格式一致性 -->
<div class="w-full bg-gray-800 rounded-lg p-6 mb-8">
    <h2 class="text-2xl font-bold text-blue-400 border-b-2 border-blue-500 pb-2 mb-6">{{ title }}</h2>
    <p class="text-xl text-gray-100 leading-relaxed mb-6">{{ description }}</p>

    {% for instance_id, instance_analysis in analysis_data.items() %}
    <div class="w-full instance-section mb-12 bg-gray-700/50 rounded-lg p-6">
        <h3 class="text-2xl font-bold text-blue-300 mb-6 border-b-2 border-blue-500 pb-2">实例: {{ instance_id }}</h3>

        {% set metrics_cards = {} %}
        {% if instance_analysis %}
            {% for metric_key, stats in instance_analysis.items() %}
                {% if metric_key in metrics_config %}
                    {% set metric_config = metrics_config[metric_key] %}
                    {% set _ = metrics_cards.update({metric_key: {
                        'name': metric_config.get('name', metric_key),
                        'unit': metric_config.get('unit', ''),
                        'latest': stats.latest,
                        'avg': stats.avg
                    }}) %}
                {% endif %}
            {% endfor %}
        {% endif %}

        <!-- 指标卡片 -->
        {% if report_config.get('show_summary_cards') and metrics_cards %}
        <div class="w-full bg-gray-800/50 rounded-lg p-6 mb-8">
            <h4 class="text-xl font-bold text-blue-300 mb-4 border-b border-blue-400 pb-2">📊 性能指标总览</h4>
            <div class="w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for metric_key, metric_data in metrics_cards.items() %}
                <div class="bg-gray-700/70 rounded-lg p-5 border border-gray-600 hover:border-blue-400 transition-colors">
                    <h5 class="text-sm font-medium text-gray-300 mb-2">{{ metric_data.name }}</h5>
                    <div class="text-3xl font-bold text-blue-400 mb-1">{{ "%.2f"|format(metric_data.latest) }}{{ metric_data.unit }}</div>
                    <div class="text-sm text-gray-400">平均值: {{ "%.2f"|format(metric_data.avg) }}{{ metric_data.unit }}</div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- 图表 -->
        {% if report_config.get('show_charts') and charts_config %}
        <div class="w-full bg-gray-800/50 rounded-lg p-6 mb-8">
            <h4 class="text-xl font-bold text-blue-300 mb-6 border-b border-blue-400 pb-2">📈 性能趋势图表</h4>
            <div class="w-full grid grid-cols-1 lg:grid-cols-2 gap-8">
                {% for chart_id, chart_config in charts_config.items() %}
                <div class="w-full bg-gray-700/70 rounded-lg p-6 border border-gray-600">
                    <h5 class="text-lg font-semibold text-blue-200 mb-4 flex items-center">
                        <span class="w-3 h-3 bg-blue-400 rounded-full mr-3"></span>
                        {{ chart_config.title }}
                    </h5>
                    <div class="w-full chart-container bg-gray-900/50 rounded-lg p-4" style="height: {{ report_config.get('chart_height', '350px') }};">
                        <canvas id="{{ chart_id }}_{{ instance_id }}" class="w-full h-full"></canvas>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

    </div>
    {% endfor %}
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const rawData = {{ raw_data|tojson }};
    const metricsConfig = {{ metrics_config|tojson }};
    const chartsConfig = {{ charts_config|tojson }};

    function parseDateTime(dateTimeStr) {
        return new Date(dateTimeStr.replace(' ', 'T'));
    }

    function getColor(colorName, alpha = 1) {
        const colors = {
            blue: `rgba(96, 165, 250, ${alpha})`,      // 更亮的蓝色
            green: `rgba(52, 211, 153, ${alpha})`,     // 更亮的绿色
            purple: `rgba(168, 85, 247, ${alpha})`,    // 更亮的紫色
            pink: `rgba(244, 114, 182, ${alpha})`,     // 更亮的粉色
            indigo: `rgba(129, 140, 248, ${alpha})`,   // 更亮的靛蓝
            cyan: `rgba(34, 211, 238, ${alpha})`,      // 更亮的青色
            orange: `rgba(251, 146, 60, ${alpha})`,    // 更亮的橙色
            red: `rgba(248, 113, 113, ${alpha})`,      // 更亮的红色
            yellow: `rgba(250, 204, 21, ${alpha})`,    // 更亮的黄色
            teal: `rgba(45, 212, 191, ${alpha})`,      // 青绿色
            lime: `rgba(163, 230, 53, ${alpha})`,      // 酸橙色
            emerald: `rgba(52, 211, 153, ${alpha})`    // 翡翠绿
        };
        return colors[colorName] || colors.blue;
    }

    function createChart(canvasId, datasets, yLabel, chartType) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return;

        new Chart(ctx.getContext('2d'), {
            type: chartType || 'line',
            data: { datasets },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        labels: {
                            color: '#E5E7EB',
                            font: {
                                size: 12,
                                weight: '500'
                            },
                            usePointStyle: true,
                            pointStyle: 'circle',
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(17, 24, 39, 0.95)',
                        titleColor: '#F3F4F6',
                        bodyColor: '#E5E7EB',
                        borderColor: '#374151',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: true,
                        callbacks: {
                            title: function(context) {
                                const timestamp = context[0].parsed.x;
                                return new Date(timestamp).toLocaleString('zh-CN', {
                                    year: 'numeric',
                                    month: '2-digit',
                                    day: '2-digit',
                                    hour: '2-digit',
                                    minute: '2-digit',
                                    second: '2-digit'
                                });
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        type: 'time',
                        time: {
                            displayFormats: {
                                minute: 'HH:mm',
                                hour: 'HH:mm',
                                day: 'MM-dd HH:mm'
                            },
                            tooltipFormat: 'yyyy-MM-dd HH:mm:ss'
                        },
                        title: {
                            display: true,
                            text: '时间',
                            color: '#9CA3AF',
                            font: {
                                size: 12,
                                weight: '500'
                            }
                        },
                        ticks: {
                            color: '#9CA3AF',
                            font: {
                                size: 11
                            },
                            maxTicksLimit: 8
                        },
                        grid: {
                            color: 'rgba(75, 85, 99, 0.3)',
                            drawBorder: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: yLabel,
                            color: '#9CA3AF',
                            font: {
                                size: 12,
                                weight: '500'
                            }
                        },
                        ticks: {
                            color: '#9CA3AF',
                            font: {
                                size: 11
                            }
                        },
                        grid: {
                            color: 'rgba(75, 85, 99, 0.3)',
                            drawBorder: false
                        }
                    }
                }
            }
        });
    }

    for (const [instance_id, instance_data] of Object.entries(rawData)) {
        for (const [chart_id, chart_config] of Object.entries(chartsConfig)) {
            const canvasId = `${chart_id}_${instance_id}`;
            const datasets = [];

            chart_config.metrics.forEach((metricKey, index) => {
                if (instance_data[metricKey] && Array.isArray(instance_data[metricKey])) {
                    const metricInfo = metricsConfig[metricKey] || {};
                    const color = metricInfo.color || 'blue';
                    datasets.push({
                        label: metricInfo.name || metricKey,
                        data: instance_data[metricKey].map(d => ({
                            x: parseDateTime(d.datetime),
                            y: d.value
                        })),
                        borderColor: getColor(color, 0.9),
                        backgroundColor: getColor(color, 0.1),
                        pointBackgroundColor: getColor(color, 1),
                        pointBorderColor: '#FFFFFF',
                        pointBorderWidth: 2,
                        pointRadius: 3,
                        pointHoverRadius: 6,
                        pointHoverBackgroundColor: getColor(color, 1),
                        pointHoverBorderColor: '#FFFFFF',
                        pointHoverBorderWidth: 2,
                        borderWidth: 2.5,
                        fill: true,
                        tension: 0.3,
                        cubicInterpolationMode: 'monotone'
                    });
                }
            });

            if (datasets.length > 0) {
                createChart(canvasId, datasets, chart_config.y_axis_label || 'Value', chart_config.chart_type);
            } else {
                const canvas = document.getElementById(canvasId);
                if(canvas) canvas.parentElement.innerHTML = `<div class="flex items-center justify-center h-full text-gray-400">No data for this chart</div>`;
            }
        }
    }
});
</script>