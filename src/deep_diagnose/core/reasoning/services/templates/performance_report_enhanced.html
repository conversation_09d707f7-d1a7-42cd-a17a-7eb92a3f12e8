<!-- 增强版性能报告HTML模板 - 使用Plotly.js实现高交互性和美观度 -->
<div class="w-full bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl p-8 mb-8 shadow-2xl">
    <div class="flex items-center mb-6">
        <div class="w-1 h-8 bg-gradient-to-b from-blue-400 to-purple-500 rounded-full mr-4"></div>
        <h2 class="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">{{ title }}</h2>
    </div>
    <p class="text-xl text-gray-200 leading-relaxed mb-8 pl-5">{{ description }}</p>

    {% for instance_id, instance_analysis in analysis_data.items() %}
    <div class="w-full instance-section mb-12 bg-gradient-to-br from-gray-800/80 to-gray-700/60 rounded-xl p-8 backdrop-blur-sm border border-gray-600/30 shadow-xl">
        <div class="flex items-center mb-8">
            <div class="w-3 h-3 bg-gradient-to-r from-emerald-400 to-blue-500 rounded-full mr-4 animate-pulse"></div>
            <h3 class="text-2xl font-bold bg-gradient-to-r from-emerald-400 to-blue-400 bg-clip-text text-transparent">实例: {{ instance_id }}</h3>
        </div>

        {% set metrics_cards = {} %}
        {% set metrics_order = {} %}
        {% if instance_analysis %}
            <!-- 首先收集所有指标及其显示顺序 -->
            {% for chart_id, chart_config in charts_config.items() %}
                {% set display_order = chart_config.get('display_order', 999) %}
                {% for metric_key in chart_config.get('metrics', []) %}
                    {% if metric_key in instance_analysis and metric_key in metrics_config %}
                        {% set metric_config = metrics_config[metric_key] %}
                        {% set stats = instance_analysis[metric_key] %}
                        {% set _ = metrics_cards.update({metric_key: {
                            'name': metric_config.get('name', metric_key),
                            'unit': metric_config.get('unit', ''),
                            'latest': stats.latest,
                            'avg': stats.avg,
                            'trend': stats.trend,
                            'status': stats.status,
                            'category': metric_config.get('category', 'other'),
                            'display_order': display_order
                        }}) %}
                        {% set _ = metrics_order.update({metric_key: display_order}) %}
                    {% endif %}
                {% endfor %}
            {% endfor %}
            
            <!-- 处理不在图表中但存在于分析结果中的指标 -->
            {% for metric_key, stats in instance_analysis.items() %}
                {% if metric_key not in metrics_cards and metric_key in metrics_config %}
                    {% set metric_config = metrics_config[metric_key] %}
                    {% set _ = metrics_cards.update({metric_key: {
                        'name': metric_config.get('name', metric_key),
                        'unit': metric_config.get('unit', ''),
                        'latest': stats.latest,
                        'avg': stats.avg,
                        'trend': stats.trend,
                        'status': stats.status,
                        'category': metric_config.get('category', 'other'),
                        'display_order': 999
                    }}) %}
                    {% set _ = metrics_order.update({metric_key: 999}) %}
                {% endif %}
            {% endfor %}
        {% endif %}

        <!-- 增强版指标卡片 -->
        {% if report_config.get('show_summary_cards') and metrics_cards %}
        <div class="w-full bg-gradient-to-br from-gray-900/60 to-gray-800/40 rounded-xl p-8 mb-10 border border-gray-600/20 backdrop-blur-sm">
            <div class="flex items-center mb-6">
                <div class="w-2 h-2 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full mr-3"></div>
                <h4 class="text-xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">📊 性能指标总览</h4>
            </div>
            <div class="w-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {% set sorted_metrics = metrics_cards.items() | list %}
                {% set sorted_metrics = sorted_metrics | sort(attribute='1.display_order') %}
                {% for metric_key, metric_data in sorted_metrics %}
                <div class="group bg-gradient-to-br from-gray-800/70 to-gray-700/50 rounded-xl p-6 border border-gray-600/30 hover:border-blue-400/50 transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/10 hover:scale-105">
                    <div class="flex items-center justify-between mb-3">
                        <h5 class="text-sm font-medium text-gray-300 group-hover:text-gray-200 transition-colors">{{ metric_data.name }}</h5>
                        {% if metric_data.trend == 'increasing' %}
                            <span class="text-red-400">📈</span>
                        {% elif metric_data.trend == 'decreasing' %}
                            <span class="text-green-400">📉</span>
                        {% else %}
                            <span class="text-blue-400">➡️</span>
                        {% endif %}
                    </div>
                    <div class="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-2">
                        {{ "%.2f"|format(metric_data.latest) }}{{ metric_data.unit }}
                    </div>
                    <div class="text-sm text-gray-400 mb-2">平均值: {{ "%.2f"|format(metric_data.avg) }}{{ metric_data.unit }}</div>
                    {% if metric_data.status %}
                    <div class="flex items-center">
                        <div class="w-2 h-2 rounded-full mr-2 
                            {% if metric_data.status.level == 'critical' %}bg-red-500
                            {% elif metric_data.status.level == 'warning' %}bg-yellow-500
                            {% else %}bg-green-500{% endif %}"></div>
                        <span class="text-xs 
                            {% if metric_data.status.level == 'critical' %}text-red-400
                            {% elif metric_data.status.level == 'warning' %}text-yellow-400
                            {% else %}text-green-400{% endif %}">{{ metric_data.status.text }}</span>
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- 增强版图表区域 -->
        {% if report_config.get('show_charts') and charts_config %}
        <div class="w-full bg-gradient-to-br from-gray-900/60 to-gray-800/40 rounded-xl p-8 mb-10 border border-gray-600/20 backdrop-blur-sm">
            <div class="flex items-center justify-between mb-8">
                <div class="flex items-center">
                    <div class="w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full mr-3"></div>
                    <h4 class="text-xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">📈 交互式性能趋势图表</h4>
                </div>
                <div class="text-sm text-gray-400">💡 支持缩放、平移、图例交互</div>
            </div>
            
            <!-- 图表控制面板 -->
            <div class="mb-6 p-4 bg-gray-800/50 rounded-lg border border-gray-600/30">
                <div class="flex flex-wrap items-center gap-4">
                    <button onclick="resetAllCharts()" class="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 text-sm font-medium">
                        🔄 重置所有图表
                    </button>
                    <button onclick="toggleDarkMode()" class="px-4 py-2 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-lg hover:from-gray-700 hover:to-gray-800 transition-all duration-200 text-sm font-medium">
                        🌙 切换主题
                    </button>
                    <div class="flex items-center gap-2">
                        <label class="text-sm text-gray-300">时间范围:</label>
                        <select id="timeRange" onchange="updateTimeRange()" class="px-3 py-1 bg-gray-700 text-gray-200 rounded border border-gray-600 text-sm">
                            <option value="all">全部时间</option>
                            <option value="1h">最近1小时</option>
                            <option value="6h">最近6小时</option>
                            <option value="24h">最近24小时</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="w-full grid grid-cols-1 xl:grid-cols-2 gap-8">
                {% for chart_id, chart_config in charts_config.items() %}
                <div class="w-full bg-gradient-to-br from-gray-800/60 to-gray-700/40 rounded-xl p-6 border border-gray-600/30 hover:border-purple-400/30 transition-all duration-300 group">
                    <div class="flex items-center justify-between mb-4">
                        <h5 class="text-lg font-semibold bg-gradient-to-r from-blue-300 to-purple-300 bg-clip-text text-transparent flex items-center">
                            <span class="w-3 h-3 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full mr-3 group-hover:animate-pulse"></span>
                            {{ chart_config.title }}
                        </h5>
                        <div class="text-xs text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity">
                            双击重置 | 滚轮缩放
                        </div>
                    </div>
                    <div class="w-full chart-container bg-gray-900/40 rounded-lg border border-gray-700/50" style="height: {{ report_config.get('chart_height', '400px') }};">
                        <div id="{{ chart_id }}_{{ instance_id }}" class="w-full h-full"></div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

    </div>
    {% endfor %}
</div>

<!-- 使用Plotly.js替代Chart.js -->
<script src="https://cdn.plot.ly/plotly-2.26.0.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const rawData = {{ raw_data|tojson }};
    const metricsConfig = {{ metrics_config|tojson }};
    const chartsConfig = {{ charts_config|tojson }};
    
    let isDarkMode = true;
    let chartInstances = {};

    // 现代化配色方案
    const colorPalette = {
        blue: '#60A5FA',
        green: '#34D399', 
        purple: '#A855F7',
        pink: '#F472B6',
        indigo: '#818CF8',
        cyan: '#22D3EE',
        orange: '#FB923C',
        red: '#F87171',
        yellow: '#FACC15',
        teal: '#2DD4BF',
        lime: '#A3E635',
        emerald: '#10B981',
        rose: '#FB7185',
        violet: '#8B5CF6',
        amber: '#F59E0B',
        sky: '#0EA5E9'
    };

    const colorKeys = Object.keys(colorPalette);

    function parseDateTime(dateTimeStr) {
        return new Date(dateTimeStr.replace(' ', 'T'));
    }

    function getColorByIndex(index) {
        return colorPalette[colorKeys[index % colorKeys.length]];
    }

    function getPlotlyTheme() {
        if (isDarkMode) {
            return {
                paper_bgcolor: 'rgba(17, 24, 39, 0.8)',
                plot_bgcolor: 'rgba(31, 41, 55, 0.6)',
                font: { color: '#E5E7EB', family: 'Inter, system-ui, sans-serif' },
                gridcolor: 'rgba(75, 85, 99, 0.3)',
                zerolinecolor: 'rgba(107, 114, 128, 0.5)'
            };
        } else {
            return {
                paper_bgcolor: 'rgba(255, 255, 255, 0.95)',
                plot_bgcolor: 'rgba(249, 250, 251, 0.8)',
                font: { color: '#374151', family: 'Inter, system-ui, sans-serif' },
                gridcolor: 'rgba(209, 213, 219, 0.5)',
                zerolinecolor: 'rgba(156, 163, 175, 0.7)'
            };
        }
    }

    function createEnhancedChart(containerId, datasets, yLabel, chartType) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const theme = getPlotlyTheme();
        
        const traces = datasets.map((dataset, index) => ({
            x: dataset.data.map(d => d.x),
            y: dataset.data.map(d => d.y),
            type: 'scatter',
            mode: 'lines+markers',
            name: dataset.label,
            line: {
                color: dataset.borderColor,
                width: 3,
                shape: 'spline',
                smoothing: 0.3
            },
            marker: {
                color: dataset.borderColor,
                size: 6,
                line: {
                    color: '#FFFFFF',
                    width: 2
                }
            },
            fill: index === 0 ? 'tozeroy' : 'tonexty',
            fillcolor: dataset.backgroundColor,
            hovertemplate: '<b>%{fullData.name}</b><br>' +
                          '时间: %{x}<br>' +
                          '数值: %{y:.2f}<br>' +
                          '<extra></extra>'
        }));

        const layout = {
            ...theme,
            title: {
                text: '',
                font: { size: 16, color: theme.font.color }
            },
            xaxis: {
                title: {
                    text: '时间',
                    font: { size: 12, color: theme.font.color }
                },
                gridcolor: theme.gridcolor,
                zerolinecolor: theme.zerolinecolor,
                tickfont: { size: 10, color: theme.font.color },
                showspikes: true,
                spikecolor: '#60A5FA',
                spikethickness: 1,
                spikedash: 'dot'
            },
            yaxis: {
                title: {
                    text: yLabel,
                    font: { size: 12, color: theme.font.color }
                },
                gridcolor: theme.gridcolor,
                zerolinecolor: theme.zerolinecolor,
                tickfont: { size: 10, color: theme.font.color },
                showspikes: true,
                spikecolor: '#60A5FA',
                spikethickness: 1,
                spikedash: 'dot'
            },
            legend: {
                orientation: 'h',
                y: -0.2,
                x: 0.5,
                xanchor: 'center',
                font: { size: 11, color: theme.font.color },
                bgcolor: 'rgba(0,0,0,0)',
                bordercolor: 'rgba(0,0,0,0)'
            },
            hovermode: 'x unified',
            margin: { l: 60, r: 30, t: 30, b: 80 },
            showlegend: true,
            dragmode: 'zoom'
        };

        const config = {
            responsive: true,
            displayModeBar: true,
            modeBarButtonsToAdd: [
                {
                    name: '重置缩放',
                    icon: {
                        'width': 1792,
                        'height': 1792,
                        'path': 'M1664 896q0 156-61 298t-164 245-245 164-298 61-298-61-245-164-164-245-61-298 61-298 164-245 245-164 298-61 298 61 245 164 164 245 61 298zm-128 0q0-130-51-248.5t-136.5-204-204-136.5-248.5-51-248.5 51-204 136.5-136.5 204-51 248.5 51 248.5 136.5 204 204 136.5 248.5 51 248.5-51 204-136.5 136.5-204 51-248.5z'
                    },
                    click: function(gd) {
                        Plotly.relayout(gd, {
                            'xaxis.autorange': true,
                            'yaxis.autorange': true
                        });
                    }
                }
            ],
            modeBarButtonsToRemove: ['lasso2d', 'select2d'],
            toImageButtonOptions: {
                format: 'png',
                filename: `performance_chart_${containerId}`,
                height: 600,
                width: 1000,
                scale: 2
            }
        };

        Plotly.newPlot(containerId, traces, layout, config);
        chartInstances[containerId] = { traces, layout, config };

        // 添加双击重置功能
        container.addEventListener('dblclick', function() {
            Plotly.relayout(containerId, {
                'xaxis.autorange': true,
                'yaxis.autorange': true
            });
        });
    }

    // 全局控制函数
    window.resetAllCharts = function() {
        Object.keys(chartInstances).forEach(chartId => {
            Plotly.relayout(chartId, {
                'xaxis.autorange': true,
                'yaxis.autorange': true
            });
        });
    };

    window.toggleDarkMode = function() {
        isDarkMode = !isDarkMode;
        Object.keys(chartInstances).forEach(chartId => {
            const theme = getPlotlyTheme();
            Plotly.relayout(chartId, theme);
        });
    };

    window.updateTimeRange = function() {
        const range = document.getElementById('timeRange').value;
        const now = new Date();
        let startTime;

        switch(range) {
            case '1h':
                startTime = new Date(now.getTime() - 60 * 60 * 1000);
                break;
            case '6h':
                startTime = new Date(now.getTime() - 6 * 60 * 60 * 1000);
                break;
            case '24h':
                startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                break;
            default:
                startTime = null;
        }

        Object.keys(chartInstances).forEach(chartId => {
            if (startTime) {
                Plotly.relayout(chartId, {
                    'xaxis.range': [startTime, now]
                });
            } else {
                Plotly.relayout(chartId, {
                    'xaxis.autorange': true
                });
            }
        });
    };

    // 调试信息
    console.log('原始数据:', rawData);
    console.log('指标配置:', metricsConfig);
    console.log('图表配置:', chartsConfig);

    // 生成图表 - 按display_order排序
    for (const [instance_id, instance_data] of Object.entries(rawData)) {
        console.log(`处理实例: ${instance_id}`, instance_data);
        
        // 按display_order排序图表配置
        const sortedCharts = Object.entries(chartsConfig).sort((a, b) => {
            const orderA = a[1].display_order || 999;
            const orderB = b[1].display_order || 999;
            return orderA - orderB;
        });
        
        for (const [chart_id, chart_config] of sortedCharts) {
            const containerId = `${chart_id}_${instance_id}`;
            const datasets = [];
            
            console.log(`处理图表: ${chart_id} (顺序: ${chart_config.display_order || 999})`, chart_config);

            chart_config.metrics.forEach((metricKey, index) => {
                if (instance_data[metricKey] && Array.isArray(instance_data[metricKey])) {
                    const metricInfo = metricsConfig[metricKey] || {};
                    const color = colorPalette[metricInfo.color] || getColorByIndex(index);
                    
                    // 确保数据格式正确
                    const validDataPoints = instance_data[metricKey].filter(d => 
                        d && typeof d === 'object' && d.datetime && (d.value !== undefined && d.value !== null)
                    );
                    
                    if (validDataPoints.length > 0) {
                        datasets.push({
                            label: metricInfo.name || metricKey,
                            data: validDataPoints.map(d => ({
                                x: parseDateTime(d.datetime),
                                y: parseFloat(d.value) || 0
                            })),
                            borderColor: color,
                            backgroundColor: color.includes('rgba') ? color : color.replace(')', ', 0.1)').replace('rgb', 'rgba')
                        });
                        
                        console.log(`处理指标 ${metricKey}: ${validDataPoints.length} 个有效数据点`);
                    } else {
                        console.warn(`指标 ${metricKey} 没有有效数据点`);
                    }
                }
            });

            console.log(`图表 ${containerId} 数据集数量: ${datasets.length}`);
            if (datasets.length > 0) {
                console.log(`创建图表 ${containerId}`, datasets);
                createEnhancedChart(containerId, datasets, chart_config.y_axis_label || 'Value', chart_config.chart_type);
            } else {
                console.warn(`图表 ${containerId} 没有数据`);
                const container = document.getElementById(containerId);
                if(container) {
                    container.innerHTML = `
                        <div class="flex items-center justify-center h-full text-gray-400">
                            <div class="text-center">
                                <div class="text-4xl mb-2">📊</div>
                                <div>暂无数据</div>
                                <div class="text-sm mt-2">图表ID: ${containerId}</div>
                            </div>
                        </div>
                    `;
                }
            }
        }
    }
});
</script>

<style>
/* 自定义样式增强 */
.chart-container {
    transition: all 0.3s ease;
}

.chart-container:hover {
    box-shadow: 0 10px 25px rgba(96, 165, 250, 0.1);
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(75, 85, 99, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #60A5FA, #A855F7);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #3B82F6, #8B5CF6);
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.instance-section {
    animation: fadeInUp 0.6s ease-out;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .chart-container {
        height: 300px !important;
    }
}
</style>