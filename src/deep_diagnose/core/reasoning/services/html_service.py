#!/usr/bin/env python3
"""
HTML处理服务

负责HTML的生成、优化和插入操作，解耦原有的HTML处理逻辑
"""

import logging
import asyncio
from typing import Optional
from deep_diagnose.core.reasoning.reporting.html_insertion_optimizer import HTMLInsertionOptimizer

logger = logging.getLogger(__name__)


class HtmlGenerationError(Exception):
    """HTML生成异常"""
    pass


class HtmlService:
    """HTML处理服务"""
    
    def __init__(self, insertion_optimizer: Optional[HTMLInsertionOptimizer] = None):
        self.insertion_optimizer = insertion_optimizer or HTMLInsertionOptimizer()
    
    async def insert_performance_section(self, html_content: str, performance_section: str, request_id: str) -> str:
        """
        异步插入性能数据section到HTML中
        
        Args:
            html_content: 原始HTML内容
            performance_section: 性能数据section HTML
            request_id: 请求ID
            
        Returns:
            str: 插入后的HTML内容
            
        Raises:
            HtmlGenerationError: 当HTML插入失败时
        """
        try:
            if not performance_section:
                logger.info(f"性能数据section为空，跳过插入，请求ID: {request_id}")
                return html_content
            
            # 使用异步执行器进行HTML插入优化
            enhanced_html = await asyncio.get_event_loop().run_in_executor(
                None, 
                self.insertion_optimizer.optimize_performance_insertion,
                html_content, 
                performance_section, 
                request_id
            )
            
            # 验证插入结果
            if not self._validate_insertion_result(enhanced_html, performance_section):
                logger.warning(f"HTML插入验证失败，返回原始内容，请求ID: {request_id}")
                return html_content
            
            logger.info(f"成功插入性能数据section，请求ID: {request_id}")
            return enhanced_html
            
        except Exception as e:
            logger.error(f"HTML插入失败，请求ID: {request_id}: {e}")
            raise HtmlGenerationError(f"HTML插入失败: {e}")
    
    def _validate_insertion_result(self, html_content: str, performance_section: str) -> bool:
        """
        验证HTML插入结果
        
        Args:
            html_content: 插入后的HTML内容
            performance_section: 原始性能section
            
        Returns:
            bool: 验证是否通过
        """
        try:
            # 检查性能数据是否成功插入
            if "附录A. 性能数据分析" not in html_content:
                logger.warning("性能数据section未成功插入到HTML中")
                return False
            
            # 检查基本HTML结构
            required_elements = ['<!DOCTYPE', '<html', '<head', '<body']
            for element in required_elements:
                if element not in html_content:
                    logger.warning(f"HTML缺少必需元素: {element}")
                    return False
            
            # 检查标签配对
            basic_checks = [
                (html_content.count('<html'), html_content.count('</html>')),
                (html_content.count('<head'), html_content.count('</head>')),
                (html_content.count('<body'), html_content.count('</body>')),
            ]
            
            for open_count, close_count in basic_checks:
                if open_count != close_count:
                    logger.warning(f"HTML标签配对不匹配: {open_count} vs {close_count}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"HTML验证失败: {e}")
            return False
    
    async def analyze_html_structure(self, html_content: str) -> dict:
        """
        异步分析HTML结构
        
        Args:
            html_content: HTML内容
            
        Returns:
            dict: 结构分析结果
        """
        try:
            return await asyncio.get_event_loop().run_in_executor(
                None, 
                self.insertion_optimizer.analyze_html_structure,
                html_content
            )
        except Exception as e:
            logger.error(f"HTML结构分析失败: {e}")
            return {'error': str(e)}