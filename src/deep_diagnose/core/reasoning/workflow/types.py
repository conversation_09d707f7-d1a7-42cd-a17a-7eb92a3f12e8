import operator
from typing import Annotated, Optional, List, Union, Literal
from dataclasses import dataclass, field

from langgraph.graph import MessagesState

from deep_diagnose.prompts.planner_model import Plan




class ReasoningState(MessagesState):
    """State for the agent system, extends MessagesState with next field."""

    # Runtime Variables
    locale: str = "zh-CN"
    request_id: str = ""  # Primary identifier - replaces thread_id usage
    user_query: str = ""  # Rewritten user query from coordinator
    observations: list[str] = []
    plan_iterations: int = 0
    current_plan: Plan | str = None
    final_report: str = ""
    auto_accepted_plan: bool = False
    enable_background_investigation: bool = True
    background_investigation_results: str = None
    mcp_servers_description: str = ""
    sop_id: str = ""
    operation_sop_content: str = ""

    
    # 并发HTML片段（新增字段）
    problem_description_html: str = ""
    diagnosis_info_html: str = ""
    key_findings_html: str = ""
    evidence_chain_html: str = ""
    summary_conclusion_html: str = ""
    merged_html_report: str = ""


# Backward compatibility: keep State as an alias for ReasoningState
State = ReasoningState