"""
计划器消息处理器

处理计划器Agent的消息，重命名自 PlannerEventProcessor
"""

import logging
from typing import Dict, Any, List

from .planning_processor_base import PlanningProcessorBase
from deep_diagnose.core.events.models import AgentMessage

logger = logging.getLogger(__name__)


class PlannerMessageProcessor(PlanningProcessorBase):
    """计划器消息处理器"""

    def __init__(self, request_id: str = None):
        """
        初始化计划器消息处理器
        
        Args:
            request_id: 请求ID，用于标识处理上下文
        """
        super().__init__(request_id=request_id)

    def get_agent_name(self) -> str:
        return "planner"

    def process_messages(self, messages: List[AgentMessage]) -> Dict[str, Any]:
        """处理计划器消息"""
        result = super().process_messages(messages)
        
        # 为计划器添加特殊的处理逻辑（如果需要）
        if result:
            # 可以在这里添加计划器特有的处理逻辑
            # 例如：标记这是初始规划的结果
            result["is_initial_planning"] = True
            
            # 记录初始规划的步骤数量
            if "plan_steps" in result:
                logger.info(f"Planner created {len(result['plan_steps'])} initial steps")
        
        return result