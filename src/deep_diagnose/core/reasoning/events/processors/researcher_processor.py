"""
研究员消息处理器

处理研究员Agent的消息，重命名自 ResearcherEventProcessor
"""

import logging
from typing import Dict, Any, List

from deep_diagnose.core.events.processors.base_processor import BaseMessageProcessor
from deep_diagnose.core.events.models import AgentMessage

logger = logging.getLogger(__name__)


class ResearcherMessageProcessor(BaseMessageProcessor):
    """研究员消息处理器"""

    def __init__(self, request_id: str = None):
        """
        初始化研究员消息处理器
        
        Args:
            request_id: 请求ID，用于标识处理上下文
        """
        super().__init__(request_id=request_id)

    def get_agent_name(self) -> str:
        return "researcher"
    
    def _assign_step_numbers(self, executions: List[Dict[str, Any]], langgraph_ids: List[str]):
        """为所有executions分配步骤编号"""
        for execution in executions:
            if 'langgraph_id' in execution:
                step = self._generate_step_number(execution['langgraph_id'], langgraph_ids)
                execution['step'] = step
                logger.debug(f"分配步骤编号: {execution['langgraph_id']} -> {step}")
    
    def _generate_step_number(self, langgraph_id: str, langgraph_id_list: List[str]) -> str:
        """
        根据langgraph_id生成步骤编号
        
        例如：
        xxx|yyyy -> 1.1
        xxx|yyyy|zzzz -> 1.1.1  
        xxx|yyy2 -> 1.2
        xxx1|yy3 -> 2.1
        """
        current_parts = langgraph_id.split("|")
        step_numbers = []
        
        for level in range(len(current_parts)):
            position = self._get_position_at_level(current_parts, level, langgraph_id_list)
            step_numbers.append(str(position))
        
        return ".".join(step_numbers)
    
    def _get_position_at_level(self, current_parts: List[str], level: int, all_ids: List[str]) -> int:
        """获取当前节点在指定层级的位置编号"""
        parent_path = "|".join(current_parts[:level]) if level > 0 else ""
        current_node = current_parts[level]
        
        # 找到同级的所有节点
        sibling_nodes = self._find_sibling_nodes(parent_path, level, all_ids)
        
        # 返回当前节点的位置（从1开始）
        try:
            return sibling_nodes.index(current_node) + 1
        except ValueError:
            return 1
    
    def _find_sibling_nodes(self, parent_path: str, level: int, all_ids: List[str]) -> List[str]:
        """找到指定父路径下同级的所有节点，保持原始顺序"""
        siblings = []
        seen = set()
        
        for langgraph_id in all_ids:
            parts = langgraph_id.split("|")
            
            # 检查是否有足够的层级
            if len(parts) <= level:
                continue
                
            # 检查父路径是否匹配
            if level == 0 or "|".join(parts[:level]) == parent_path:
                node = parts[level]
                if node not in seen:
                    siblings.append(node)
                    seen.add(node)
        
        return siblings


    def process_messages(self, messages: List[AgentMessage]) -> Dict[str, Any]:
        """处理研究员消息"""
        try:
            # 按稳定序号排序，保证生成顺序稳定
            # 先按稳定序号排序，再按 langgraph_id 的首次出现顺序做次序稳定
            messages = sorted(
                messages,
                key=lambda m: (
                    (m.order or 0),
                    # langgraph_id 次序辅助：短路径优先，字符串排序作为兜底
                    len(m.langgraph_id or ""),
                    m.langgraph_id or "",
                ),
            )
            executions = []
            execution_step: Dict[str, Any] = {}
            langgraph_ids = [message.langgraph_id for message in messages]
            logger.debug(f"收集到的langgraph_ids: {langgraph_ids}")
            for message in messages:
                execution_id = (message.langgraph_id or "").split("|")[0] if message.langgraph_id else ""
                if message.tags and execution_id and execution_id not in execution_step:
                    execution_step[execution_id] = message.tags
                for tool_execution in message.tool_executions:
                    if not self._should_filter_tool(tool_execution.call_name):
                        executions.append({
                            "step_type": "tool_call",
                            "langgraph_id": message.langgraph_id,
                            "tool_call_id": tool_execution.call_id,
                            "tool_name": tool_execution.call_name,
                            "parameters": tool_execution.call_args,
                            "result": tool_execution.call_result,
                            "status": tool_execution.status.value,
                            "tags": message.tags if message.tags else execution_step.get(execution_id)
                        })

                if message.content:
                    executions.append({
                        "step_type": "summary",
                        "langgraph_id": message.langgraph_id,
                        "result": message.content,
                        "tags": message.tags if message.tags else execution_step.get(execution_id)
                    })

            # 在返回前统一计算步骤编号
            self._assign_step_numbers(executions, langgraph_ids)
            
            return {"executions": executions, "execution_step": execution_step}
        except Exception as e:
            logger.error(f"Error processing researcher messages: {e}", exc_info=True)
            return {}

    def _should_filter_tool(self, tool_name: str) -> bool:
        """判断是否应该过滤工具"""
        return tool_name in {"handoff_to_planner"}