"""
HTML报告事件处理器

简洁的HTML报告完成事件处理，从Redis获取报告URL并返回。
"""

import logging
from typing import Dict, Any, List
from deep_diagnose.storage.redis_client import RedisClient
from deep_diagnose.core.events.processors.base_processor import BaseMessageProcessor
from deep_diagnose.core.events.models import AgentMessage

logger = logging.getLogger(__name__)


class HtmlReportFinishMessageProcessor(BaseMessageProcessor):
    """
HTML报告完成消息处理器
    
    当接收到finish消息时，从Redis读取报告URL并返回给客户端。
    支持传统和并发HTML生成流程。
    """
    
    def __init__(self, request_id: str = None):
        super().__init__(request_id=request_id)
        self.redis_client = RedisClient()
    
    def get_agent_name(self) -> str:
        return "finish_event_processor"
    
    def process_messages(self, messages: List[AgentMessage]) -> Dict[str, Any]:
        """处理完成消息，返回报告URL"""
        if not self.request_id:
            logger.warning("请求ID为空，无法处理消息")
            return {}
        
        try:
            for message in messages:
                if message.is_finished:
                    return self._get_report_response()
        except Exception as e:
            logger.error(f"处理消息时发生错误: {e}", exc_info=True)
            return {}
    
    def _get_report_response(self) -> Dict[str, Any]:
        """获取报告URL响应"""
        redis_key = f"diagnosis:report_url:{self.request_id}"
        
        try:
            report_url = self.redis_client.get_cache(redis_key)
            
            if report_url:
                #logger.info(f"成功获取任务 {self.request_id} 的报告URL: {report_url}")
                return {
                    "urls": [{
                        "url": report_url,
                        "name": "CloudBot智能体-长推理诊断报告"
                    }]
                }
            else:
                return {}
                
        except Exception as e:
            logger.error(f"获取报告URL时发生错误: {e}", exc_info=True)
            return {}
    
    def should_process_finish_event(self) -> bool:
        return True
