"""
业务消息处理器

管理所有Agent消息处理器，重命名自 BusinessEventProcessor
"""

import logging
from typing import Dict, Any, List, Optional

from deep_diagnose.core.events.storage import MessageRepository
from deep_diagnose.core.reasoning.events.processors.coordinator_processor import CoordinatorMessageProcessor
from deep_diagnose.core.reasoning.events.processors.planner_processor import PlannerMessageProcessor
from deep_diagnose.core.reasoning.events.processors.researcher_processor import ResearcherMessageProcessor
from deep_diagnose.core.reasoning.events.processors.reporter_processor import ReporterMessageProcessor
from deep_diagnose.core.reasoning.events.processors.html_generator_processor import HtmlReportFinishMessageProcessor
from deep_diagnose.core.reasoning.events.processors.replanner_processor import ReplannerMessageProcessor


logger = logging.getLogger(__name__)


class ReasoningEventMessageProcessor:
    """业务消息处理器 - 管理所有Agent消息处理器"""

    def __init__(self, request_id: Optional[str] = None):
        """
        初始化业务消息处理器
        
        Args:
            request_id: 请求ID，传递给所有子处理器
        """
        self.request_id = request_id
        self.processors = {
            "coordinator": CoordinatorMessageProcessor(request_id=request_id),
            "planner": PlannerMessageProcessor(request_id=request_id),
            "replanner": ReplannerMessageProcessor(request_id=request_id),
            "researcher": ResearcherMessageProcessor(request_id=request_id),
            "background_investigator": ResearcherMessageProcessor(request_id=request_id),
            "coder": ResearcherMessageProcessor(request_id=request_id),
            "reporter": ReporterMessageProcessor(request_id=request_id),
            "html_report_merger": HtmlReportFinishMessageProcessor(request_id=request_id),
        }

    def process_business_messages(self, message_repository: MessageRepository, only_agents: Optional[List[str]] = None) -> Dict[str, Any]:
        """处理业务消息 - 优化版本，只处理交集中的Agent"""
        results = {}
        try:
            # 性能优化：只处理交集中的Agent
            if only_agents:
                # 直接交集过滤，无需复杂映射逻辑
                valid_agents = set(only_agents) & set(self.processors.keys())
                target_processors = [(agent, self.processors[agent]) for agent in valid_agents]

                # 只处理有效的processors
                for agent_name, processor in target_processors:
                    # 如果是coder或researcher，获取两者的所有消息
                    if agent_name in ['coder', 'researcher']:
                        researcher_messages = message_repository.get_messages_by_agent('researcher')
                        coder_messages = message_repository.get_messages_by_agent('coder')
                        # 合并两个agent的消息
                        agent_messages = (researcher_messages or []) + (coder_messages or [])
                    else:
                        agent_messages = message_repository.get_messages_by_agent(agent_name)
                    
                    if agent_messages:  # 只在有消息时才处理
                        try:
                            agent_result = processor.process_messages(agent_messages)
                            if agent_result:  # 简化判断，去掉不必要的内容检查
                                results.update(agent_result)
                        except Exception as e:
                            logger.warning(f"Processor {agent_name} failed: {e}")
                            continue
            
        except Exception as e:
            logger.error(f"Error processing business messages: {e}", exc_info=True)
        return results



    def get_supported_agents(self) -> List[str]:
        """获取支持的Agent列表"""
        return list(self.processors.keys())