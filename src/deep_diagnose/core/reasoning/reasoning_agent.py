"""
Reasoning Agent V2 实现

V2版本的推理智能体，内聚了状态解析逻辑，对外播报结构化的业务事件。
完全参考现有的 chat_service_deprecated.py 实现，正确调用 Graph 和解析 SSE 事件。
"""
import asyncio
import logging
from typing import AsyncGenerator, List, Dict, Any, Optional
from uuid import uuid4

# Deferred import of CallbackHandler to avoid ImportError during module import
# from langfuse.callback import CallbackHandler
from langgraph.types import Command
from deep_diagnose.core.agent.base_graph import BaseGraph

from deep_diagnose.core.reasoning.events.reasoning_events import ReasoningAgentEvent as ReasoningAgentEvent
from deep_diagnose.common.config import get_config
from deep_diagnose.core.reasoning.workflow.builder import build_graph_with_memory
from deep_diagnose.tools.mcp.mcp_tool_config import MCPToolConfig
from deep_diagnose.core.reasoning.agents.react_agent_factory import get_recursion_limit
from deep_diagnose.storage.redis_client import RedisClient
from deep_diagnose.storage.redis_pubsub_stream import RedisPubSubStream

logger = logging.getLogger(__name__)


class ReasoningAgent(BaseGraph):
    """

    - Orchestrates graph streaming and event transformation
    - Uses ReasoningAgentEventV2 which internally decouples collection (sync) and processing (async queue)
    - Ensures lifecycle: always stop background processor in finally
    - Provides shallow-copy helper to avoid copying asyncio internals
    """
    """
    V1版本的推理智能体，内聚了状态解析逻辑，对外播报结构化的业务事件。
    
    完全参考 chat_service_deprecated.py 的实现：
    1. _setup_execution 方法 - 创建 Graph 配置
    2. _stream_execution 方法 - 流式执行和事件解析
    3. _parse_final_content 方法 - 最终内容解析
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化推理智能体
        
        Args:
            config: 配置字典，包含langfuse_handler等参数
        """
        super().__init__(config)
        # 内部构建 LangChain Graph workflow
        self._graph = build_graph_with_memory()
        self._langfuse_handler = None
        # 运行时状态 - 只保留核心状态变量
        self.plan_iterations: int = 0
        self.final_report: str = ""
        self.current_plan: Optional[list] = None
        self.observations: list = []
        self.auto_accepted_plan = True
        self.enable_background_investigation = False
        self.interrupt_feedback = None
        self.max_plan_iterations = 1
        self.max_search_results = 3
        self.max_step_num = 30

        # Redis URL consumer相关属性
        self._redis_client = None
        self._pubsub_stream = None
        self._url_consumer_task = None

        # 从配置中获取可选的依赖项
        if config:
            # 允许外部覆盖langfuse_handler
            self._langfuse_handler = config.get("langfuse_handler")
            # 允许外部覆盖默认配置
            self.auto_accepted_plan = config.get("auto_accepted_plan", self.auto_accepted_plan)
            self.enable_background_investigation = config.get("enable_background_investigation",
                                                              self.enable_background_investigation)
            self.interrupt_feedback = config.get("interrupt_feedback", self.interrupt_feedback)
            self.max_plan_iterations = config.get("max_plan_iterations", self.max_plan_iterations)
            self.max_search_results = config.get("max_search_results", self.max_search_results)
            self.max_step_num = config.get("max_step_num", self.max_step_num)
            self.request_id = config.get("request_id")

    async def astream(
            self,
            question: str,
            messages: Optional[List[Dict[str, Any]]] = None,
            **kwargs: Any
    ) -> AsyncGenerator[ReasoningAgentEvent, None]:
        """
        流式执行推理任务并输出结构化事件
        
        简化的流式转换逻辑：
        1. 创建 Graph
        2. 流式接收 Graph 事件
        3. 解析并转换为结构化事件
        4. 实时输出事件
        
        Args:
            question: 用户问题
            messages: 历史消息列表
            **kwargs: 其他参数
            
        Yields:
            ReasoningAgentEvent: 推理事件对象
        """
        request_id = kwargs.get("request_id", kwargs.get("thread_id", "agent_run"))

        # 记录函数开始时的question和messages信息
        logger.info(f"ReasoningAgent.astream started - question: {question}")
        logger.info(f"ReasoningAgent.astream started - messages: {messages}")

        messages = [{"role": "user", "content": question}]

        # 生成request_id
        if not request_id or request_id == "agent_run":
            request_id = str(uuid4())

        # 使用默认MCP设置
        mcp_settings = MCPToolConfig.get_server_configs()

        try:
            # 构建工作流输入 - 使用实例属性
            input_data = {
                "messages": messages,
                "plan_iterations": self.plan_iterations,
                "final_report": self.final_report,
                "current_plan": self.current_plan,
                "observations": self.observations,
                "auto_accepted_plan": self.auto_accepted_plan,
                "enable_background_investigation": self.enable_background_investigation,
                "request_id": request_id,
            }

            # 处理中断反馈
            if not self.auto_accepted_plan and self.interrupt_feedback:
                resume_msg = f"[{self.interrupt_feedback}]"
                # 将最后一条消息添加到恢复消息中
                if messages:
                    resume_msg += f" {messages[-1]['content']}"
                input_data = Command(resume=resume_msg)

            # 构建配置 - 使用实例属性
            config = {
                "thread_id": request_id,  # Keep thread_id for LangGraph compatibility
                "max_step_num": self.max_step_num,
                "mcp_settings": mcp_settings,
                "recursion_limit": get_recursion_limit(),
                "max_plan_iterations": self.max_plan_iterations,
                "max_search_results": self.max_search_results
            }

            # 添加langfuse_handler（如果存在）
            if self._langfuse_handler:
                config["callbacks"] = [self._langfuse_handler]

            # 创建推理事件对象用于解析和累积状态
            reasoning_event = ReasoningAgentEvent(request_id=request_id)
            reasoning_event.understanding = "已接收用户问题，正在启动智能诊断流程"
            
            # 启动Redis URL consumer - 修复版本
            await self._start_url_consumer(request_id, reasoning_event)
            
            yield self._shallow_copy_event(reasoning_event)
            # 执行工作流并处理事件流
            async for agent, _, event_data in self._graph.astream(
                    input_data,
                    config=config,
                    stream_mode=["messages", "updates"],
                    subgraphs=True,
            ):
                # 仅入队，由后台 _processor_loop 串行消费并更新 reasoning_event 的聚合状态。
                # 为了确保异步更新也能被及时感知并输出，这里对“自上次输出后的版本变更”做检测。

                # 只处理关键agent的事件，跳过其他agent来验证延迟原因
                critical_agents = {"planner","replanner", "coordinator", "reporter", "researcher",  "coder","html_report_merger"}

                # 处理agent参数（可能是元组或字符串）
                if isinstance(agent, tuple) and len(agent) > 0:
                    agent_str = str(agent[0])  # 取元组的第一个元素
                else:
                    agent_str = str(agent)

                # 提取实际的agent名称（取冒号前的第一个部分）
                actual_agent_name = agent_str.split(':')[0] if ':' in agent_str else agent_str

                if actual_agent_name not in critical_agents:
                    logger.debug(f"🚫 跳过非关键agent事件处理: {agent} (提取名称: {actual_agent_name})")
                    continue


                # 解析事件并入队（异步处理）
                reasoning_event.parse_graph_event(agent, event_data, request_id)

                # 仅当队列中有待处理项时，才等待版本变更，避免在graph内部等待时空转
                if reasoning_event._queue.qsize() > 0:
                    try:
                        await asyncio.wait_for(reasoning_event._version_changed.wait(), timeout=0.05)
                    except asyncio.TimeoutError:
                        logger.debug(f"Timeout waiting for version change, queue size: {reasoning_event._queue.qsize()}")
                    except Exception as e:
                        logger.error(f"Error waiting for version change: {e}")
                    finally:
                        try:
                            reasoning_event._version_changed.clear()
                        except Exception:
                            pass

                # 基于变更快照输出
                if not hasattr(self, "_last_emit_version"):
                    self._last_emit_version = 0
                    self._last_emit_thought_len = 0
                    self._last_emit_result_len = 0
                    self._last_emit_executions_len = 0
                    self._last_emit_plan_steps_len = 0

                current_version = getattr(reasoning_event, "_version", 0)
                cur_thought_len = len(reasoning_event.thought)
                cur_result_len = len(reasoning_event.result)
                cur_exec_len = len(reasoning_event.executions)
                cur_plan_len = len(reasoning_event.plan_steps)

                has_incremental_info = (
                        current_version != self._last_emit_version or
                        cur_thought_len > self._last_emit_thought_len or
                        cur_result_len > self._last_emit_result_len or
                        cur_exec_len > self._last_emit_executions_len or
                        cur_plan_len > self._last_emit_plan_steps_len
                )

                if has_incremental_info:
                    # 更新“已输出”的快照指标并输出
                    self._last_emit_version = current_version
                    self._last_emit_thought_len = cur_thought_len
                    self._last_emit_result_len = cur_result_len
                    self._last_emit_executions_len = cur_exec_len
                    self._last_emit_plan_steps_len = cur_plan_len
                    yield self._shallow_copy_event(reasoning_event)
            # 生命周期结束：优雅关闭后台处理
            try:
                reasoning_event.stop()
                # 停止Redis URL consumer - 修复版本
                await self._stop_url_consumer()
                yield self._shallow_copy_event(reasoning_event)
            except Exception:
                pass


        except Exception as e:
            # 记录完整的错误信息和堆栈
            import traceback
            logger.error(
                f"An error occurred during ReasoningAgentV1 stream for request_id {request_id}: {e}\n"
                f"{traceback.format_exc()}"
            )

            # 将错误信息添加到reasoning_event并返回
            if 'reasoning_event' in locals():
                reasoning_event.result += f"\n\n[Error] {type(e).__name__}: {str(e)}"
                yield self._shallow_copy_event(reasoning_event)
            else:
                # 如果reasoning_event还未创建，创建一个包含错误信息的事件
                error_reasoning_event = ReasoningAgentEvent(request_id=request_id)
                error_reasoning_event.result = f"[Error] {type(e).__name__}: {str(e)}"
                yield error_reasoning_event

    def _setup_graph_config(self, kwargs: Dict[str, Any], thread_id: str) -> Dict[str, Any]:
        """
        设置 Graph 配置 - 完全参考 chat_service_deprecated.py 的 _setup_execution
        
        Args:
            kwargs: 传入的参数
            thread_id: 线程ID
            
        Returns:
            Dict: Graph 配置
        """
        # 优先使用实例级别的 langfuse_handler
        langfuse_handler = self._langfuse_handler or kwargs.get("langfuse_handler")

        # 如果都没有提供，创建新的 langfuse_handler - 与 chat_service_deprecated.py 一致
        if not langfuse_handler:
            try:
                # Deferred import to avoid ImportError at module import time
                from langfuse.callback import CallbackHandler  # type: ignore
                langfuse_handler = CallbackHandler(
                    public_key=get_config().observability.langfuse.public_key,
                    secret_key=get_config().observability.langfuse.secret_key,
                    host=get_config().observability.langfuse.endpoint
                )
            except Exception as e:
                logger.warning(f"Failed to create langfuse handler: {e}")
                langfuse_handler = None

        config = {
            "langfuse_handler": langfuse_handler,
            "thread_id": thread_id
        }

        logger.info(f"Graph configured with thread_id: {thread_id}")
        return config

    def _shallow_copy_event(self, src: "ReasoningAgentEvent") -> "ReasoningAgentEvent":
        """Shallow copy ReasoningAgentEvent fields to avoid copying asyncio internals.
        Only copies plain fields used by SSE consumers.
        """
        dst = ReasoningAgentEvent(request_id=src.request_id)
        dst.understanding = src.understanding
        dst.thought = src.thought
        dst.plan_steps = list(src.plan_steps)
        dst.executions = list(src.executions)
        dst.result = src.result
        dst.urls = list(src.urls)
        dst.finished = src.finished
        return dst

    async def _start_url_consumer(self, request_id: str, reasoning_event: ReasoningAgentEvent):
        """启动Redis URL consumer - 修复版本
        
        修复问题：
        1. 延长生命周期，避免过早停止
        2. 增加超时时间
        3. 改进错误处理
        """
        try:
            self._redis_client = RedisClient()
            self._pubsub_stream = RedisPubSubStream(self._redis_client)
            
            # 订阅报告就绪频道
            channel = f"diagnosis:report_ready:{request_id}"
            await self._pubsub_stream.subscribe(channel)
            
            # 启动后台消费任务
            self._url_consumer_task = asyncio.create_task(
                self._url_consumer_loop(reasoning_event, channel)
            )
            
            logger.info(f"✅ Redis URL consumer已启动，订阅频道: {channel}")
            
        except Exception as e:
            logger.error(f"启动Redis URL consumer失败: {e}", exc_info=True)

    async def _url_consumer_loop(self, reasoning_event: ReasoningAgentEvent, channel: str):
        """URL消费循环 - 修复版本
        
        修复问题：
        1. 增加超时时间到30秒（足够html_merger完成）
        2. 添加详细日志
        3. 改进消息处理逻辑
        """
        logger.info(f"🔄 URL consumer开始监听: {channel}")
        message_count = 0
        
        try:
            # 修复1: 增加超时时间，给html_merger足够时间完成
            async for message in self._pubsub_stream.aiter_messages(timeout=30.0):
                message_count += 1
                
                if message is None:
                    logger.debug(f"URL consumer超时等待 (第{message_count}次)")
                    continue
                    
                logger.info(f"📨 URL consumer收到消息: {message}")
                    
                try:
                    import json
                    message_data = json.loads(message)
                    
                    # 验证消息格式
                    if (message_data.get("type") == "report_ready" and 
                        message_data.get("url")):
                        
                        # 构建URL对象
                        url_info = {
                            "url": message_data["url"],
                            "task_id": message_data.get("request_id", message_data.get("task_id")),
                            "timestamp": message_data.get("timestamp"),
                            "type": "report",
                            "name": "CloudBot智能体-长推理诊断报告"
                        }
                        
                        # 修复2: 更新reasoning_event的urls并触发版本更新
                        reasoning_event.urls.append(url_info)
                        reasoning_event._version += 1
                        
                        try:
                            reasoning_event._version_changed.set()
                        except Exception:
                            pass
                        
                        logger.info(f"✅ URL consumer成功添加URL: {message_data['url']}")
                        
                        # 收到URL后退出循环
                        break
                        
                except json.JSONDecodeError as e:
                    logger.warning(f"解析Redis消息失败: {e}")
                except Exception as e:
                    logger.error(f"处理Redis消息失败: {e}")
                    
        except Exception as e:
            logger.error(f"Redis URL consumer循环异常: {e}", exc_info=True)
        finally:
            logger.info(f"🔚 URL consumer结束，共处理 {message_count} 条消息")

    async def _stop_url_consumer(self):
        """停止Redis URL consumer - 修复版本"""
        try:
            # 取消消费任务
            if self._url_consumer_task and not self._url_consumer_task.done():
                self._url_consumer_task.cancel()
                try:
                    await self._url_consumer_task
                except asyncio.CancelledError:
                    pass
            
            # 关闭PubSub连接
            if self._pubsub_stream:
                await self._pubsub_stream.close()
                self._pubsub_stream = None
            
            # 清理Redis客户端
            self._redis_client = None
            self._url_consumer_task = None
            
            logger.info("✅ Redis URL consumer已停止")
            
        except Exception as e:
            logger.error(f"停止Redis URL consumer失败: {e}", exc_info=True)
