"""
基于LLM的HTML生成器

使用LLM智能生成高质量的HTML内容
"""

import logging
from typing import Optional
from .base import BaseHTMLGenerator

logger = logging.getLogger(__name__)


class LLMHTMLGenerator(BaseHTMLGenerator):
    """基于LLM的HTML生成器"""
    
    def __init__(self, output_dir: Optional[str] = None, enable_oss: bool = True, llm_type: str = "basic"):
        """
        初始化LLM HTML生成器
        
        Args:
            output_dir: 输出目录
            enable_oss: 是否启用OSS上传
            llm_type: LLM类型，默认为basic
        """
        super().__init__(output_dir, enable_oss)
        self.llm_type = llm_type
        
    def markdown_to_html(self, content: str) -> str:
        """使用LLM转换Markdown到HTML"""
        if not content:
            return "<p>暂无内容</p>"
        
        try:
            # 构建LLM提示词
            prompt = self._build_conversion_prompt(content)
            
            # 调用LLM进行转换
            html_content = self._call_llm(prompt)
            
            # 验证和清理HTML
            cleaned_html = self._clean_and_validate_html(html_content)
            
            return cleaned_html
            
        except Exception as e:
            logger.error(f"LLM HTML conversion failed: {e}")
            # 降级到简单的规则转换
            return self._fallback_conversion(content)

    async def amarkdown_to_html(self, content: str) -> str:
        """使用LLM异步地将Markdown转换为HTML"""
        if not content:
            return "<p>暂无内容</p>"
        
        try:
            # 构建LLM提示词
            prompt = self._build_conversion_prompt(content)
            
            # 异步调用LLM进行转换
            html_content = await self._acall_llm(prompt)
            
            # 清理和验证HTML
            cleaned_html = self._clean_and_validate_html(html_content)
            
            return cleaned_html
            
        except Exception as e:
            logger.error(f"LLM异步HTML转换失败: {e}")
            # 降级到简单的规则转换
            return self._fallback_conversion(content)

    async def _acall_llm(self, prompt: str) -> str:
        """异步调用LLM进行转换"""
        try:
            from deep_diagnose.llms.llm import get_llm_by_type
            from deep_diagnose.common.config.constants.agents import AGENT_LLM_MAP
            
            # 获取LLM实例
            llm = get_llm_by_type(self.llm_type)
            
            # 异步调用LLM
            response = await llm.ainvoke(prompt, config={"stream": False, "tags": ["html_generation"]})
            
            if hasattr(response, 'content'):
                return response.content
            else:
                return str(response)
                
        except Exception as e:
            logger.error(f"LLM异步调用失败: {e}")
            raise
    
    def _build_conversion_prompt(self, content: str) -> str:
        """构建LLM转换提示词"""
        return f"""请将以下Markdown内容转换为高质量的HTML，要求：

1. 保持内容的结构和语义
2. 使用语义化的HTML标签
3. 对于表格，确保正确的table/thead/tbody结构
4. 对于代码块，使用pre和code标签，并保持语言标识
5. 对于列表，使用正确的ul/ol/li结构
6. 只返回HTML body内容，不包含html/head/body标签
7. 确保HTML格式正确且语义清晰

Markdown内容：
```markdown
{content}
```

请返回转换后的HTML："""
    
    def _call_llm(self, prompt: str) -> str:
        """调用LLM进行转换"""
        try:
            from deep_diagnose.llms.llm import get_llm_by_type
            from deep_diagnose.common.config.constants.agents import AGENT_LLM_MAP
            
            # 获取LLM实例
            llm = get_llm_by_type(self.llm_type)
            
            # 调用LLM
            response = llm.invoke(prompt, config={"stream": False, "tags": ["html_generation"]})
            
            if hasattr(response, 'content'):
                return response.content
            else:
                return str(response)
                
        except Exception as e:
            logger.error(f"LLM call failed: {e}")
            raise
    
    def _clean_and_validate_html(self, html_content: str) -> str:
        """清理和验证HTML内容"""
        # 移除可能的markdown代码块标记
        html_content = html_content.strip()
        
        # 如果LLM返回的内容包含```html标记，提取其中的内容
        if html_content.startswith('```html'):
            lines = html_content.split('\n')
            html_lines = []
            in_html_block = False
            
            for line in lines:
                if line.strip() == '```html':
                    in_html_block = True
                    continue
                elif line.strip() == '```':
                    in_html_block = False
                    continue
                elif in_html_block:
                    html_lines.append(line)
            
            html_content = '\n'.join(html_lines)
        
        # 移除可能的HTML/body标签包装
        html_content = html_content.strip()
        if html_content.startswith('<html'):
            # 提取body内容
            import re
            body_match = re.search(r'<body[^>]*>(.*?)</body>', html_content, re.DOTALL | re.IGNORECASE)
            if body_match:
                html_content = body_match.group(1)
        
        # 基本的HTML清理
        html_content = html_content.strip()
        
        # 如果内容为空，返回默认内容
        if not html_content:
            return "<p>暂无内容</p>"
        
        return html_content
    
    def _fallback_conversion(self, content: str) -> str:
        """降级转换方案"""
        logger.info("Using fallback rule-based conversion")
        
        # 使用简单的规则进行转换
        from .rule_based_generator import RuleBasedHTMLGenerator
        
        # 创建临时的规则生成器进行转换
        rule_generator = RuleBasedHTMLGenerator()
        return rule_generator.markdown_to_html(content)
    
    def set_llm_type(self, llm_type: str):
        """设置LLM类型"""
        self.llm_type = llm_type
        logger.info(f"LLM type set to: {llm_type}")
    
    def get_conversion_quality_score(self, original_content: str, html_content: str) -> float:
        """评估转换质量（可选功能）"""
        try:
            # 简单的质量评估指标
            score = 0.0
            
            # 检查是否包含基本HTML标签
            if '<p>' in html_content or '<h1>' in html_content or '<h2>' in html_content:
                score += 0.3
            
            # 检查是否保持了原始内容的长度比例
            original_words = len(original_content.split())
            html_text_length = len(html_content.replace('<', ' ').replace('>', ' ').split())
            
            if original_words > 0:
                ratio = html_text_length / original_words
                if 0.8 <= ratio <= 1.5:  # 合理的长度比例
                    score += 0.4
            
            # 检查HTML格式是否正确（简单检查）
            if html_content.count('<') == html_content.count('>'):
                score += 0.3
            
            return min(score, 1.0)
            
        except Exception as e:
            logger.warning(f"Quality assessment failed: {e}")
            return 0.5  # 返回中等分数