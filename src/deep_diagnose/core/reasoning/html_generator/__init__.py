"""
HTML报告生成器模块

支持两种生成方式：
1. Rule-based Generator: 基于规则的快速转换
2. LLM Generator: 基于LLM的智能转换

使用示例：
    # 基于规则的生成器（默认，快速）
    generator = create_html_generator(generator_type="rule")
    result = generator.generate_simple_report("task_123", "# 报告内容")
    
    # 基于LLM的生成器（智能，高质量）
    generator = create_html_generator(generator_type="llm")
    result = generator.generate_simple_report("task_123", "# 报告内容")
"""

# 导入两种生成器
from .base import DocumentFormat, GenerationResult, BaseHTMLGenerator
from .rule_based_generator import RuleBasedHTMLGenerator
from .llm_generator import LLMHTMLGenerator

# 为了向后兼容，保留HTMLGenerator别名指向规则生成器
HTMLGenerator = RuleBasedHTMLGenerator

from typing import Optional, Union


def create_html_generator(
    generator_type: str = "rule", 
    output_dir: Optional[str] = None, 
    enable_oss: bool = True,
    llm_type: str = "basic"
) -> BaseHTMLGenerator:
    """
    创建HTML生成器
    
    Args:
        generator_type: 生成器类型 ("rule" 或 "llm")
        output_dir: 输出目录
        enable_oss: 是否启用OSS上传
        llm_type: LLM类型（仅在generator_type="llm"时有效）
        
    Returns:
        HTML生成器实例
    """
    if generator_type.lower() == "llm":
        return LLMHTMLGenerator(output_dir, enable_oss, llm_type)
    elif generator_type.lower() == "rule":
        return RuleBasedHTMLGenerator(output_dir, enable_oss)
    else:
        raise ValueError(f"不支持的生成器类型: {generator_type}. 请使用 'rule' 或 'llm'")


def create_rule_based_generator(output_dir: Optional[str] = None, enable_oss: bool = True) -> RuleBasedHTMLGenerator:
    """创建基于规则的HTML生成器"""
    return RuleBasedHTMLGenerator(output_dir, enable_oss)


def create_llm_generator(
    output_dir: Optional[str] = None, 
    enable_oss: bool = True, 
    llm_type: str = "basic"
) -> LLMHTMLGenerator:
    """创建基于LLM的HTML生成器"""
    return LLMHTMLGenerator(output_dir, enable_oss, llm_type)


def generate_simple_report(task_id: str, content: str, generator_type: str = "rule") -> GenerationResult:
    """
    生成简单报告的便捷函数
    
    Args:
        task_id: 任务ID
        content: 报告内容
        generator_type: 生成器类型 ("rule" 或 "llm")
        
    Returns:
        生成结果
    """
    generator = create_html_generator(generator_type=generator_type, enable_oss=True)
    return generator.generate_simple_report(task_id, content)


# 统一导出
__all__ = [
    # 核心类
    'BaseHTMLGenerator',
    'RuleBasedHTMLGenerator', 
    'LLMHTMLGenerator',
    'HTMLGenerator',  # 向后兼容别名
    'GenerationResult', 
    'DocumentFormat',
    
    # 工厂函数
    'create_html_generator',
    'create_rule_based_generator',
    'create_llm_generator',
    
    # 便捷函数
    'generate_simple_report'
]

# 向后兼容的别名
def create_auto_generator(output_dir=None, enable_oss=True):
    """向后兼容：自动选择生成器，实际返回规则生成器"""
    return create_html_generator("rule", output_dir, enable_oss)