"""
HTML生成器基类和通用组件
"""

import logging
import os
import tempfile
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class DocumentFormat(Enum):
    """支持的文档格式"""
    HTML = "html"


@dataclass
class GenerationResult:
    """统一的文档生成结果"""
    success: bool                           # 是否成功
    file_path: Optional[str] = None        # 本地文件路径
    file_size: Optional[int] = None        # 文件大小（字节）
    oss_url: Optional[str] = None          # OSS访问链接
    error_message: Optional[str] = None    # 错误信息
    format: Optional[DocumentFormat] = None # 文档格式


class BaseHTMLGenerator(ABC):
    """HTML生成器基类"""
    
    def __init__(self, output_dir: Optional[str] = None, enable_oss: bool = True):
        self.output_dir = output_dir or tempfile.gettempdir()
        self.enable_oss = enable_oss
        os.makedirs(self.output_dir, exist_ok=True)
        logger.info(f"{self.__class__.__name__} initialized: {self.output_dir}, OSS: {enable_oss}")
    
    def generate_simple_report(self, task_id: str, content: str) -> GenerationResult:
        """生成简单HTML报告"""
        try:
            # 生成文件路径
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"diagnostic_report_{task_id}_{timestamp}.html"
            file_path = os.path.join(self.output_dir, filename)
            
            # 转换Markdown到HTML（子类实现）
            html_content = self.markdown_to_html(content)
            
            # 生成完整的HTML文档
            full_html = self._build_complete_html(html_content)
            
            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(full_html)
            
            file_size = os.path.getsize(file_path)
            logger.info(f"HTML document generated: {file_path}")
            
            # 上传到OSS（如果启用）
            oss_url = None
            if self.enable_oss:
                try:
                    oss_url = self._upload_to_oss(file_path, task_id)
                    logger.info(f"Successfully uploaded HTML to OSS: {oss_url}")
                except Exception as e:
                    logger.warning(f"Failed to upload HTML to OSS: {e}")
            
            return GenerationResult(
                success=True,
                file_path=file_path,
                file_size=file_size,
                oss_url=oss_url,
                format=DocumentFormat.HTML
            )
            
        except Exception as e:
            error_msg = f"Failed to generate HTML report for task {task_id}: {str(e)}"
            logger.error(error_msg)
            return GenerationResult(
                success=False,
                error_message=error_msg,
                format=DocumentFormat.HTML
            )
    
    @abstractmethod
    def markdown_to_html(self, content: str) -> str:
        """转换Markdown到HTML - 子类实现"""
        pass
    
    def _build_complete_html(self, content: str) -> str:
        """构建完整的HTML文档"""
        return f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CloudBot智能体-深度诊断报告</title>
    <link rel="shortcut icon" href="https://cloudbot2.aliyun-inc.com/cloudbot/img/favicon_front.ico">
    <link rel="icon" type="image/x-icon" href="https://cloudbot2.aliyun-inc.com/cloudbot/img/favicon_front.ico">
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: #1A202C;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #F7FAFC;
        }}
        h1, h2, h3, h4, h5, h6 {{ color: #3182CE; margin-top: 2em; margin-bottom: 1em; }}
        h1 {{ font-size: 2.5em; border-bottom: 3px solid #3182CE; padding-bottom: 10px; }}
        h2 {{ font-size: 2em; border-bottom: 2px solid #63B3ED; padding-bottom: 8px; }}
        h3 {{ font-size: 1.5em; }}
        p {{ margin-bottom: 1em; }}
        code {{ background-color: #F7FAFC; color: #D53F8C; padding: 2px 4px; border-radius: 3px; font-family: 'Consolas', 'Monaco', 'Courier New', monospace; }}
        pre {{ background-color: #EDF2F7; border: 1px solid #E2E8F0; border-radius: 5px; padding: 15px; overflow-x: auto; }}
        pre code {{ background: none; color: inherit; padding: 0; }}
        ul, ol {{ margin: 1em 0; padding-left: 2em; }}
        li {{ margin-bottom: 0.5em; }}
        hr {{ border: none; height: 2px; background-color: #CBD5E0; margin: 2em 0; }}
        a {{ color: #3182CE; text-decoration: none; }}
        a:hover {{ text-decoration: underline; }}
        strong {{ color: #1A202C; }}
        em {{ color: #4A5568; }}
        table {{ width: 100%; border-collapse: collapse; margin: 1em 0; background-color: white; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        th, td {{ border: 1px solid #CBD5E0; padding: 12px; text-align: left; }}
        th {{ background-color: #4299E1; color: #FFFFFF; font-weight: bold; }}
        tr:nth-child(even) {{ background-color: #F7FAFC; }}
        blockquote {{ border-left: 4px solid #3182CE; margin: 1em 0; padding: 0.5em 1em; background-color: #F0FFF4; color: #38A169; }}
    </style>
</head>
<body>
{content}
<hr>
<p style="text-align: center; color: #7F8C8D; font-size: 0.9em;">
    生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
</p>
</body>
</html>"""
    
    def _upload_to_oss(self, file_path: str, task_id: str) -> str:
        """上传文件到OSS"""
        try:
            from deep_diagnose.storage.oss_client import OssClient
            from deep_diagnose.common.config import get_config
            
            try:
                oss_upload_dir = get_config().infrastructure.oss.upload_dir
            except Exception:
                oss_upload_dir = "html_reports"

            oss_client = OssClient()
            filename = os.path.basename(file_path)
            file_extension = os.path.splitext(filename)[1]
            oss_key = f"{oss_upload_dir}_{task_id}{file_extension}"
            
            oss_client.upload_file(file_path, oss_key)
            signed_url = oss_client.get_file_url(oss_key, 2592000)
            return signed_url
            
        except ImportError:
            logger.error("OSS client not available, skipping upload")
            raise Exception("OSS client not available")
        except Exception as e:
            logger.error(f"OSS upload failed: {e}")
            raise