"""
基于规则的HTML生成器

使用正则表达式和规则转换Markdown到HTML
"""

import re
import html
from .base import BaseHTMLGenerator


class RuleBasedHTMLGenerator(BaseHTMLGenerator):
    """基于规则的HTML生成器"""
    
    def markdown_to_html(self, content: str) -> str:
        """基于规则的Markdown到HTML转换"""
        if not content:
            return "<p>暂无内容</p>"
        
        lines = content.split('\n')
        html_lines = []
        in_code_block = False
        in_list = False
        list_items = []
        
        for line in lines:
            line = line.rstrip()
            
            # 处理代码块
            if line.startswith('```'):
                if in_code_block:
                    html_lines.append('</code></pre>')
                    in_code_block = False
                else:
                    # 关闭列表（如果有）
                    if in_list:
                        html_lines.extend(self._close_list(list_items))
                        list_items = []
                        in_list = False
                    
                    # 提取语言标识
                    lang = line[3:].strip()
                    if lang:
                        html_lines.append(f'<pre><code class="language-{lang}">')
                    else:
                        html_lines.append('<pre><code>')
                    in_code_block = True
                continue
            
            if in_code_block:
                html_lines.append(html.escape(line))
                continue
            
            # 处理列表项
            if line.startswith('- ') or line.startswith('* '):
                if not in_list:
                    in_list = True
                list_items.append(('ul', line[2:].strip()))
                continue
            elif re.match(r'^\d+\.\s', line):
                if not in_list:
                    in_list = True
                match = re.match(r'^\d+\.\s(.+)', line)
                if match:
                    list_items.append(('ol', match.group(1)))
                continue
            else:
                # 非列表项，关闭列表
                if in_list:
                    html_lines.extend(self._close_list(list_items))
                    list_items = []
                    in_list = False
            
            # 处理其他元素
            if not line.strip():
                if not in_list:  # 只有在非列表中才添加换行
                    html_lines.append('<br>')
            elif line.startswith('# '):
                html_lines.append(f'<h1>{self._process_inline_formatting(line[2:])}</h1>')
            elif line.startswith('## '):
                html_lines.append(f'<h2>{self._process_inline_formatting(line[3:])}</h2>')
            elif line.startswith('### '):
                html_lines.append(f'<h3>{self._process_inline_formatting(line[4:])}</h3>')
            elif line.startswith('#### '):
                html_lines.append(f'<h4>{self._process_inline_formatting(line[5:])}</h4>')
            elif line.startswith('> '):
                html_lines.append(f'<blockquote>{self._process_inline_formatting(line[2:])}</blockquote>')
            elif line.strip() == '---':
                html_lines.append('<hr>')
            elif line.startswith('|') and '|' in line[1:]:
                # 表格处理
                html_lines.append(self._process_table_line(line))
            else:
                # 普通段落
                processed = self._process_inline_formatting(line)
                html_lines.append(f'<p>{processed}</p>')
        
        # 关闭未关闭的元素
        if in_code_block:
            html_lines.append('</code></pre>')
        
        if in_list:
            html_lines.extend(self._close_list(list_items))
        
        return '\n'.join(html_lines)
    
    def _close_list(self, list_items):
        """关闭列表并返回HTML"""
        if not list_items:
            return []
        
        html_lines = []
        current_type = None
        
        for item_type, content in list_items:
            if current_type != item_type:
                if current_type:
                    html_lines.append(f'</{current_type}>')
                html_lines.append(f'<{item_type}>')
                current_type = item_type
            
            processed_content = self._process_inline_formatting(content)
            html_lines.append(f'<li>{processed_content}</li>')
        
        if current_type:
            html_lines.append(f'</{current_type}>')
        
        return html_lines
    
    def _process_table_line(self, line):
        """处理表格行"""
        # 简单的表格处理
        cells = [cell.strip() for cell in line.split('|')[1:-1]]
        
        # 检查是否是分隔行
        if all(cell.replace('-', '').replace(':', '').strip() == '' for cell in cells):
            return ''  # 跳过分隔行
        
        processed_cells = [self._process_inline_formatting(cell) for cell in cells]
        cell_html = ''.join(f'<td>{cell}</td>' for cell in processed_cells)
        return f'<tr>{cell_html}</tr>'
    
    def _process_inline_formatting(self, text: str) -> str:
        """处理行内格式化"""
        if not text:
            return ""
        
        # 先转义HTML
        text = html.escape(text)
        
        # 处理行内代码（在其他格式之前）
        text = re.sub(r'`([^`]+)`', r'<code>\1</code>', text)
        
        # 处理粗体
        text = re.sub(r'\*\*([^*]+)\*\*', r'<strong>\1</strong>', text)
        
        # 处理斜体
        text = re.sub(r'\*([^*]+)\*', r'<em>\1</em>', text)
        
        # 处理链接
        text = re.sub(r'\[([^\]]+)\]\(([^)]+)\)', r'<a href="\2">\1</a>', text)
        
        # 处理删除线
        text = re.sub(r'~~([^~]+)~~', r'<del>\1</del>', text)
        
        return text