import logging
from typing import Any

from deep_diagnose.core.reasoning.planning.models import SelectedSOP
from deep_diagnose.core.reasoning.planning.storage import <PERSON>OPS<PERSON>
from deep_diagnose.core.reasoning.planning.strategy import SOPSelectionStrategy

logger = logging.getLogger(__name__)


class SOPService:
    """Main service class for SOP selection and retrieval."""

    def __init__(self, store: SOPStore, strategy: SOPSelectionStrategy):
        """Initializes the SOPService with a specific store and selection strategy.

        Args:
            store: An instance of SOPStore to retrieve SOP data.
            strategy: An instance of SOPSelectionStrategy to select the best SOP.
        """
        self.store = store
        self.strategy = strategy
        logger.info(f"SOPService initialized with store: {type(store).__name__} and strategy: {type(strategy).__name__}")

    async def find_sop(self, query: str) -> SelectedSOP:
        """Finds the most appropriate SOP based on the given query.

        Args:
            query: The user's query or problem description.

        Returns:
            A SelectedSOP object containing the chosen SOP and selection details.
        """
        if not query or not query.strip():
            return SelectedSOP(sop=None, reason="Query is empty.", success=False, error_message="Query cannot be empty.")

        try:
            sops = await self.store.get_all_sops()
            if not sops:
                return SelectedSOP(sop=None, reason="No SOPs found in store.", success=False, error_message="No SOPs available for selection.")

            selected_sop = await self.strategy.select_sop(query, sops)
            return selected_sop

        except Exception as e:
            logger.error(f"Error finding SOP for query '{query}': {e}", exc_info=True)
            return SelectedSOP(sop=None, reason=f"An error occurred during SOP selection: {str(e)}", success=False, error_message=str(e))

    async def get_all_sops(self):
        """Retrieves all SOPs from the store.

        Returns:
            List of all available SOPs.
        """
        try:
            return await self.store.get_all_sops()
        except Exception as e:
            logger.error(f"Error retrieving all SOPs: {e}", exc_info=True)
            return []
