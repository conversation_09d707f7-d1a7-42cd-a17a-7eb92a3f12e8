from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional

@dataclass
class SOP:
    """Represents a Standard Operating Procedure."""
    sop_id: str
    name: str
    scenario: str
    template_file_path: str
    example_user_queries: List[str] = field(default_factory=list)
    tool_dependencies: List[str] = field(default_factory=list)
    content: Optional[str] = None # The actual markdown content of the SOP

@dataclass
class SelectedSOP:
    """Represents the result of an SOP selection, including the selected SOP and the reason for selection."""
    sop: Optional[SOP]
    reason: str
    score: Optional[float] = None # e.g., similarity score, LLM confidence
    success: bool = True
    error_message: Optional[str] = None
