import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
import yaml

from deep_diagnose.core.reasoning.planning.models import SOP
from .base import SOPStore

logger = logging.getLogger(__name__)


class FileSystemSOPStore(SOPStore):
    """SOP store implementation that loads SOPs from YAML configuration and markdown files."""

    def __init__(self, config_path: Optional[str] = None):
        """Initializes the FileSystemSOPStore.

        Args:
            config_path: Path to the main SOP configuration YAML file. If None, a default path is used.
        """
        if config_path is None:
            self.config_path = self._get_default_config_path()
        else:
            self.config_path = Path(config_path)

        # Calculate sop_dir correctly based on config file location
        # config_path is like: src/deep_diagnose/prompts/sop/configs/sop_diagnosis_config.yaml
        # sop_dir should be: src/deep_diagnose/prompts/sop
        self.sop_dir = self.config_path.parent.parent
        self._sops: Optional[List[SOP]] = None

        logger.debug(f"FileSystemSOPStore initialized with config path: {self.config_path}")

    @staticmethod
    def _get_default_config_path() -> Path:
        """Determines the default path to the SOP configuration YAML file."""
        current_file = Path(__file__)
        # Path from deep_diagnose/core/reasoning/planning to deep_diagnose/prompts/sop/configs
        # current_file.parent = .../deep_diagnose/core/reasoning/planning
        # current_file.parent.parent = .../deep_diagnose/core/reasoning  
        # current_file.parent.parent.parent = .../deep_diagnose/core
        # current_file.parent.parent.parent.parent = .../deep_diagnose
        return (current_file.parent.parent.parent.parent.parent /
                "prompts" / "sop" / "configs" / "sop_diagnosis_config.yaml")

    def _load_configs(self) -> None:
        """Loads SOP configurations from the YAML file and their content from markdown files."""
        try:
            if not self.config_path.exists():
                logger.warning(f"SOP configuration file not found: {self.config_path}")
                self._sops = []
                return

            with open(self.config_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
                raw_configs = data.get('diagnosis_sops', []) if data else []

            loaded_sops: List[SOP] = []
            for raw_sop in raw_configs:
                sop_id = raw_sop.get("sop_id")
                name = raw_sop.get("name")
                scenario = raw_sop.get("scenario")
                template_file_path = raw_sop.get("template_file_path")
                example_user_queries = raw_sop.get("example_user_queries", [])
                tool_dependencies = raw_sop.get("tool_dependencies", [])

                if not all([sop_id, name, scenario, template_file_path]):
                    logger.warning(f"Skipping malformed SOP config: {raw_sop}")
                    continue

                content = self._load_sop_content(template_file_path)

                loaded_sops.append(SOP(
                    sop_id=sop_id,
                    name=name,
                    scenario=scenario,
                    template_file_path=template_file_path,
                    example_user_queries=example_user_queries,
                    tool_dependencies=tool_dependencies,
                    content=content
                ))
            self._sops = loaded_sops
            logger.info(f"Successfully loaded {len(self._sops)} SOPs from file system.")

        except yaml.YAMLError as e:
            logger.error(f"YAML parsing error for {self.config_path}: {e}")
            self._sops = []
        except Exception as e:
            logger.error(f"Failed to load SOPs from file system: {e}", exc_info=True)
            self._sops = []

    def _load_sop_content(self, file_name: str) -> str:
        """Loads the content of an SOP markdown file."""
        if not file_name or not file_name.strip():
            return ""

        # Skip planner.md as it's not a diagnostic SOP content
        if 'planner.md' in file_name:
            return ""

        try:
            content = self._try_load_from_paths(file_name)
            if not content:
                logger.warning(f"SOP content file not found or empty: {file_name}")
            return content

        except Exception as e:
            logger.error(f"Failed to load SOP content from {file_name}: {e}")
            return ""

    def _try_load_from_paths(self, file_name: str) -> str:
        """Attempts to load a file from multiple potential paths."""
        # Prioritize paths relative to the main config file's parent directory
        # and then relative to the sop_dir (src/deep_diagnose/prompts/sop/)
        paths_to_try = [
            self.config_path.parent / file_name, # e.g., configs/../instance/diagnosis/instance_restart_single.md
            self.sop_dir / file_name, # e.g., sop/instance/diagnosis/instance_restart_single.md
            # Fallback for direct file names if they are in the sop_dir root
            self.sop_dir / Path(file_name).name
        ]

        for path in paths_to_try:
            if path.exists() and path.is_file():
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        return f.read()
                except Exception as e:
                    logger.debug(f"Failed to read file {path}: {e}")
        return ""

    async def get_all_sops(self) -> List[SOP]:
        """Retrieves all SOPs, loading them if not already loaded."""
        if self._sops is None:
            self._load_configs()
        return self._sops or []

    async def get_sop_by_id(self, sop_id: str) -> Optional[SOP]:
        """Retrieves a single SOP by its ID."""
        sops = await self.get_all_sops()
        for sop in sops:
            if sop.sop_id == sop_id:
                return sop
        return None

    @staticmethod
    def get_keyword_config(config_path: Optional[str] = None) -> Dict[str, Any]:
        """Retrieves the keyword matching configuration from the YAML file."""
        if config_path is None:
            current_file = Path(__file__)
            config_path = (current_file.parent.parent.parent.parent.parent /
                           "prompts" / "sop" / "configs" / "sop_diagnosis_config.yaml")
        else:
            config_path = Path(config_path)

        try:
            if not config_path.exists():
                logger.warning(f"Keyword config file not found: {config_path}")
                return {}

            with open(config_path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
                return data.get('keyword_matching', {}) if data else {}

        except yaml.YAMLError as e:
            logger.error(f"YAML parsing error for keyword config {config_path}: {e}")
            return {}
        except Exception as e:
            logger.error(f"Failed to load keyword config from {config_path}: {e}", exc_info=True)
            return {}
