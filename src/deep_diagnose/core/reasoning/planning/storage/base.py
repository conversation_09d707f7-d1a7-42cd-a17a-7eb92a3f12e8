import logging
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, Any, List, Optional
import yaml

from deep_diagnose.core.reasoning.planning.models import SOP

logger = logging.getLogger(__name__)


class SOPStore(ABC):
    """Abstract base class for SOP storage."""

    @abstractmethod
    async def get_all_sops(self) -> List[SOP]:
        """Retrieves all SOPs from the store."""
        pass

    @abstractmethod
    async def get_sop_by_id(self, sop_id: str) -> Optional[SOP]:
        """Retrieves a single SOP by its ID."""
        pass
