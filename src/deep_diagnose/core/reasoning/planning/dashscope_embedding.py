


import os
from typing import List

from langchain_community.embeddings import DashScopeEmbeddings
from langchain_community.embeddings.dashscope import embed_with_retry
from deep_diagnose.common.config import get_config


os.environ['DASHSCOPE_API_KEY'] =get_config().embedding.api_key


class DashScopeEmbeddings(DashScopeEmbeddings):

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """为了适配query search场景定制"""
        embeddings = embed_with_retry(
            self, input=texts, text_type="query", model=self.model
        )
        embedding_list = [item["embedding"] for item in embeddings]
        return embedding_list


    def embed_query(self, text: str) -> List[float]:
        """
        embed_with_retry()有bug，如果input是字符串，会截取[i:i+25]，新版本已修复。
        为了不修改langchain_community版本，这里做适配
        """
        embedding_list = self.embed_documents([text])
        return embedding_list[0]
