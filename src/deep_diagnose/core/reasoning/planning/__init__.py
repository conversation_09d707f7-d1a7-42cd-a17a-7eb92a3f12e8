import logging
from typing import Dict, Any, Optional

# New architecture components
from .service import SOPService
from .storage import FileSystemSOPStore, PGVectorSOPStore
from .strategy import LLMSelectionStrategy, SimilaritySelectionStrategy
from .strategy.embedding_service import DashScopeEmbeddingService
from deep_diagnose.common.config import get_config

logger = logging.getLogger(__name__)

def create_sop_service() -> SOPService:
    """Factory function to create and configure an SOPService instance.

    Returns:
        An initialized SOPService instance.

    Raises:
        ValueError: If sop_service_config is missing or malformed, or if an unknown
                    strategy_type or storage_type is provided.
    """
    config = get_config()
    
    if not hasattr(config, 'sop_service_config') or not config.sop_service_config:
        raise ValueError("sop_service_config is missing in the configuration.")

    sop_service_config = config.sop_service_config
    strategy_type = sop_service_config.get('strategy_type', 'llm')
    storage_type = sop_service_config.get('storage_type', 'filesystem')
    config_for_service = sop_service_config.get('config', {})

    store = None
    strategy = None

    storage_config = config_for_service.get('storage_config') or {}
    strategy_config = config_for_service.get('strategy_config') or {}

    # 1. Initialize Storage
    if storage_type == 'filesystem':
        config_path = storage_config.get('config_path') if storage_config else None
        store = FileSystemSOPStore(config_path)
    elif storage_type == 'pgvector':
        connection_string = storage_config.get('connection_string', '') if storage_config else ''
        collection_name = storage_config.get('collection_name', 'sops') if storage_config else 'sops'
        store = PGVectorSOPStore(connection_string, collection_name)
    else:
        raise ValueError(f"Unknown storage type: {storage_type}. Must be 'filesystem' or 'pgvector'.")

    # 2. Initialize Strategy
    if strategy_type == 'llm':
        strategy = LLMSelectionStrategy()
    elif strategy_type == 'similarity':
        embedding_config_data = strategy_config.get('embedding_config', {})
        # 直接传递配置参数而不是使用EmbeddingConfig类
        embedding_service = DashScopeEmbeddingService(
            model_name=embedding_config_data.get('model'),
            api_key=embedding_config_data.get('api_key')
        )

        strategy = SimilaritySelectionStrategy(
            embedding_service=embedding_service,
            similarity_threshold=strategy_config.get('similarity_threshold', 0.6),
            semantic_weight=strategy_config.get('semantic_weight', 0.7),
            example_weight=strategy_config.get('example_weight', 0.3),
            default_sop_id=strategy_config.get('default_sop_id', 'general_troubleshooting'),
        )
    else:
        raise ValueError(f"Unknown strategy type: {strategy_type}. Must be 'llm' or 'similarity'.")

    if store is None or strategy is None:
        raise RuntimeError("Failed to initialize store or strategy. This should not happen.")

    return SOPService(store, strategy)

# Update __all__ to expose the new factory function
__all__ = [
    "create_sop_service",
]


