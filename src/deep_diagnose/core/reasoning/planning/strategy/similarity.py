import logging
from typing import List, Dict, Any, Optional

import numpy as np

from deep_diagnose.core.reasoning.planning.models import SOP, SelectedSOP
from .base import SOPSelectionStrategy
from .embedding_service import DashScopeEmbeddingService

logger = logging.getLogger(__name__)


class SimilaritySelectionStrategy(SOPSelectionStrategy):
    """SOP selection strategy that uses semantic and example similarity."""

    def __init__(
        self,
        embedding_service: DashScopeEmbeddingService,
        similarity_threshold: float = 0.6,
        semantic_weight: float = 0.7, # Adjusted weight
        example_weight: float = 0.3, # Adjusted weight
        default_sop_id: str = "general_troubleshooting",
    ):
        self.embedding_service = embedding_service
        self.similarity_threshold = similarity_threshold
        self.semantic_weight = semantic_weight
        self.example_weight = example_weight
        self.default_sop_id = default_sop_id

    async def select_sop(self, query: str, sops: List[SOP]) -> SelectedSOP:
        if not sops:
            return SelectedSOP(sop=None, reason="No SOPs available for selection.", success=False)

        try:
            results: List[Dict[str, Any]] = []
            for sop in sops:
                score_breakdown = await self._calculate_similarity(query, sop)
                results.append({
                    "sop": sop,
                    "total_score": score_breakdown["total"],
                    "semantic_score": score_breakdown["semantic"],
                    "example_score": score_breakdown["example"]
                })

            results.sort(key=lambda x: x["total_score"], reverse=True)
            best_result = results[0]
            best_sop = best_result["sop"]

            if best_result["total_score"] >= self.similarity_threshold:
                reason = f"相似度匹配 (分数: {best_result['total_score']:.3f})"
                return SelectedSOP(
                    sop=best_sop,
                    reason=reason,
                    score=best_result["total_score"],
                    success=True
                )
            else:
                # Fallback to default SOP if score is too low
                default_sop = self._get_default_sop(sops)
                reason = f"相似度过低 ({best_result['total_score']:.3f} < {self.similarity_threshold}), 使用默认SOP"
                return SelectedSOP(
                    sop=default_sop,
                    reason=reason,
                    score=best_result["total_score"],
                    success=True # Still a success as a fallback was found
                )

        except Exception as e:
            logger.error(f"Similarity selection failed: {e}", exc_info=True)
            # Fallback to the first SOP on error
            fallback_sop = sops[0]
            return SelectedSOP(sop=fallback_sop, reason=f"相似度选择失败，使用默认SOP: {str(e)}", success=False, error_message=str(e))

    async def _calculate_similarity(self, query: str, sop: SOP) -> Dict[str, float]:
        semantic_score = await self._calculate_semantic_similarity(query, sop)
        example_score = self._calculate_example_similarity(query, sop)

        total_score = (
            semantic_score * self.semantic_weight +
            example_score * self.example_weight
        )
        return {
            "total": min(total_score, 1.0),
            "semantic": semantic_score,
            "example": example_score
        }

    async def _calculate_semantic_similarity(self, query: str, sop: SOP) -> float:
        try:
            sop_text = self._build_sop_text(sop)
            query_embedding = await self.embedding_service.get_embedding(query)
            sop_embedding = await self.embedding_service.get_embedding(sop_text)
            similarity = self.embedding_service.calculate_similarity(query_embedding, sop_embedding)
            return similarity
        except Exception as e:
            logger.warning(f"Semantic similarity calculation failed for SOP {sop.sop_id}: {e}")
            return 0.0

    def _calculate_example_similarity(self, query: str, sop: SOP) -> float:
        try:
            query_lower = query.lower()
            max_score = 0.0

            examples = sop.example_user_queries
            for example in examples:
                if isinstance(example, str):
                    example_lower = example.lower()
                    similarity = self._calculate_text_overlap(query_lower, example_lower)
                    max_score = max(max_score, similarity)

            return max_score

        except Exception as e:
            logger.warning(f"Example similarity calculation failed for SOP {sop.sop_id}: {e}")
            return 0.0

    def _calculate_text_overlap(self, text1: str, text2: str) -> float:
        if not text1 or not text2:
            return 0.0

        words1 = set(text1.split())
        words2 = set(text2.split())

        if not words1 or not words2:
            return 0.0

        intersection = len(words1 & words2)
        union = len(words1 | words2)

        return intersection / union if union > 0 else 0.0

    def _build_sop_text(self, sop: SOP) -> str:
        parts = []
        if sop.name:
            parts.append(sop.name)
        if sop.scenario:
            scenario = sop.scenario
            if isinstance(scenario, str):
                scenario_clean = scenario.replace('\n', ' ').strip()
                parts.append(scenario_clean)
        if sop.example_user_queries:
            parts.extend(sop.example_user_queries[:3])
        return " ".join(parts)

    def _get_default_sop(self, sops: List[SOP]) -> Optional[SOP]:
        for sop in sops:
            if sop.sop_id == self.default_sop_id:
                return sop
        return sops[0] if sops else None # Fallback to first SOP if default not found
