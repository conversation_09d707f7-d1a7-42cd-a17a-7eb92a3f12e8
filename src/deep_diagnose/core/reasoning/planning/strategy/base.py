from abc import ABC, abstractmethod
from typing import List

from deep_diagnose.core.reasoning.planning.models import SOP, SelectedSOP


class SOPSelectionStrategy(ABC):
    """Abstract base class for SOP selection strategies."""

    @abstractmethod
    async def select_sop(self, query: str, sops: List[SOP]) -> SelectedSOP:
        """Selects the most appropriate SOP based on the query and available SOPs."""
        pass
