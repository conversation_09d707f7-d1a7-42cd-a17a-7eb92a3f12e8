"""Graph工厂模块

根据类型创建和返回相应的Graph实例。
"""

from typing import Dict, Any, Optional

from .base_graph import BaseGraph


class AgentFactory:
    """
    根据类型创建和返回相应的 Graph 实例。
    """

    # 简单的注册表，避免硬编码
    _agents = {}

    @classmethod
    def register(cls, name: str, agent_class: type):
        """注册Agent类型"""
        cls._agents[name] = agent_class

    @staticmethod
    def create_agent(agent_name: str = "ReasoningAgent", config: Optional[Dict[str, Any]] = None) -> BaseGraph:
        """
        创建 Graph 实例。

        Args:
            agent_name: Graph 的类型标识符
            config: 传递给 Graph 实例的配置

        Returns:
            BaseGraph: 一个具体的 Graph 实例

        Raises:
            ValueError: 如果 agent_name 无效
        """
        # 如果请求的 Agent 不存在，尝试重新注册一次
        if agent_name not in AgentFactory._agents:
            _register_agents()
        
        agent_class = AgentFactory._agents.get(agent_name)
        if not agent_class:
            available = list(AgentFactory._agents.keys())
            raise ValueError(f"未知的 Graph 类型: {agent_name}. 可用类型: {available}")

        return agent_class(config=config)

    @classmethod
    def list_available_agents(cls):
        """List available agents"""
        return list(cls._agents.keys())

def _register_agents():
    """Register agents to AgentFactory"""
    try:
        from deep_diagnose.core.reasoning.reasoning_agent import ReasoningAgent
        AgentFactory.register("ReasoningAgent", ReasoningAgent)
    except ImportError as e:
        print(f"Error registering ReasoningAgent: {e}")

    try:
        from deep_diagnose.core.interactive.agents.inspect import InspectAgent
        AgentFactory.register("InspectAgent", InspectAgent)
    except ImportError as e:
        print(f"Error registering InspectAgent: {e}")

    try:
        from deep_diagnose.core.interactive.interactive_agent import InteractiveAgent
        AgentFactory.register("InteractiveAgent", InteractiveAgent)
    except ImportError as e:
        print(f"Error registering InteractiveAgent: {e}")

    try:
        from deep_diagnose.core.interactive.aone_agent import AoneAgent
        AgentFactory.register("AoneAgent", AoneAgent)
    except ImportError as e:
        print(f"Error registering AoneAgent: {e}")


# 延迟注册，避免模块级别的循环导入
_register_agents()
