"""
Inspect Workflow Implementation

LangGraph-based Inspect workflow with Langfuse automatic tracking support.
Provides comprehensive machine inspection capabilities with streaming output.
"""

import re
from langfuse.callback.langchain import LangchainCallbackHandler
import logging
import uuid
from datetime import datetime
from typing import AsyncGenerator, Optional, Dict, Any, Tuple
from uuid import uuid4

from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import StateGraph, START, END
from langchain_core.messages import HumanMessage

from deep_diagnose.common.config import get_config
from deep_diagnose.common.utils.machine_utils import MachineIdUtil, MachineIdType
from deep_diagnose.core.events.inspect_event import InspectEvent
from deep_diagnose.core.interactive.types.state import InspectState
from deep_diagnose.core.interactive.nodes import (
    data_collection_node,
    scene_recognition_node,
    overview_generation_node,
    recommendation_generation_node,
)

logger = logging.getLogger(__name__)


class FilteredLangfuseCallback(LangchainCallbackHandler):
    """
    Filtered Langfuse Callback Handler for Non-Serializable Data

    Addresses "Expected object, received string" errors by:
    1. Filtering non-serializable objects (enums, callback handlers, etc.)
    2. Retaining only essential serializable data
    3. Ensuring proper data format for Langfuse transmission
    """

    def on_chain_start(self, serialized, inputs, **kwargs):
        """Filter input data when chain starts"""
        try:
            filtered_inputs = self._filter_data(inputs)
            # 确保返回字典类型
            if not isinstance(filtered_inputs, dict):
                filtered_inputs = {"data": filtered_inputs}
            super().on_chain_start(serialized, filtered_inputs, **kwargs)
        except Exception as e:
            logger.warning(f"Failed to filter chain start inputs: {e}")
            super().on_chain_start(serialized, {"filtered": True}, **kwargs)

    def on_chain_end(self, outputs, **kwargs):
        """Filter output data when chain ends"""
        try:
            filtered_outputs = self._filter_data(outputs)
            # 确保返回字典类型
            if not isinstance(filtered_outputs, dict):
                filtered_outputs = {"data": filtered_outputs}
            super().on_chain_end(filtered_outputs, **kwargs)
        except Exception as e:
            logger.warning(f"Failed to filter chain end outputs: {e}")
            super().on_chain_end({"filtered": True}, **kwargs)

    def on_llm_start(self, serialized, prompts, **kwargs):
        """Filter prompt data when LLM starts"""
        # Prompts are usually string lists and should be safe
        super().on_llm_start(serialized, prompts, **kwargs)

    def on_llm_end(self, response, **kwargs):
        """Filter response data when LLM ends"""
        try:
            # For LLM responses, pass through without filtering as types are typically correct
            super().on_llm_end(response, **kwargs)
        except Exception as e:
            logger.warning(f"Failed to process LLM end event: {e}")
            # Skip this event if it fails

    def _filter_data(self, data):
        """
        Filter data to remove non-serializable objects

        Args:
            data: Raw data to filter

        Returns:
            Filtered data safe for serialization
        """
        if data is None:
            return None

        try:
            if isinstance(data, dict):
                return self._filter_dict(data)
            elif isinstance(data, list):
                return [self._filter_data(item) for item in data]
            elif isinstance(data, tuple):
                return tuple(self._filter_data(item) for item in data)
            elif hasattr(data, "__dict__"):
                # For objects with __dict__, convert to dictionary and filter
                return self._filter_dict(data.__dict__)
            else:
                # For basic types, return directly
                return data
        except Exception as e:
            logger.warning(f"Data filtering failed: {e}, returning summary info")
            return {"type": str(type(data)), "summary": "过滤后的数据"}

    def _filter_dict(self, data_dict):
        """
        Filter dictionary data

        Args:
            data_dict: Dictionary data to filter

        Returns:
            Filtered dictionary safe for serialization
        """
        if not isinstance(data_dict, dict):
            return data_dict

        filtered = {}
        for key, value in data_dict.items():
            try:
                # Skip specific non-serializable fields
                if key in ["langfuse_handler", "callbacks", "handler"]:
                    continue

                # Handle enum types
                if hasattr(value, "value") and hasattr(value, "name"):
                    filtered[key] = value.value
                    continue

                # Handle LangChain message objects
                if hasattr(value, "content") and hasattr(value, "type"):
                    filtered[key] = {
                        "type": getattr(value, "type", "unknown"),
                        "content": getattr(value, "content", ""),
                    }
                    continue

                # Recursive filtering
                filtered[key] = self._filter_data(value)

            except Exception as e:
                logger.debug(f"Skipping field {key}: {e}")
                # If field cannot be serialized, record type information
                filtered[f"{key}_type"] = str(type(value))

        return filtered


def create_inspect_workflow():
    """
    Create stateless Inspect workflow

    Returns:
        CompiledGraph: Compiled workflow graph without memory checkpoints
    """
    builder = StateGraph(state_schema=InspectState)

    # Add workflow nodes
    builder.add_node("data_collection", data_collection_node)
    builder.add_node("scene_recognition", scene_recognition_node)
    builder.add_node("overview_generation", overview_generation_node)  # 使用概览生成节点
    builder.add_node("recommendation_generation", recommendation_generation_node)

    # Define workflow edges
    builder.add_edge(START, "data_collection")
    builder.add_edge("data_collection", "scene_recognition")
    builder.add_edge("scene_recognition", "overview_generation")
    builder.add_edge("overview_generation", "recommendation_generation")
    builder.add_edge("recommendation_generation", end_key=END)

    # Compile workflow graph
    graph = builder.compile()

    logger.info("Inspect workflow created successfully (stateless) - using overview generation node")
    return graph


def create_inspect_workflow_with_memory():
    """
    Create stateful Inspect workflow with memory checkpoints

    Returns:
        CompiledGraph: Compiled workflow graph with memory checkpoints
    """
    builder = StateGraph(InspectState)

    # Add workflow nodes
    builder.add_node("data_collection", data_collection_node)
    builder.add_node("scene_recognition", scene_recognition_node)
    builder.add_node("overview_generation", overview_generation_node)  # 使用概览生成节点
    builder.add_node("recommendation_generation", recommendation_generation_node)

    # Define workflow edges
    builder.add_edge(START, "data_collection")
    builder.add_edge("data_collection", "scene_recognition")
    builder.add_edge("scene_recognition", "overview_generation")
    builder.add_edge("overview_generation", "recommendation_generation")
    builder.add_edge("recommendation_generation", END)

    # Compile workflow with memory checkpoints
    checkpointer = MemorySaver()
    graph = builder.compile(checkpointer=checkpointer)

    logger.info("Inspect workflow created successfully (stateful) - using overview generation node")
    return graph


def create_langfuse_handler(
    session_id: Optional[str] = None,
    user_id: Optional[str] = None,
    trace_name: Optional[str] = None,
) -> Optional[FilteredLangfuseCallback]:
    """
    Create Langfuse handler with LangGraph automatic tracking support and data filtering

    Args:
        session_id: Session identifier
        user_id: User identifier  
        trace_name: Trace name for identification

    Returns:
        FilteredLangfuseCallback: Langfuse handler instance, None if configuration fails
    """
    try:
        config = get_config()

        # Safely retrieve configuration values
        public_key = str(config.observability.langfuse.public_key) if config.observability.langfuse.public_key else None
        secret_key = str(config.observability.langfuse.secret_key) if config.observability.langfuse.secret_key else None
        host = (
            str(config.observability.langfuse.endpoint)
            if config.observability.langfuse.endpoint
            else "https://cloud.langfuse.com"
        )

        if not public_key or not secret_key:
            logger.warning("Missing Langfuse configuration, skipping handler creation")
            return None

        # Create filtered callback handler
        handler = FilteredLangfuseCallback(
            public_key=public_key,
            secret_key=secret_key,
            host=host,
            session_id=session_id,
            user_id=user_id,
            trace_name=trace_name or "inspect_workflow",
        )
        logger.info(f"Langfuse FilteredLangfuseCallback created successfully, trace_name: {trace_name}")
        return handler
    except Exception as e:
        logger.warning(f"Failed to create Langfuse handler: {e}")
        return None


async def stream_inspect_workflow(
    machine_id: str,
    start_time: str,
    end_time: str,
    session_id: Optional[str] = None,
    user_id: Optional[str] = None,
    trace_id: Optional[str] = None,
    with_memory: bool = False,
    langfuse_handler: Optional[FilteredLangfuseCallback] = None,
) -> AsyncGenerator[InspectEvent, None]:
    """
    Execute Inspect workflow with streaming output

    Args:
        machine_id: Machine identifier
        start_time: Analysis start time
        end_time: Analysis end time
        session_id: Session identifier
        user_id: User identifier
        trace_id: Trace identifier
        with_memory: Whether to use memory checkpoints
        langfuse_handler: Optional external Langfuse handler

    Yields:
        InspectEvent: Workflow execution events
    """
    logger.info(f"Starting Inspect workflow streaming execution for machine: {machine_id}")

    # Create workflow instance
    if with_memory:
        workflow = create_inspect_workflow_with_memory()
    else:
        workflow = create_inspect_workflow()

    # Create or use external Langfuse handler
    if langfuse_handler is None:
        # No external handler provided, create new one
        langfuse_handler = create_langfuse_handler(
            session_id=session_id,
            user_id=user_id,
            trace_name=f"inspect_test_{machine_id}",
        )
        logger.info(f"Created new Langfuse handler, trace_name: inspect_test_{machine_id}")
    else:
        # Use external handler
        logger.info("Using external Langfuse handler")

    # Initialize workflow state (use dictionary format for LangGraph compatibility, ensure all values are serializable)
    initial_state = {
        "machine_id": machine_id,
        "start_time": start_time,
        "end_time": end_time,
        "machine_id_type": MachineIdUtil.get_machine_id_type(machine_id).value,  # 使用 .value 获取字符串值
        "trace_id": trace_id,
        "session_id": session_id,
        "user_id": user_id,
        "messages": [HumanMessage(content=f"请分析机器 {machine_id} 的状态")],
        "current_step": "start",
    }

    # Safely retrieve machine_id_type value (as string)
    machine_id_type_value = "unknown"
    try:
        # Use dictionary access, machine_id_type is now a string
        if "machine_id_type" in initial_state:
            machine_id_type_value = initial_state["machine_id_type"]
        # Directly use MachineIdUtil to get value
        else:
            machine_id = initial_state.get("machine_id", "")
            machine_id_type_enum = MachineIdUtil.get_machine_id_type(machine_id)
            machine_id_type_value = machine_id_type_enum.value
    except Exception as e:
        logger.warning(f"Failed to retrieve machine_id_type value: {e}, using default 'unknown'")

    config: Dict[str, Any] = {
        "metadata": {
            "langfuse_user_id": user_id,
            "langfuse_session_id": session_id,
            "langfuse_tags": ["inspect", "diagnostic", machine_id_type_value],
        }
    }

    if langfuse_handler:
        config["callbacks"] = [langfuse_handler]
        logger.info("LangGraph automatic Langfuse tracking enabled")
    else:
        logger.warning("No Langfuse handler provided, skipping automatic tracking")

    if with_memory:
        config["configurable"] = {"thread_id": session_id}

    # Create initial event
    inspect_event = InspectEvent()

    # Safely retrieve machine_id_type (now as string value)
    machine_id_type_for_event = MachineIdType.UNKNOWN
    try:
        # Use dictionary access, machine_id_type is now a string
        if "machine_id_type" in initial_state:
            machine_id_type_str = initial_state["machine_id_type"]
            try:
                machine_id_type_for_event = MachineIdType(machine_id_type_str)
            except (ValueError, TypeError):
                machine_id_type_for_event = MachineIdType.UNKNOWN
        # Directly use MachineIdUtil to get value
        else:
            machine_id = initial_state.get("machine_id", "")
            machine_id_type_for_event = MachineIdUtil.get_machine_id_type(machine_id)
    except Exception as e:
        logger.warning(f"Failed to retrieve machine_id_type: {e}, using default UNKNOWN")
        machine_id_type_for_event = MachineIdType.UNKNOWN

    inspect_event.set_machine_info(machine_id, start_time, end_time, machine_id_type=machine_id_type_for_event)
    inspect_event.update_overview_content(content="正在查询资源运行状态，生成概览...")

    # Send initial event
    yield inspect_event

    try:
        # Execute workflow with streaming
        logger.info("Starting workflow streaming execution...")
        event_count = 0

        # Use custom mode to capture get_stream_writer output
        async for event in workflow.astream(initial_state, config=config, stream_mode=["custom", "updates"]):  # type: ignore

            event_count += 1
            logger.debug(f"Received workflow event {event_count}: {type(event)} - {event}")

            # Process workflow event and convert to InspectEvent
            updated_event = _convert_workflow_event_to_inspect_event(event, inspect_event)
            if updated_event:
                logger.debug(f"Event {event_count} updated, yielding updated event")
                yield updated_event
            else:
                logger.debug(f"Event {event_count} no updates, skipping")

        logger.info(f"Workflow streaming execution completed, total events received: {event_count}")

        # Send completion event
        inspect_event.finished = True

        # Record final results to Langfuse - handled automatically by LangGraph CallbackHandler
        # No manual recording needed

        yield inspect_event

        logger.info(f"Inspect workflow execution completed for machine: {machine_id}")

    except Exception as e:
        logger.error(f"Inspect workflow execution failed: {e}", exc_info=True)
        inspect_event.finished = True
        inspect_event.overview = f"执行失败: {str(e)}"
        yield inspect_event

    finally:
        # Cleanup Langfuse handler
        if langfuse_handler:
            try:
                langfuse_handler.flush()
            except Exception as e:
                logger.warning(f"Failed to cleanup Langfuse handler: {e}")


def _convert_workflow_event_to_inspect_event(
    workflow_event: Tuple[str, Any], inspect_event: InspectEvent
) -> Optional[InspectEvent]:
    """
    Convert workflow events to InspectEvent
    Supports two modes:
    1. stream_mode="updates": workflow_event contains state updates
    2. stream_mode="custom": workflow_event contains custom streaming data

    Args:
        workflow_event: LangGraph workflow event
        inspect_event: InspectEvent to update

    Returns:
        InspectEvent: Updated event, None if no updates
    """
    try:
        updated = False
        event_type, event_data = workflow_event
        # Check for custom streaming data (stream_mode="custom")
        if isinstance(event_data, dict) and "type" in event_data:
            updated = _process_custom_stream_event(event_data, inspect_event)

        # Check for state updates (stream_mode="updates")
        elif isinstance(event_data, dict):
            updated = _process_updates_stream_event(event_data, inspect_event)

        return inspect_event if updated else None

    except Exception as e:
        logger.warning(f"Failed to convert workflow event: {e}")
        return None


def _process_custom_stream_event(event_data: Dict[str, Any], inspect_event: InspectEvent) -> bool:
    """
    Process custom streaming data (stream_mode="custom")

    Args:
        event_data: Custom streaming data
        inspect_event: InspectEvent to update

    Returns:
        bool: Whether any updates were made
    """
    try:
        updated = False

        # Process overview status updates
        if event_data.get("type") == "overview_status":
            status = event_data.get("status", "")
            message = event_data.get("message", "")

            if status in ["started", "generating", "completed", "error"]:
                inspect_event.overview = message
                updated = True

            # Record metadata
            if "strategy" in event_data:
                setattr(inspect_event, "generation_strategy", event_data["strategy"])
            if "duration" in event_data:
                setattr(inspect_event, "generation_duration", event_data["duration"])
            if "chunk_count" in event_data:
                setattr(inspect_event, "chunk_count", event_data["chunk_count"])

            logger.debug(f"Processed overview status update: {status} - {message}")

        # Process overview content updates
        elif event_data.get("type") == "overview_content":
            content = event_data.get("content", "")
            overview = event_data.get("overview", "")

            if overview:
                inspect_event.overview = overview
                updated = True
                logger.debug(f"Processed overview content update: +{len(content)} chars, total length: {len(overview)}")

            # Record metadata
            if "chunk_count" in event_data:
                setattr(inspect_event, "chunk_count", event_data["chunk_count"])
            if "overview_length" in event_data:
                setattr(inspect_event, "overview_length", event_data["overview_length"])

        return updated

    except Exception as e:
        logger.warning(f"Failed to process custom stream event: {e}")
        return False


def _process_updates_stream_event(event_data: Dict[str, Any], inspect_event: InspectEvent) -> bool:
    """
    Process state update streaming data (stream_mode="updates")

    Args:
        event_data: State update data
        inspect_event: InspectEvent to update

    Returns:
        bool: Whether any updates were made
    """
    try:
        updated = False

        # Iterate through all node results in the workflow event
        for node_name, node_result in event_data.items():
            # Check if it's a state dictionary
            if isinstance(node_result, dict):
                # Directly update overview (if exists)
                if "overview" in node_result:
                    inspect_event.overview = node_result["overview"]
                    updated = True

                # Directly update recommendations (if exists)
                if "recommendations" in node_result:
                    recommendations = node_result["recommendations"]
                    if recommendations:
                        # Extract recommendation summary information
                        inspect_event.recommendations = _extract_recommendation_summary(recommendations)
                    updated = True

                # Directly update completion status (if exists)
                if "finished" in node_result:
                    inspect_event.finished = node_result["finished"]
                    updated = True

                # Directly update current step (if exists)
                if "current_step" in node_result:
                    current_step = node_result["current_step"]
                    if current_step and not inspect_event.finished:
                        step_message = _get_step_progress_message(current_step)
                        # Only show progress message when overview is empty
                        if not inspect_event.overview:
                            inspect_event.overview = step_message
                        # Record last processed step
                        setattr(inspect_event, "_last_step", current_step)
                    updated = True

                # Directly update info data (if exists and in debug mode)
                if "info_data" in node_result and logger.level <= logging.DEBUG:
                    info_data = node_result["info_data"]
                    if info_data:
                        # Extract data summary
                        inspect_event.info_data = _extract_data_summary(info_data)
                    updated = True

        return updated

    except Exception as e:
        logger.warning(f"Failed to process state update event: {e}")
        return False


def _extract_recommendation_summary(recommendations: list) -> list:
    """
    Extract recommendation summary information, removing redundant data

    Args:
        recommendations: Complete recommendation list

    Returns:
        list: Condensed recommendation summary
    """
    summary_recommendations = []

    for rec in recommendations[:5]:
        if isinstance(rec, dict):

            summary_rec = {
                "content": rec.get("content", "")[:100],  # Limit length
                "source": rec.get("source", "unknown"),
                "raw_content": rec.get("raw_content", ""),
                "confidence": rec.get("confidence", 0.0),
            }

            # Try to extract time range from recommendation content like this: 展示一下宿主机的详细信息 (2025-09-02 00:00:00 - 2025-09-02 01:00:00)
            time_range = _extract_time_range(rec.get("content", ""))
            if time_range:
                # Remove ( ) content in raw_content with regex
                plain_content = re.sub(r'\(.*?\)', '', rec.get("raw_content", ""))
                summary_rec["time_range"] = time_range
                summary_rec["content"] = plain_content

            # Optional fields
            if "url" in rec:
                summary_rec["url"] = rec["url"]
            if "score" in rec and isinstance(rec["score"], (int, float)):
                summary_rec["score"] = round(rec["score"], 2)

            summary_recommendations.append(summary_rec)
        elif isinstance(rec, str):
            # Simplified handling for string recommendations
            summary_recommendations.append({"content": rec[:100], "source": "text"})

    return summary_recommendations

def _extract_time_range(content: str) -> str:
    """
    Extract time range from content, prioritizing patterns like '2025-09-02 00:00:00 - 2025-09-02 01:00:00',
    or extracting the first time point if no range is found
    """
    # Priority: extract time range
    range_match = re.search(
        r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\s*-\s*(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})',
        content
    )
    if range_match:
        return f"{range_match.group(1)} - {range_match.group(2)}"
    # Otherwise extract first time point
    time_match = re.search(r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}', content)
    return time_match.group(0) if time_match else ""


def _extract_data_summary(info_data: dict) -> dict:
    """
    Extract data summary statistics for debugging purposes

    Args:
        info_data: Complete information data

    Returns:
        dict: Data summary statistics
    """
    summary = {
        "data_sources": list(info_data.keys()),
        "total_size": len(str(info_data)),
    }

    # Count entries for each data source
    for key, value in info_data.items():
        if isinstance(value, list):
            summary[f"{key}_count"] = len(value)
        elif isinstance(value, dict):
            summary[f"{key}_keys"] = len(value.keys())

    return summary


def _get_step_progress_message(step: str) -> str:
    """
    Get step progress message

    Args:
        step: Current step name

    Returns:
        str: Progress message
    """
    step_messages = {
        "start": "正在初始化...",
        "data_collection": "正在收集机器信息...",
        "scene_recognition": "正在识别故障场景...",
        "overview_generation": "正在生成状态概览...",
        "recommendation_generation": "正在生成后续建议...",
    }

    return step_messages.get(step, f"正在执行 {step}...")
