import json
import logging

from langchain_core.tools.base import BaseTool

from deep_diagnose.common.utils import string_utils
from deep_diagnose.tools.mcp.mcp_tool_manager import MCPToolManager

logger = logging.getLogger(__name__)


class Tool:

    def __init__(self, tool: BaseTool, authority: str, server_name: str):
        self.tool = tool
        self.authority = authority
        self.server_name = server_name
        # 使用None作为未计算的标记
        self._description = None
        self._input_description = None
        self._output_description = None

    def get_description(self) -> str:
        if self._description is None:
            if self.server_name == "vm_migration":
                self._description = string_utils.replace_newlines_with_spaces(self.tool.description)
            else:
                self._description = string_utils.get_first_non_empty_line(self.tool.description)
        return self._description

    def get_input_description(self) -> str:
        if self._input_description is None:
            if self.server_name == "vm_migration":
                self._input_description = str(self.tool.args_schema)
            else:
                self._input_description = string_utils.substring_before(self.tool.description, "Returns:")
        return self._input_description

    def get_output_description(self) -> str:
        if self._output_description is None:
            if self.server_name == "vm_migration":
                self._output_description = ""
            else:
                self._output_description = string_utils.substring_after(self.tool.description, "Returns:")
        return self._output_description

    def get_name(self) -> str:
        return self.tool.name

    async def ainvoke(self, args: dict):
        result = await self.tool.ainvoke(args)
        return self._post_process(self.tool.name, result)

    def _post_process(self, tool_name, result):
        # 调用特定的过滤方法
        filter_method_name = f"_post_{tool_name}"
        if hasattr(self, filter_method_name):
            filter_method = getattr(self, filter_method_name)
            return filter_method(result)

        # 默认返回原始结果
        return result

    def _post_listReportedOperationalEvents(self, result):
        # 开发权限直接返回
        if self.authority == "dev":
            return result
        # 仅针对TAM权限过滤输出中的敏感字段
        try:
            obj = json.loads(result)
            for event in obj:
                if "reason" in event:
                    event['reason'] = string_utils.substring_before(event['reason'], '命中')
            return json.dumps(obj, ensure_ascii=False)
        except Exception as _:
            logger.warning(f"Failed in _filter_listReportedOperationalEvents: {result}")
            return result

    def _post_runDiagnose(self, result):
        # 开发权限直接返回
        if self.authority == "dev":
            return result
        # 仅针对TAM权限过滤输出中的敏感字段
        try:
            obj = json.loads(result)
            del obj['message']
            if "desc" in obj:
                obj['desc'] = string_utils.substring_before(obj['desc'], ':')
            return json.dumps(obj, ensure_ascii=False)
        except Exception as _:
            logger.warning(f"Failed in _filter_runDiagnose: {result}")
            return result

    @staticmethod
    def _post_query_vm_live_migration_eligibility(result):
        result = result.replace('热迁移风险等级为3，不能进行热迁移', '热迁移风险等级为3，客户肯定有损，迁移风险很大，一定需要客户授权')
        return result


class ToolProvider:
    # MCP工具白名单，包含了一系列允许执行的操作或功能
    mcp_safe_tools = [
        "runLiveMigrationCheck", "runColdMigrationCheck", "listLiveMigrationRecords", "listColdMigrationRecords",
        "runOpsEventPostpone", "runOperationSubmit", "listChangeRecords", "listActionTrail",
        "listReportedOperationalEvents", "runVmStartStopDiagnose", "runScreenShotDiagnose",
        "runPerformanceDiagnose", "runDiagnose", "listHealthStatus", "listOnDutyStaffs", "getDiskInfo",
        "listVmHostHistory", "getUserInfo", "getNcBasicInfo", "getVmBasicInfo",
        "listMonitorExceptions", "listKnowledge", "query_vm_live_migration_eligibility",
        "query_live_migration_performance_metrics", "query_live_migration_advanced_features"
    ]
    mcp_privileged_tools = [
        "listOperationRuleMatchRecords", "listVmsOnNc", "listOperationRecords"
    ]

    def __init__(self, authority: str):
        self.authority = authority
        self.tool_map: dict[str, Tool] = {}

    async def setup(self):
        await self._create_tool_map(self.authority)

    async def _create_tool_map(self, authority: str):
        # 根据权限级别确定MCP工具白名单
        match authority:
            case "tam":
                mcp_whitelist = self.mcp_safe_tools
            case "dev":
                mcp_whitelist = self.mcp_safe_tools + self.mcp_privileged_tools
            case _:
                raise ValueError("Invalid authority")

        # 获取所有启用的MCP工具字典
        raw_mcp_dict = await MCPToolManager().get_enabled_mcp_tools_dict()

        # 遍历所有服务器的工具，将白名单中的工具添加到工具映射表中
        for server_name, tools in raw_mcp_dict.items():
            for tool in tools:
                if tool.name in mcp_whitelist:
                    self.tool_map[tool.name] = Tool(tool, authority, server_name)

    def get_tool(self, tool_name: str) -> Tool | None:
        return self.tool_map.get(tool_name, None)

    def get_tool_num(self) -> int:
        return len(self.tool_map)

    def get_tool_name_set(self) -> set:
        return set(self.tool_map.keys())

    def get_tool_input_description(self, tool_name: str) -> str:
        return self.get_tool(tool_name).get_input_description()

    def get_tool_output_description(self, tool_name: str) -> str:
        return self.get_tool(tool_name).get_output_description()

    def get_tool_summary(self) -> str:
        summaries = []
        for name, tool in self.tool_map.items():
            summaries.append(f"- {name}: {tool.get_description()}")
        return str.join("\n", summaries)
