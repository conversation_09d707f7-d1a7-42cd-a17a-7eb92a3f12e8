import re
from typing import Optional

from langchain_core.tools.base import BaseTool

from deep_diagnose.common.utils.string_utils import contains_keywords


class ContextualParamManager:
    """
    ContextualParamManager类用于管理参数，基于上下文进行参数验证、参数提取等操作。
    """

    # 各种参数需要符合的规则（正则表达式）
    resource_pattern_map = {
        "uid": r'\b\d{5,8}|\d{16}\b(?!-)',
        "eventId": r'\be-[a-z0-9-]+\b(?!-)',
        "diskId": r'\bd-[a-z0-9-]+\b(?!-)',
        "instanceId": r'\b(?:i-|hbm-|AY|eci-|cp-|ic-|ay|acs-)[a-z0-9]+\b(?!-)',
        "ncIp": r'\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b(?!-)',
        "ncId": r'\b(?:\d{4,5}-\d{1,5})\b(?!-)'
    }
    resource_pattern_map["nc"] = resource_pattern_map['ncId'] + "|" + resource_pattern_map['ncIp']
    resource_pattern_map["machineId"] = resource_pattern_map['nc'] + "|" + resource_pattern_map['instanceId']

    time_pattern = r'\b\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\b'
    markdown_link_pattern = r'\[([^\]]+)\]\(([^)]+)\)'
    url_pattern = r'\bhttps?://(?:[-\w.])+(?:[:\d]+)?(?:/(?:[-\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:[\w.])*)?)?\b'

    def __init__(self):
        pass

    def validate_params(self, params: dict, context: str) -> str | None:
        """
        验证参数中的关键字段值是否存在于上下文中。

        该函数主要用于确保参数中与虚拟机（VM）、宿主机（NC）、实例或机器相关的字段值
        在给定的上下文中存在，以防止无效的参数传入工具调用。

        参数:
            params (dict): 需要验证的参数字典。
            context (str): 上下文字符串，通常包含有效的字段值（如VM ID、NC IP等）。

        返回:
            Optional[str]: 如果发现无效字段值，返回错误信息；否则返回 None。
        """

        # 确保参数为字典类型
        if not isinstance(params, dict):
            return "Generated param is not dict"

        # 定义需验证的关键字集合
        keywords = list(self.resource_pattern_map.keys())

        # 遍历参数字典中的每一个键值对
        for key in params:
            # 如果键名不含关键字，则跳过
            contained_keyword = contains_keywords(key, keywords)
            if not contained_keyword:
                continue
            param_value = params[key]
            # 统一转为列表处理
            values = [param_value] if isinstance(param_value, str) else param_value
            # 具体执行校验
            for item in values:
                error = self._validate_param_value(contained_keyword, item, context)
                if error is not None:
                    return error
        # 所有参数验证通过
        return None

    def _validate_param_value(self, param_key: str, param_value: str, context: str) -> str | None:
        if param_value not in context:
            return f"{param_value} is not found in context"
        if re.fullmatch(self.resource_pattern_map[param_key], param_value, flags=re.ASCII) is None:
            return f"{param_value} does not match regex pattern"
        return None

    def sanitize_content(self, content: str, context: str) -> str:
        """
        从content中找到所有的URL，检查其是否在context中曾经出现过。
        如果不曾出现过，则将其加上删除线（前后都加上~~）。
        如果URL是Markdown格式的超链接，则将其整个加删除线。

        参数:
            content (str): 原始内容
            context (str): 上下文字符串，用于验证URL是否合法

        返回:
            str: 处理后的内容
        """

        # 处理普通URL
        def process_plain_url(match):
            url = match.group(0)

            # 检查URL是否在上下文中出现过
            if url not in context:
                return f" ~~{url}~~ "
            return url

        # 处理普通URL，使用负向前瞻避免匹配Markdown链接中的URL
        content = re.sub(self.url_pattern, process_plain_url, content, flags=re.ASCII)

        # 再处理Markdown格式的链接，注意链接中的URL可能已经被加了删除线，需要去除
        def process_markdown_link(match):
            full_match = match.group(0)
            link_name = match.group(1).strip("~ ")
            url = match.group(2).strip("~ ")

            # 检查URL是否在上下文中出现过
            if url not in context:
                return f" ~~[{link_name}]({url})~~ "
            return full_match

        # 处理Markdown链接
        content = re.sub(self.markdown_link_pattern, process_markdown_link, content, flags=re.ASCII)

        return content

    def extract_param(self, context: str, tool: BaseTool) -> Optional[dict]:
        """
        从上下文中提取工具所需的参数值。

        参数:
            context (str): 上下文字符串，用于提取参数值
            tool (BaseTool): 工具对象，包含所需的参数信息

        返回:
            dict | None: 提取成功时返回参数字典，失败时返回None
        """
        result = {}

        # 获取工具的所有参数名
        tool_param_names = tool.args.keys()

        # 构造关键词列表
        keywords = list(self.resource_pattern_map.keys()) + ["startTime", "endTime"]

        # 检查所有参数是否都包含关键词
        for param_name in tool_param_names:
            contained_keyword = contains_keywords(param_name, keywords)
            if not contained_keyword:
                return None

        # 为每个参数提取值
        for param_name in tool_param_names:
            contained_keyword = contains_keywords(param_name, keywords)

            if contained_keyword in ["startTime", "endTime"]:
                # 时间参数提取
                matches = list(set(re.findall(self.time_pattern, context, flags=re.ASCII)))
                if len(matches) == 1:
                    result[param_name] = matches[0]
                elif len(matches) == 2:
                    if contained_keyword == "startTime":
                        result[param_name] = min(matches[0], matches[1])
                    else:
                        result[param_name] = max(matches[0], matches[1])
                else:
                    return None
            else:
                # 资源参数提取
                pattern = self.resource_pattern_map[contained_keyword]
                matches = list(set(re.findall(pattern, context, flags=re.ASCII)))
                if len(matches) != 1:
                    return None
                param_type = tool.args[param_name].get('type')
                if param_type == 'array':
                    result[param_name] = [matches[0]]
                elif param_type == 'string':
                    result[param_name] = matches[0]
                else:
                    return None

        return result

    def extract_resources(self, context: str) -> list[str]:
        """
        从给定的上下文中提取所有匹配machineId模式的资源标识符。

        参数:
            context (str): 需要搜索的上下文字符串

        返回:
            list[str]: 匹配到的所有唯一machineId列表
        """
        matches = re.findall(self.resource_pattern_map['machineId'], context, flags=re.ASCII)
        return list(set(matches))
