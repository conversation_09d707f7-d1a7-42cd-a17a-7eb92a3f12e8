"""
Overview Generation Node

Generates status overview based on collected data and scene identification results.
Implements real-time streaming output using LangGraph's get_stream_writer.
"""

import logging
import time
from typing import Dict, Any

from deep_diagnose.common.utils.machine_utils import MachineIdType
from deep_diagnose.core.interactive.interactive_agent import InteractiveAgent
from deep_diagnose.llms.bailian_application import get_overview_generator_llm
from langgraph.config import get_stream_writer

logger = logging.getLogger(__name__)


async def overview_generation_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    Overview Generation Node - Implements real-time streaming output using LangGraph's get_stream_writer.

    Features:
    1. Generate overview based on collected data
    2. Select appropriate generation strategy (FastPath or Generic) based on scene
    3. Stream generation using BailianApplication LLM wrapper
    4. Support Langfuse automatic tracing
    5. Real-time streaming output using get_stream_writer

    Args:
        state: Dict[str, Any] - State containing data and scene information

    Returns:
        Dict[str, Any]: Final state with generated overview

    Raises:
        Exception: When overview generation fails
    """
    # Extract state fields
    machine_id = state.get("machine_id", "")
    machine_id_type = state.get("machine_id_type", "")
    start_time = state.get("start_time", "")
    info_data = state.get("info_data", {})
    scene = state.get("scene", {})

    logger.info("Starting overview generation process", extra={
        "machine_id": machine_id,
        "machine_id_type": machine_id_type,
        "operation": "overview_generation",
        "step": "initialization"
    })

    try:
        # Get stream writer for real-time streaming output
        writer = get_stream_writer()

        # Generate prompt
        prompt = _generate_overview_prompt(machine_id_type, info_data)
        state["prompt"] = prompt

        scene_type = scene.get("scene", "unknown")
        overview = ""
        generation_strategy = "unknown"

        overview_start_time = time.time()

        logger.debug("Overview generation initialized", extra={
            "scene_type": scene_type,
            "prompt_length": len(prompt),
            "operation": "overview_generation",
            "step": "prompt_generated"
        })

        # Send start status
        writer(
            {
                "type": "overview_status",
                "status": "started",
                "message": "正在分析机器状态信息...",
                "scene_type": scene_type,
                "timestamp": time.time(),
            }
        )

        if scene_type in ["machine_stopped", "down"]:
            # FastPath strategy
            generation_strategy = "fastpath"
            logger.info("Executing FastPath strategy for overview generation", extra={
                "strategy": generation_strategy,
                "scene_type": scene_type,
                "operation": "overview_generation",
                "step": "strategy_selection"
            })

            # Send strategy selection status
            writer(
                {
                    "type": "overview_status",
                    "status": "strategy_selected",
                    "message": "正在使用FastPath策略分析...",
                    "strategy": generation_strategy,
                    "timestamp": time.time(),
                }
            )

            agent = InteractiveAgent()
            instruction = _build_fastpath_instruction(machine_id_type, machine_id, start_time)

            logger.debug("FastPath instruction prepared", extra={
                "instruction": instruction,
                "operation": "overview_generation",
                "step": "instruction_prepared"
            })

            # Send execution status
            writer(
                {
                    "type": "overview_status",
                    "status": "executing",
                    "message": "正在执行FastPath分析...",
                    "timestamp": time.time(),
                }
            )

            async for chunk in agent.astream(instruction, mode="instruction", authority="dev"):
                text = getattr(chunk, "result", None)
                if text:
                    overview = text  # FastPath returns complete result
                    writer(
                        {
                            "type": "overview_content",
                            "overview": overview,
                            "overview_length": len(overview),
                            "timestamp": time.time(),
                        }
                    )

            # Send completion status
            writer(
                {
                    "type": "overview_status",
                    "status": "completed",
                    "message": overview,
                    "strategy": generation_strategy,
                    "duration": time.time() - overview_start_time,
                    "timestamp": time.time(),
                }
            )

        else:
            # Generic Bailian strategy - Using new LLM wrapper
            generation_strategy = "bailian"
            logger.info("Executing Bailian strategy for overview generation", extra={
                "strategy": generation_strategy,
                "scene_type": scene_type,
                "operation": "overview_generation",
                "step": "strategy_selection"
            })

            # Send strategy selection status
            writer(
                {
                    "type": "overview_status",
                    "status": "strategy_selected",
                    "message": "正在使用Bailian策略分析...",
                    "strategy": generation_strategy,
                    "timestamp": time.time(),
                }
            )

            full_prompt = prompt + "\n 上述信息的查询资源状态总结："

            # Send LLM calling status
            writer(
                {
                    "type": "overview_status",
                    "status": "llm_calling",
                    "message": "正在调用Bailian LLM进行分析...",
                    "timestamp": time.time(),
                }
            )

            # Use new BailianApplication LLM wrapper
            overview_llm = get_overview_generator_llm()
            overview = ""
            chunk_count = 0

            logger.debug("Bailian LLM instance obtained", extra={
                "llm_type": "bailian",
                "operation": "overview_generation",
                "step": "llm_initialized"
            })

            # Send stream generation start status
            writer(
                {
                    "type": "overview_status",
                    "status": "generating",
                    "message": "正在生成概览内容...",
                    "timestamp": time.time(),
                }
            )

            async for chunk in overview_llm.astream(full_prompt):
                if chunk:
                    # BailianApplication now returns BaseMessageChunk objects, need to extract content
                    chunk_content = str(chunk.content) if hasattr(chunk, 'content') else str(chunk)
                    overview += chunk_content  # Bailian returns incremental fragments
                    chunk_count += 1

                    # Send real-time streaming content updates
                    writer(
                        {
                            "type": "overview_content",
                            "content": chunk_content,
                            "overview": overview,
                            "chunk_count": chunk_count,
                            "overview_length": len(overview),
                            "timestamp": time.time(),
                        }
                    )

            logger.debug("Bailian streaming completed", extra={
                "chunk_count": chunk_count,
                "overview_length": len(overview),
                "operation": "overview_generation",
                "step": "streaming_completed"
            })

            # Send completion status
            writer(
                {
                    "type": "overview_status",
                    "status": "completed",
                    "message": overview,
                    "strategy": generation_strategy,
                    "chunk_count": chunk_count,
                    "duration": time.time() - overview_start_time,
                    "timestamp": time.time(),
                }
            )

        overview_duration = time.time() - overview_start_time

        # Set final state
        state["overview"] = overview
        state["current_step"] = "overview_generation"
        state["generation_strategy"] = generation_strategy
        state["step_metadata"] = {
            "status": "completed",
            "generation_duration": overview_duration,
            "overview_length": len(overview),
            "scene_type": scene_type,
            "prompt_length": len(prompt),
            "chunk_count": chunk_count if "chunk_count" in locals() else 0,
        }

        logger.info("Overview generation completed successfully", extra={
            "duration_seconds": f"{overview_duration:.3f}",
            "strategy": generation_strategy,
            "overview_length": len(overview),
            "scene_type": scene_type,
            "operation": "overview_generation",
            "step": "completed"
        })

        return state

    except Exception as e:
        logger.error("Overview generation node execution failed", extra={
            "error": str(e),
            "machine_id": machine_id,
            "scene_type": scene.get("scene", "unknown"),
            "operation": "overview_generation",
            "step": "error_handling"
        }, exc_info=True)

        # Send error status
        try:
            writer = get_stream_writer()
            writer(
                {
                    "type": "overview_status",
                    "status": "error",
                    "message": f"概览生成失败: {str(e)}",
                    "error": str(e),
                    "timestamp": time.time(),
                }
            )
        except Exception as writer_error:
            logger.warning("Failed to send error status via stream writer", extra={
                "writer_error": str(writer_error),
                "operation": "overview_generation",
                "step": "error_notification_failed"
            })

        state["overview"] = f"概览生成失败: {str(e)}"
        state["current_step"] = "overview_generation"
        state["generation_strategy"] = "error"
        state["step_metadata"] = {"status": "error", "error": str(e), "scene_type": scene.get("scene", "unknown")}
        return state


def _generate_overview_prompt(machine_id_type, info_data: Dict[str, Any]) -> str:
    """
    Generate overview prompt based on machine type and collected information.

    Args:
        machine_id_type: Machine ID type identifier
        info_data: Dict containing collected information data

    Returns:
        str: Generated prompt string

    Raises:
        Exception: When prompt generation fails
    """
    try:
        prompt = "查询资源类型为"

        # Build basic information prompt based on machine type
        if machine_id_type == MachineIdType.INSTANCE_ID:
            vm_info = info_data.get("getVmBasicInfo", [{}])
            base_info = vm_info[0] if isinstance(vm_info, list) and len(vm_info) > 0 else {}
            prompt += _construct_vminfo_prompt(base_info)
        elif machine_id_type in [MachineIdType.NC_IP, MachineIdType.NC_ID]:
            nc_info = info_data.get("getNcBasicInfo", [{}])
            base_info = nc_info[0] if isinstance(nc_info, list) and len(nc_info) > 0 else {}
            prompt += _construct_ncinfo_prompt(base_info)
        else:
            prompt += "机器类型未知。"

        # Add operational events information
        prompt += _construct_event_prompt(info_data.get("listReportedOperationalEvents", []))

        # Add monitoring exceptions information
        prompt += _construct_exception_prompt(info_data.get("listCategorizedMonitorExceptions", []))

        # Add operation records information
        prompt += _construct_operation_prompt(info_data.get("listOperationRecords", []))

        logger.debug("Overview prompt generated successfully", extra={
            "prompt_length": len(prompt),
            "machine_id_type": machine_id_type,
            "operation": "prompt_generation",
            "step": "completed"
        })

        return prompt

    except Exception as e:
        logger.error("Failed to generate overview prompt", extra={
            "error": str(e),
            "machine_id_type": machine_id_type,
            "operation": "prompt_generation",
            "step": "error"
        }, exc_info=True)
        return f"prompt生成失败: {str(e)}"


def _build_fastpath_instruction(machine_id_type, machine_id: str, start_time: str) -> str:
    """
    Build FastPath instruction based on machine type.

    Args:
        machine_id_type: Machine ID type identifier
        machine_id: Machine identifier
        start_time: Analysis start time

    Returns:
        str: FastPath instruction string
    """
    if machine_id_type in [MachineIdType.NC_IP, MachineIdType.NC_ID]:
        prefix = "FASTPATH_NC_DOWN"
    elif machine_id_type == MachineIdType.INSTANCE_ID:
        prefix = "FASTPATH_VM_DOWN"
    else:
        prefix = "FASTPATH_NC_DOWN"

    instruction = f"{prefix}@BAILIAN,{machine_id},{start_time}"
    
    logger.debug("FastPath instruction built", extra={
        "instruction": instruction,
        "machine_id_type": machine_id_type,
        "operation": "fastpath_instruction",
        "step": "completed"
    })

    return instruction


def _construct_vminfo_prompt(data: Dict[str, Any]) -> str:
    """Build VM information prompt section."""
    required_fields = [
        "instanceId",
        "isLocalDisk",
        "imageName",
        "instanceType",
        "isWin",
        "osVersion",
        "ecsBusinessStatus",
        "status",
    ]

    available_info = {}
    for field in required_fields:
        if field in data and data[field] is not None and data[field] != "":
            available_info[field] = data[field]

    prompt_parts = []
    field_descriptions = {
        "instanceId": lambda v: f"实例ID为 {v}",
        "isLocalDisk": lambda v: f"磁盘类型是 {v}",
        "imageName": lambda v: f"镜像名称为 {v}",
        "instanceType": lambda v: f"实例规格为 {v}",
        "isWin": lambda v: f"是 {v} 操作系统",
        "osVersion": lambda v: f"操作系统版本是 {v}",
        "ecsBusinessStatus": lambda v: f"业务状态为 {v}",
        "status": lambda v: f"当前运行状态为 {v}",
    }

    for field in required_fields:
        if field in available_info:
            prompt_parts.append(field_descriptions[field](available_info[field]))

    if prompt_parts:
        prompt = "，".join(prompt_parts) + "。"
        return f"VM实例，{prompt}"
    else:
        return "VM实例，基础信息不完整。"


def _construct_ncinfo_prompt(data: Dict[str, Any]) -> str:
    """Build NC information prompt section."""
    required_fields = [
        "isLocalDisk",
        "cpuModel",
        "lockType",
        "productName",
        "multiCnNum",
        "cpuGeneration",
        "osType",
        "bizStatus",
        "dragonboxNc",
        "vcpuMod",
        "physicalModel",
        "lockReason",
        "grayBizType",
    ]

    available_info = {}
    for field in required_fields:
        if field in data and data[field] is not None and data[field] != "":
            available_info[field] = data[field]

    prompt_parts = []
    field_descriptions = {
        "isLocalDisk": lambda v: f"磁盘类型是 {v}",
        "cpuModel": lambda v: f"CPU型号为 {v}",
        "lockType": lambda v: f"锁定类型为 {v}",
        "productName": lambda v: f"产品名称为 {v}",
        "multiCnNum": lambda v: f"多实例数量为 {v}",
        "cpuGeneration": lambda v: f"CPU代际为 {v}",
        "osType": lambda v: f"操作系统类型为 {v}",
        "bizStatus": lambda v: f"业务状态为 {v}",
        "dragonboxNc": lambda v: f"龙盒NC: {v}",
        "vcpuMod": lambda v: f"虚拟CPU模式为 {v}",
        "physicalModel": lambda v: f"物理模型为 {v}",
        "lockReason": lambda v: f"锁定原因为 {v}",
        "grayBizType": lambda v: f"灰度业务类型: {v}",
    }

    for field in required_fields:
        if field in available_info:
            prompt_parts.append(field_descriptions[field](available_info[field]))

    if prompt_parts:
        prompt = "，".join(prompt_parts) + "。"
        return f"NC机器，{prompt}"
    else:
        return "NC机器，基础信息不完整。"


def _construct_event_prompt(data):
    """
    Generate client event prompt section.
    
    Args:
        data: List of operational events
        
    Returns:
        str: Formatted event prompt
    """
    if len(data) == 0:
        return "无需要客户知会或处理的运维事件\n"
    
    required_fields = ["instance", "reason", "code_desc", "status", "plan", "publish", "end"]
    field_descriptions = {
        "instance": lambda v: f"影响实例 {v}",
        "reason": lambda v: f"异常原因为 {v}",
        "code_desc": lambda v: f"处理动作为 {v}",
        "status": lambda v: f"当前状态为 {v}",
        "publish": lambda v: f"通知发布时间为 {v}",
        "plan": lambda v: f"计划处理时间为 {v}",
        "end": lambda v: f"实际完成时间为 {v}",
    }

    prompt_lines = ["以下是最近推送给客户的运维事件："]
    for event in data:
        event_lines = []
        for field in required_fields:
            value = event.get(field)
            if value is not None and value != "" and field in field_descriptions:
                event_lines.append(field_descriptions[field](value))
        if event_lines:
            prompt_lines.append("\n".join(event_lines))
        else:
            prompt_lines.append("(No detailed field information)")
        prompt_lines.append("")  # Empty line separator
    prompt = "\n".join(prompt_lines)
    return prompt


def _construct_exception_prompt(data):
    """Generate monitoring exception prompt section."""
    if len(data) == 0:
        return "时间范围内资源无监控异常\n"
    
    keymetric = data.get("keymetric", [])
    other = data.get("other", [])
    low_warning = data.get("low_warning", [])

    prompt = "时间范围内出现监控异常：\n"
    
    if len(keymetric) > 0:
        prompt += "关键监控异常：\n"
        for exception in keymetric:
            prompt += f"""检测到异常 {exception.get('exceptionName', '')} ({exception.get('exceptionDesc', '')}): 在时间 {exception.get('exceptionTime', '')} - {exception.get('lastExceptionTime', '')} 共发生了 {exception.get('exceptionCount')}次。 详细原因解释为 {exception.get('reason')} + {exception.get('additionalInfo')}\n"""

    if len(other) > 0:
        prompt += "其他监控异常：\n"
        for exception in other:
            prompt += f"""检测到其他异常 {exception.get('exceptionName', '')} ({exception.get('exceptionDesc', '')}): 在时间 {exception.get('exceptionTime', '')} - {exception.get('lastExceptionTime', '')} 共发生了 {exception.get('exceptionCount')}次。 详细原因解释为 {exception.get('reason')} + {exception.get('additionalInfo')}\n"""
            
    if len(low_warning) > 0:
        prompt += "低级别监控异常：\n"
        for exception in low_warning:
            prompt += f"""检测到异常事件 {exception.get('exceptionName', '')} ({exception.get('exceptionDesc', '')}): 在时间 {exception.get('exceptionTime', '')} - {exception.get('lastExceptionTime', '')} 共发生了 {exception.get('exceptionCount')}次。 详细原因解释为 {exception.get('reason')} + {exception.get('additionalInfo')}\n"""

    return prompt


def _construct_operation_prompt(operations: list) -> str:
    """Build operation records prompt section."""
    if not operations or not isinstance(operations, list):
        return ""
    
    field_descriptions = {
        "status": lambda v: f"运维动作状态为 {v}",
        "description": lambda v: f"运维描述为 {v}",
        "create_time": lambda v: f"运维发起时间为 {v}",
        "modified_time": lambda v: f"运维状态更新时间为 {v}",
        "code": lambda v: f"运维code为 {v}",
    }
    required_fields = ["description", "create_time", "status", "modified_time", "code"]

    if len(operations) == 0:
        return "\n该时间段内未发现运维记录。"

    prompt_lines = [f"\n该时间段内发现 {len(operations)} 个运维命中记录："]
    for i, operation in enumerate(operations):
        operation_lines = []
        for field in required_fields:
            value = operation.get(field)
            if value is not None and value != "" and field in field_descriptions:
                operation_lines.append(field_descriptions[field](value))
        if operation_lines:
            prompt_lines.append(f"第 {i+1} 条记录：\n" + "\n".join(operation_lines))
        else:
            prompt_lines.append(f"第 {i+1} 条记录：（无详细字段信息）")
        prompt_lines.append("")
    return "\n".join(prompt_lines)
