"""
Scene Recognition Node

This module implements scene recognition functionality to identify fault scenario types
based on collected diagnostic data. The implementation maintains compatibility with
the original recognition logic while providing structured output format.
"""

import logging
from typing import Dict, Any

from deep_diagnose.common.utils.scene_recognition import recognize_scene

logger = logging.getLogger(__name__)


async def scene_recognition_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    Scene recognition node for fault scenario identification.

    This function analyzes collected diagnostic data to identify the type of fault scenario.
    It provides a structured response format and supports automatic tracing capabilities.

    Args:
        state (Dict[str, Any]): Current state containing collected diagnostic data
            - info_data: Diagnostic information collected from various sources
            - machine_id: Unique identifier for the target machine

    Returns:
        Dict[str, Any]: Updated state containing scene recognition results
            - scene: Scene identification data with type, time, and reason
            - step_metadata: Processing metadata including identification status and method

    Raises:
        Exception: When scene recognition process fails due to data processing errors
    """
    info_data = state.get("info_data", {})
    machine_id = state.get("machine_id", "")

    logger.info("Initiating scene recognition process for machine_id=%s", machine_id)

    try:
        # Execute scene recognition using legacy-compatible logic
        scene_result = recognize_scene(info_data)

        if scene_result:
            scene_data = {
                "scene": scene_result.get("scene", "unknown"),
                "time": scene_result.get("time", ""),
                "reason": scene_result.get("reason", ""),
            }
        else:
            scene_data = {"scene": "unknown", "time": "", "reason": "无法识别场景类型"}

        logger.info("Scene recognition completed successfully: scene_type=%s, timestamp=%s", 
                   scene_data['scene'], scene_data['time'])
        return {
            "scene": scene_data,
            "step_metadata": {
                "scene_identified": bool(scene_result),
                "scene_type": scene_data["scene"],
                "time": scene_data["time"],
                "data_sources": list(info_data.keys()) if info_data else [],
                "recognition_method": "legacy_compatible",
            },
        }

    except Exception as e:
        logger.error("Scene recognition node execution failed: %s", str(e), exc_info=True)
        return {
            "scene": {"scene": "error", "confidence": 0.0, "reason": f"识别异常: {str(e)}"},
            "step_metadata": {"scene_identified": False, "error": str(e), "recognition_method": "error_fallback"},
        }
