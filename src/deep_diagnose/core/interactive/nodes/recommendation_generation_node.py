"""
推荐生成节点

基于收集的数据和生成的概览，生成后续追问建议
使用 next_query_prediction 工具进行智能推荐
"""

import json
import logging
import time
from typing import Dict, Any, List, Optional

from deep_diagnose.tools.next_query_prediction import next_query_prediction_tool

logger = logging.getLogger(__name__)


async def recommendation_generation_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    推荐生成节点
    
    功能：
    1. 基于概览内容和上下文使用 next_query_prediction 工具生成推荐
    2. 支持多种预测策略和配置参数
    3. 使用默认推荐作为后备
    4. 支持 Langfuse 自动追踪
    
    Args:
        state: Dict[str, Any] - 包含概览和上下文信息的状态
        
    Returns:
        Dict[str, Any]: 更新后的状态，包含推荐列表
    """
    # 获取状态字段
    machine_id = state.get("machine_id", "")
    machine_id_type = state.get("machine_id_type", "")
    overview = state.get("overview", "")
    scene = state.get("scene", {})
    prompt = state.get("prompt", "")
    info_data = state.get("info_data", {})
    
    logger.info(f"开始推荐生成，机器ID: {machine_id}")
    
    try:
        # 生成推荐
        recommendation_start_time = time.time()
        
        # 构建消息上下文
        messages = _build_messages_context(overview, info_data)
        
        # 构建特征信息
        features = _build_features(machine_id, machine_id_type, scene, info_data)
        
        prediction_result = await next_query_prediction_tool.ainvoke({
            "messages": messages,
            "features": features,
            "top_k": 8,  # 返回5个推荐
            "strategy": "hybrid",  # 使用混合策略：启发式 + LLM
            "llm_mode": "agent",  # 使用百炼Agent agent / 通用LLM generic
            "ranker": "hybrid",  # 使用混合排序：BM25 + embedding
            "hybrid_weight": 0.6,  # embedding权重0.6，BM25权重0.4
            "dup_threshold": 0.9,  # 去重阈值0.9
        })
        
        recommendation_duration = time.time() - recommendation_start_time
        logger.info(f"推荐生成完成，耗时: {recommendation_duration:.3f}秒")
        
        # 解析推荐结果
        recommendations = _parse_prediction_result(prediction_result, machine_id)
        
        # 如果推荐为空，使用默认推荐
        if not recommendations:
            logger.info("使用默认推荐")
            recommendations = _get_default_recommendations(machine_id)
        
        logger.info(f"推荐生成完成，生成 {len(recommendations)} 个推荐")
        return {
            "recommendations": recommendations,
            "finished": True,
            "step_metadata": {
                "recommendation_count": len(recommendations),
                "generation_duration": recommendation_duration,
                "generation_method": "next_query_prediction",
                "strategy": "hybrid",
                "llm_mode": "agent",
                "ranker": "hybrid"
            }
        }
        
    except Exception as e:
        logger.error(f"推荐生成节点执行失败: {e}", exc_info=True)
        
        # 使用默认推荐作为后备
        default_recommendations = _get_default_recommendations(machine_id)
        return {
            "recommendations": default_recommendations,
            "finished": True,
            "step_metadata": {
                "recommendation_count": len(default_recommendations),
                "error": str(e),
                "generation_method": "fallback",
                "strategy": "fallback"
            }
        }


def _build_messages_context(overview: str, prompt: str) -> List[Dict[str, Any]]:
    """
    构建消息上下文
    
    Args:
        overview: 概览内容
        info_data: 收集的信息数据
        
    Returns:
        List[Dict]: 消息上下文列表
    """
    messages = []
    
    # 添加用户查询（模拟）
    messages.append({
        "role": "user",
        "content": "检查一下目标机器的运行状态如何？"
    })
    
    # 添加工具返回的信息
    if prompt:
        messages.append({
            "role": "tool",
            "content": prompt
        })
    
    # 添加助手回复（概览）
    if overview:
        messages.append({
            "role": "assistant",
            "content": overview
        })
    
    return messages



def _build_features(machine_id: str, machine_id_type: str, scene: Dict[str, Any], info_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    构建特征信息
    
    Args:
        machine_id: 机器ID
        machine_id_type: 机器ID类型
        scene: 场景信息
        info_data: 收集的信息数据
        
    Returns:
        Dict: 特征信息字典
    """
    features = {
        "machine_id": machine_id,
        "machine_id_type": machine_id_type,
    }
    
    # 添加场景信息
    if scene and isinstance(scene, dict):
        features["scene"] = scene.get("scene", "")
        features["scene_time"] = scene.get("time", "")
        features["scene_reason"] = scene.get("reason", "")
    
    # 添加数据源信息
    if info_data:
        features["data_sources"] = list(info_data.keys())
        for key, value in info_data.items():
            features[key] = value
    
    return features


def _parse_prediction_result(prediction_result: str, machine_id: str) -> List[Dict[str, Any]]:
    """
    解析 next_query_prediction 工具的返回结果
    
    Args:
        prediction_result: 工具返回的JSON字符串
        machine_id: 机器ID
        
    Returns:
        List[Dict]: 解析后的推荐列表
    """
    try:
        # 尝试解析JSON结果
        if isinstance(prediction_result, str):
            result_data = json.loads(prediction_result)
        else:
            result_data = prediction_result
        
        predictions = result_data.get("predictions", [])
        recommendations = []
        
        for pred in predictions:
            question = pred.get("question", "")
            confidence = pred.get("confidence", 0.0)
            rationale = pred.get("rationale", "unknown")
            
            if question:
                recommendations.append({
                    "content": question,
                    "raw_content": question,
                    "confidence": confidence,
                    "rationale": rationale,
                    "source": rationale
                })
        
        logger.info(f"解析到 {len(recommendations)} 个推荐问题")
        return recommendations
        
    except Exception as e:
        logger.error(f"解析预测结果失败: {e}", exc_info=True)
        return []


def _get_default_recommendations(machine_id: str) -> List[Dict[str, Any]]:
    """
    获取默认推荐
    
    Args:
        machine_id: 机器ID
        
    Returns:
        List[Dict]: 默认推荐列表
    """
    return [
        {
            "content": "运维查询",
            "url": f"https://cloudbot2.aliyun-inc.com/cloudbot/ng2/#/ecs/ops/maintenance/search?type=vmName&vmName={machine_id}"
        },
        {
            "content": "资源详情查询",
            "url": f"https://changeplus.aliyun-inc.com/#/moon-main-operation/instanceOperation/detail?instanceId={machine_id}"
        }
    ]