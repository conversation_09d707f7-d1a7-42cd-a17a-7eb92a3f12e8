"""
Data Collection Node

Collects machine basic information, monitoring anomalies, operational records and other data.
Supports MCP tool concurrent invocation with simplified implementation.
"""

import asyncio
import json
import logging
import math
import time
from datetime import datetime
from typing import Dict, Any

from deep_diagnose.common.utils.machine_utils import MachineIdType
from deep_diagnose.tools.mcp.mcp_tool_manager import MCPToolManager
from deep_diagnose.storage.redis_client import RedisClient

logger = logging.getLogger(__name__)


async def data_collection_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    Data Collection Node

    Features:
    1. Retrieve machine basic information (VM/NC)
    2. Query monitoring anomalies
    3. Fetch operational records
    4. Query operation events

    Args:
        state: Dict[str, Any] - Current state (dictionary format passed by LangGraph)

    Returns:
        Dict[str, Any]: Updated state containing collected data
    """
    # Extract basic parameters
    machine_id = state.get("machine_id", "")
    machine_id_type = state.get("machine_id_type")
    start_time = state.get("start_time", "")
    end_time = state.get("end_time", "")

    # Ensure machine type is string (Langfuse serializable)
    if hasattr(machine_id_type, "value"):
        state["machine_id_type"] = machine_id_type.value

    logger.info("Initiating data collection for machine_id=%s", machine_id)

    try:
        # Define MCP tool whitelist
        mcp_tool_whitelist = [
            "getVmBasicInfo",
            "getNcBasicInfo",
            "listCategorizedMonitorExceptions",
            "listReportedOperationalEvents",
            "listOperationRecords",
        ]

        # Retrieve available MCP tools
        mcp_tools = await MCPToolManager().get_enabled_mcp_tools()
        tool_map = {tool.name: tool for tool in mcp_tools if tool.name in mcp_tool_whitelist}

        logger.info("Available tools discovered: %s", list(tool_map.keys()))

        # Check cache for existing data
        redis_client = RedisClient()
        redis_key = _generate_cache_key(machine_id, start_time, end_time)
        cached_data = redis_client.get_cache(redis_key)

        if cached_data:
            logger.info("Cache hit for key=%s", redis_key)
            return {
                "info_data": json.loads(str(cached_data)),
                "step_metadata": {
                    "cache_hit": True,
                    "tools_available": list(tool_map.keys()),
                    "collection_method": "cached",
                },
            }

        # Build tool invocation tasks
        tool_tasks = _build_tool_tasks(machine_id, machine_id_type, start_time, end_time, tool_map)

        if not tool_tasks:
            logger.warning("No executable tool tasks available")
            return {
                "info_data": {},
                "step_metadata": {
                    "cache_hit": False,
                    "tools_available": list(tool_map.keys()),
                    "collection_method": "no_tools",
                },
            }

        # Execute tool invocations concurrently
        start_time_exec = time.time()
        task_results = await asyncio.gather(*[task["coroutine"] for task in tool_tasks], return_exceptions=True)
        duration = time.time() - start_time_exec

        logger.info("Concurrent tool execution completed in %.3f seconds", duration)

        # Process results and cache
        results = _process_tool_results(tool_tasks, task_results)
        redis_client.set_cache(redis_key, json.dumps(results), ttl_seconds=60 * 60 * 24)

        logger.info("Data collection completed successfully. Executed %d tools", len(results))
        return {
            "info_data": results,
            "step_metadata": {
                "cache_hit": False,
                "tools_executed": len(tool_tasks),
                "tools_successful": len(results),
                "execution_duration": duration,
                "collection_method": "mcp_tools",
                "tools_available": list(tool_map.keys()),
            },
        }

    except Exception as e:
        logger.error("Data collection node execution failed: %s", e, exc_info=True)
        return {"info_data": {}, "step_metadata": {"error": str(e), "collection_method": "error"}}


def _generate_cache_key(machine_id: str, start_time: str, end_time: str) -> str:
    """Generate cache key for data collection results"""

    def round_to_nearest_15min(
        datetime_str: str, datetime_format: str = "%Y-%m-%d %H:%M:%S", round_to_seconds: int = 1
    ) -> int:
        try:
            datetime_obj = datetime.strptime(datetime_str, datetime_format)
            ts = int(datetime_obj.timestamp())
            return int(math.floor(ts / round_to_seconds) * round_to_seconds)
        except Exception:
            raise ValueError(f"Invalid timestamp format: {datetime_str}")

    key_start_time = round_to_nearest_15min(start_time)
    key_end_time = round_to_nearest_15min(end_time)
    return f"inspect_information_{machine_id}_{key_start_time}_{key_end_time}"


def _process_tool_results(tool_tasks: list, task_results: list) -> Dict[str, Any]:
    """Process tool invocation results"""
    results = {}
    for task_info, result in zip(tool_tasks, task_results):
        tool_name = task_info["name"]
        try:
            if isinstance(result, Exception):
                logger.error("Tool %s execution failed: %s", tool_name, result)
                # Set default value based on expected return type
                results[tool_name] = [] if tool_name.endswith("Info") else {}
                continue

            json_result = json.loads(str(result))
            results[tool_name] = json_result

        except Exception as e:
            logger.warning("Failed to parse result from tool %s: %s", tool_name, e)
            results[tool_name] = [] if tool_name.endswith("Info") else {}

    return results


def _build_tool_tasks(
    machine_id: str, machine_id_type, start_time: str, end_time: str, tool_map: Dict[str, Any]
) -> list:
    """Build tool invocation tasks based on machine type"""
    tool_tasks = []

    # Determine tools to invoke based on machine type
    if machine_id_type == MachineIdType.INSTANCE_ID:
        # VM instance related tools
        if "getVmBasicInfo" in tool_map:
            tool_tasks.append(
                {
                    "name": "getVmBasicInfo",
                    "coroutine": tool_map["getVmBasicInfo"].ainvoke({"instanceIds": [machine_id]}),
                }
            )

        if "listReportedOperationalEvents" in tool_map:
            tool_tasks.append(
                {
                    "name": "listReportedOperationalEvents",
                    "coroutine": tool_map["listReportedOperationalEvents"].ainvoke(
                        {"instanceId": machine_id, "startTime": start_time, "endTime": end_time}
                    ),
                }
            )

    elif machine_id_type in [MachineIdType.NC_IP, MachineIdType.NC_ID]:
        # NC related tools
        if "getNcBasicInfo" in tool_map:
            tool_tasks.append(
                {"name": "getNcBasicInfo", "coroutine": tool_map["getNcBasicInfo"].ainvoke({"ncs": [machine_id]})}
            )

    else:
        logger.warning("Unsupported machine_id_type: %s", machine_id_type)
        return tool_tasks

    # Common tools (required for all machine types)
    if "listCategorizedMonitorExceptions" in tool_map:
        tool_tasks.append(
            {
                "name": "listCategorizedMonitorExceptions",
                "coroutine": tool_map["listCategorizedMonitorExceptions"].ainvoke(
                    {"machineId": machine_id, "startTime": start_time, "endTime": end_time}
                ),
            }
        )

    if "listOperationRecords" in tool_map:
        tool_tasks.append(
            {
                "name": "listOperationRecords",
                "coroutine": tool_map["listOperationRecords"].ainvoke(
                    {"machineId": machine_id, "startTime": start_time, "endTime": end_time}
                ),
            }
        )

    logger.info("Built %d tool invocation tasks", len(tool_tasks))
    return tool_tasks
