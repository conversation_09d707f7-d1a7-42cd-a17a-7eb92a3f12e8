from typing import Any, Optional, List, Dict
from dataclasses import field
from langgraph.graph import MessagesState

from deep_diagnose.common.utils.machine_utils import MachineIdType


class InspectState(MessagesState):
    # 基础信息
    machine_id: str = ""
    start_time: str = ""
    end_time: str = ""
    machine_id_type: MachineIdType = MachineIdType.UNKNOWN
    
    # 数据交换结构（恢复到原始简洁实现）
    tool_map: Dict[str, Any] = field(default_factory=dict)
    info_data: Dict[str, Any] = field(default_factory=dict)
    scene: Optional[Dict[str, Any]] = None
    
    # 输出结果
    overview: str = ""
    recommendations: List[Dict[str, Any]] = field(default_factory=list)
    finished: bool = False
    
    # Langfuse 跟踪字段
    trace_id: Optional[str] = None
    session_id: Optional[str] = None
    user_id: Optional[str] = None
    langfuse_handler: Optional[Any] = None  # Langfuse CallbackHandler 对象
    
    # 执行上下文
    current_step: str = "start"
    step_start_time: Optional[str] = None
    step_metadata: Dict[str, Any] = field(default_factory=dict)

    class Config:
        arbitrary_types_allowed = True
