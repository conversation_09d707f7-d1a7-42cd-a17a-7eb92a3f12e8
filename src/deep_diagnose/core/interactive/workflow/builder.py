from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.runnables import RunnableConfig
from typing import AsyncGenerator
import asyncio

from deep_diagnose.core.events.inspect_event import InspectEvent
from deep_diagnose.core.interactive.types.state import InspectState
from deep_diagnose.core.interactive.workflows.inspect_workflow import (
    create_inspect_workflow,
    create_inspect_workflow_with_memory,
    stream_inspect_workflow
)


def build_graph_with_memory():
    """Build and return the interactive workflow graph with memory."""
    import warnings
    warnings.warn(
        "build_graph_with_memory is deprecated. Use create_inspect_workflow_with_memory instead.",
        DeprecationWarning,
        stacklevel=2
    )
    
    return create_inspect_workflow_with_memory()


def build_graph():
    """Build and return the interactive workflow graph without memory."""
    import warnings
    warnings.warn(
        "build_graph is deprecated. Use create_inspect_workflow instead.",
        DeprecationWarning,
        stacklevel=2
    )
    
    return create_inspect_workflow()


# 创建默认图实例（兼容性）
graph = create_inspect_workflow()
graph_with_memory = create_inspect_workflow_with_memory()


# 便捷的流式执行函数（兼容性）
async def stream_overview_workflow(machine_id: str, start_time: str, end_time: str) -> AsyncGenerator[InspectEvent, None]:
    """
    流式执行 Inspect 工作流的便捷函数
    
    已弃用：请使用 stream_inspect_workflow 替代

    Args:
        machine_id: 机器ID
        start_time: 开始时间
        end_time: 结束时间

    Yields:
        InspectEvent: 每次状态更新
    """
    import warnings
    warnings.warn(
        "stream_overview_workflow is deprecated. Use stream_inspect_workflow instead.",
        DeprecationWarning,
        stacklevel=2
    )
    
    # 兼容性包装：使用新的流式工作流
    async for event in stream_inspect_workflow(machine_id, start_time, end_time):
        yield event


# 测试函数
async def _test_workflow():
    """Test main function for workflow validation"""
    print("🚀 Starting Interactive Workflow Builder Test...")

    # Test cases with machine_id and time parameters
    test_cases = [
        {
            "machine_id": "i-bp1fz27ong6p6w693vn5",
            "description": "ECS Instance ID 测试 - 通过 workflow",
            "start_time": "2025-07-28 00:00:00",
            "end_time": "2025-07-30 23:59:59",
        },
        {
            "machine_id": "***********",
            "description": "NC IP Address 测试 - 通过 workflow",
            "start_time": "2025-07-28 00:00:00",
            "end_time": "2025-07-30 23:59:59",
        },
    ]

    print(f"\n📊 测试方法:")
    print(f"   1. 直接调用 stream_overview_workflow 函数")
    print(f"   2. 使用 graph 实例流式执行")
    print(f"   3. 使用 graph_with_memory 实例流式执行")

    for method_idx, method_name in enumerate(["stream_overview_workflow 函数", "graph 实例", "graph_with_memory 实例"], 1):
        print(f"\n{'='*80}")
        print(f"🔧 测试方法 {method_idx}: {method_name}")
        print("=" * 80)

        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{'='*60}")
            print(f"🧪 测试用例 {i}: {test_case['description']}")
            print(f"📍 Machine ID: {test_case['machine_id']}")
            print(f"⏰ Time Range: {test_case['start_time']} ~ {test_case['end_time']}")
            print("=" * 60)

            try:
                event_count = 0
                last_overview_len = 0
                last_recommendations_count = 0

                print(f"📡 开始事件流处理... (方法: {method_name})")

                # 根据测试方法选择执行方式
                if method_idx == 1:
                    # 方法1: 直接调用 stream_overview_workflow 函数
                    event_stream = stream_overview_workflow(
                        machine_id=test_case["machine_id"], start_time=test_case["start_time"], end_time=test_case["end_time"]
                    )
                elif method_idx == 2:
                    # 方法2: 使用 graph 实例
                    initial_state = InspectState(machine_id=test_case["machine_id"], start_time=test_case["start_time"], end_time=test_case["end_time"])
                    event_stream = graph.astream(initial_state)
                else:
                    # 方法3: 使用 graph_with_memory 实例
                    initial_state = InspectState(machine_id=test_case["machine_id"], start_time=test_case["start_time"], end_time=test_case["end_time"])
                    event_stream = graph_with_memory.astream(initial_state)

                async for event in event_stream:
                    event_count += 1

                    # 对于 graph 实例，event 是字典格式
                    if isinstance(event, dict):
                        # 提取 overview_processor 节点的输出
                        if "overview_processor" in event:
                            event = event["overview_processor"]
                        else:
                            print(f"📨 图状态更新 #{event_count}: {list(event.keys())}")
                            continue

                    # 解析事件内容进行调试输出
                    if hasattr(event, "overview") and hasattr(event, "recommendations"):
                        # 直接输出 InspectEvent 对象
                        current_overview_len = len(event.overview)
                        current_recommendations_count = len(event.recommendations)

                        # 检查是否有新的概览内容
                        if current_overview_len > last_overview_len:
                            new_content = event.overview[last_overview_len:]
                            if new_content.strip():
                                print(f"📝 概览更新 (+{len(new_content)} 字符): {new_content[:100]}{'...' if len(new_content) > 100 else ''}")
                            last_overview_len = current_overview_len

                        # 检查是否有新的推荐
                        if current_recommendations_count > last_recommendations_count:
                            new_recommendations = event.recommendations[last_recommendations_count:]
                            for rec in new_recommendations:
                                print(f"💡 新推荐: {rec.get('raw_content', 'N/A')}")
                            last_recommendations_count = current_recommendations_count

                        # 显示状态变化
                        status_parts = []
                        if hasattr(event, "finished") and event.finished:
                            status_parts.append("✅ 已完成")
                        if hasattr(event, "machine_id") and event.machine_id:
                            status_parts.append(f"🖥️ {event.machine_id}")
                        if current_overview_len > 0:
                            status_parts.append(f"📄 概览({current_overview_len}字符)")
                        if current_recommendations_count > 0:
                            status_parts.append(f"💡 推荐({current_recommendations_count}个)")

                        print(f"🔄 事件 #{event_count}: {' | '.join(status_parts)}")

                    else:
                        # 如果是字符串格式，输出原始内容用于调试
                        print(f"📨 事件 #{event_count}: {str(event)[:]}{'...' if len(str(event)) > 1000 else ''}")

                    # 如果是完成事件，输出最终结果
                    if hasattr(event, "finished") and event.finished:
                        print(f"\n🎉 任务完成! (通过 {method_name})")
                        print(f"📊 最终统计:")
                        print(f"   - 概览内容长度: {len(event.overview)} 字符")
                        print(f"   - 推荐数量: {len(event.recommendations)} 个")
                        print(f"   - 处理事件总数: {event_count} 个")

                        if len(event.overview) > 0:
                            print(f"\n📄 最终概览内容:")
                            print("-" * 40)
                            print(event.overview)
                            print("-" * 40)

                        if len(event.recommendations) > 0:
                            print(f"\n💡 最终推荐列表:")
                            for idx, rec in enumerate(event.recommendations, 1):
                                print(f"   {idx}. {rec.get('raw_content', 'N/A')}")
                                if "url" in rec:
                                    print(f"      🔗 {rec['url']}")
                        break

                print(f"✅ 测试用例 {i} 完成，共处理 {event_count} 个事件 (通过 {method_name})")

            except Exception as e:
                print(f"❌ 测试用例 {i} 执行失败 (通过 {method_name}): {str(e)}")
                import traceback

                print(f"📋 错误详情:\n{traceback.format_exc()}")

    print(f"\n🏁 所有测试方法和用例执行完成!")


if __name__ == "__main__":
    asyncio.run(_test_workflow())
