import asyncio
import json
import logging
import time
from typing import Dict, AsyncGenerator

from langchain_core.tools.base import BaseTool

from deep_diagnose.common.utils import time_utils
from deep_diagnose.common.utils.json_utils import extract_items_from_incomplete_json, extract_json_code_block, \
    repair_json_output
from deep_diagnose.common.utils.string_utils import is_blank
from deep_diagnose.common.utils.task_scheduler import TaskScheduler, Task
from deep_diagnose.core.interactive.utils.param_manager import ContextualParamManager
from deep_diagnose.core.interactive.state import ExecutionState, StepState, ToolState
from deep_diagnose.core.interactive.utils.tool_provider import Tool
from deep_diagnose.llms.app_bailian import app_bailian, BailianAppEnum
from deep_diagnose.tools.decorators import catch_all_async_errors

logger = logging.getLogger(__name__)


@catch_all_async_errors
async def plan_and_execute(execution_state: ExecutionState):
    # 准备planner的参数
    biz_params = {
        "currentTime": time_utils.current_time_with_week(),
        "tools": execution_state.tool_provider.get_tool_summary()
    }
    # 启动调度器
    scheduler = TaskScheduler()
    scheduler_task = asyncio.create_task(scheduler.aschedule(execution_state.task_queue))
    logger.info("Task scheduler is created.")
    # 异步调用规划器，按步骤流水执行
    responses = app_bailian.app_stream_call(app=BailianAppEnum.planner, prompt=execution_state.question,
                                            biz_params=biz_params)
    planner_start_ts = time.time()
    async for result, _ in responses:
        # 优雅退出
        if execution_state.fast_path_scenario is not None:
            logger.info("Planner exit due to fast path.")
            return
        # 跳过空输出
        if is_blank(result):
            continue
        # 将输出内容追加到字符串中
        text = execution_state.plan_content + result
        # 分析输出内容，获取未完成的 JSON 对象
        partial_list, size = extract_items_from_incomplete_json(text[execution_state.analyzed_chars_of_plan + 1:])
        # 将规划的步骤加入执行队列
        for step in partial_list:
            if step['id'] not in execution_state.analyzed_steps:
                task = Task(
                    idx=int(step['id']),
                    name=step.get('tool', ''),
                    func=_step_execute,  # 固定调用该方法，具体的工具执行在方法内部决定
                    args=[step, execution_state],  # 默认传入该步骤的规划和整体的状态
                    dependencies=[int(i) for i in step['dependence']],
                )
                await execution_state.task_queue.put(task)
                execution_state.analyzed_steps.add(step['id'])
                logger.info(f"Step {step.get('id')} with tool {step.get('tool')} is put to task queue.")
        # 维护状态，更新已处理的字符数和输出内容
        execution_state.analyzed_chars_of_plan += size
        execution_state.plan_content = text
    planner_stop_ts = time.time()
    logger.info(f"Call of planner costs {planner_stop_ts - planner_start_ts:.3f} s.")
    # 注入结束任务，等待调度器停止
    await execution_state.task_queue.put(None)
    await scheduler_task
    logger.info("Task scheduler is stopped.")


@catch_all_async_errors
async def planner_test(execution_state: ExecutionState) -> str:
    # 准备planner的参数
    biz_params = {
        "currentTime": time_utils.current_time_with_week(),
        "tools": execution_state.tool_provider.get_tool_summary()
    }
    # 调用规划器
    content = await app_bailian.app_call(app=BailianAppEnum.planner, prompt=execution_state.question,
                                         biz_params=biz_params)
    return repair_json_output(extract_json_code_block(content))


@catch_all_async_errors
async def param_extractor_test(execution_state: ExecutionState) -> str:
    # 准备参数提取器的参数
    step = json.loads(execution_state.question)
    tool = execution_state.tool_provider.get_tool(step['tool'])
    biz_params = {
        "tool": step.get('tool', ''),
        "input": step.get('input', ''),
        "analysis": step.get('analysis', ''),
        "dependence": step.get('dependence', '无'),
        "tool_doc": execution_state.tool_provider.get_tool_input_description(tool)
    }

    # 调用参数提取器获取生成的参数并修复可能的错误
    param_content = await app_bailian.app_call(app=BailianAppEnum.param_extractor, prompt="ecs-deep-diagnose",
                                               biz_params=biz_params)
    return repair_json_output(extract_json_code_block(param_content))


@catch_all_async_errors
async def _step_execute(step: dict, state: ExecutionState) -> None:
    """
    异步执行单个步骤的核心逻辑，包括参数提取、验证及工具调用。

    参数:
        step (dict): 当前步骤的定义，包含步骤ID、工具名称、输入内容、分析结果、依赖步骤等。
        state (ExecutionState): 当前任务的执行状态，包含步骤状态、工具映射、任务队列等信息。

    返回值:
        None: 该函数没有返回值，执行结果通过 state.step_states 中的 StepState 对象记录。
    """
    logger.info(f"Executing step: {step}")

    # 快速路径优雅退出
    if state.fast_path_scenario is not None:
        logger.info(f"Executor of step {step["id"]} exit due to fast path.")
        return
    # 创建步骤状态对象并注册到执行状态中
    step_state = StepState(step)
    step_state.status = "Executing"
    state.step_states[step_state.id] = step_state

    # 跳过空的工具（代表纯思考步骤）
    if is_blank(step_state.tool):
        step_state.tool = None
        step_state.status = "Success"
        return

    # 跳过不存在的工具
    if state.tool_provider.get_tool(step_state.tool) is None:
        logger.error(f"{step_state.tool} tool is not found.")
        step_state.error = "指定的工具不存在"
        step_state.status = "Fail"
        return

    # 准备工具和参数提取器的业务参数
    tool = state.tool_provider.get_tool(step_state.tool)
    biz_params = {
        "tool": step_state.tool,
        "input": step_state.input,
        "analysis": step_state.analysis,
        "dependence": _collect_dependency_results(state, step),
        "tool_doc": state.tool_provider.get_tool_input_description(step_state.tool)
    }
    context = biz_params['input'] + biz_params['analysis'] + biz_params['dependence']
    partial_list_generator = None

    # 尝试使用正则进行参数提取
    if state.param_extract_with_regex:
        extracted_params = ContextualParamManager().extract_param(context, tool.tool)
        if extracted_params:
            partial_list_generator = _convert_list_async([[extracted_params]])
            logger.info(f"Regex extractor successfully extracts parameters {extracted_params}")

    # 使用LLM参数提取器生成参数
    if not partial_list_generator:
        responses = app_bailian.app_stream_call(app=BailianAppEnum.param_extractor, prompt="ecs-deep-diagnose",
                                                biz_params=biz_params)
        partial_list_generator = _generate_partial_list(responses)

    # 流水执行工具调用
    tasks = []
    async for partial_list in partial_list_generator:
        # 快速路径优雅退出
        if state.fast_path_scenario is not None:
            logger.info(f"Executor of step {step['id']} exit due to fast path.")
            return
        # 执行工具调用
        for input_params in partial_list:
            tool_state = ToolState(input_params)
            step_state.tool_states.append(tool_state)
            task = asyncio.create_task(_execute_tool(tool_state, tool, context))
            tasks.append(task)

    # 等待所有任务完成
    await asyncio.gather(*tasks)
    # 修改状态
    if all(tool_state.status == "Success" for tool_state in step_state.tool_states):
        step_state.status = "Success"
    else:
        step_state.status = "Fail"


async def _execute_tool(state: ToolState, tool: Tool, validation_context: str):
    # 调整优化时间范围
    input_params = _adjust_time_range(state.input)
    state.input = json.dumps(input_params, ensure_ascii=False)

    # 校验生成的参数，防止幻觉
    validation_error = ContextualParamManager().validate_params(input_params, validation_context)
    if validation_error is not None:
        state.error = "生成的工具参数未通过校验：" + validation_error
        state.status = "Fail"
        logger.error(f"Param validation failed. {validation_error}")
        return
    logger.info(f"Invoking tool {tool.get_name()} with params {input_params}.")

    # 执行方法
    try:
        state.result = await tool.ainvoke(input_params)
        state.result = "None" if not state.result else state.result
        state.status = "Success"
    except Exception as e:
        logger.error(f"Error when executing tool {tool.get_name()}: {e}", exc_info=True)
        state.error = "工具调用时报错，错误信息：" + str(e)
        state.status = "Fail"
        return


def _collect_dependency_results(state: ExecutionState, step: dict) -> str:
    """
    收集当前步骤所依赖的其他步骤的执行结果。

    参数:
        state (ExecutionState): 当前任务的执行状态对象，包含所有步骤的执行结果。
        step (dict): 当前步骤的定义，其中可能包含依赖的其他步骤ID列表。

    返回:
        str: 所有依赖步骤的执行结果拼接成的字符串，每条结果之间用换行符分隔。
           如果当前步骤没有依赖或所有依赖都不存在，返回'无'。
    """
    dependencies = step.get('dependence', [])
    if not dependencies:
        return '无'
    result = []
    for key in dependencies:
        # 检查键是否存在，避免KeyError
        if key in state.step_states:
            step_state = state.step_states[key]
            for tool_state in step_state.tool_states:
                result.append(str(tool_state.result))
        else:
            # 如果依赖的键不存在，记录警告但继续执行
            logger.warning(f"Dependency step {key} not found in step_states.")
    return '\n'.join(result)


def _adjust_time_range(time_dict: dict) -> dict:
    """
    检查并调整时间范围，确保 endTime 比 startTime 晚至少2小时。

    参数:
        time_dict (dict): 包含 startTime 和 endTime 字段的字典，格式为 yyyy-MM-dd HH:mm:ss

    返回:
        dict: 调整后的时间字典
    """
    import datetime

    # 获取时间字段
    start_time_str = time_dict.get('startTime', '') or time_dict.get('start_time', '')
    end_time_str = time_dict.get('endTime', '') or time_dict.get('end_time', '')

    if is_blank(start_time_str) or is_blank(end_time_str):
        # 如果不存在时间字段，则直接返回原始字典
        return time_dict

    try:
        # 解析时间字符串
        start_time = datetime.datetime.strptime(start_time_str, '%Y-%m-%d %H:%M:%S')
        end_time = datetime.datetime.strptime(end_time_str, '%Y-%m-%d %H:%M:%S')
    except ValueError as e:
        logger.warning(f"Invalid time format in adjust_time_range: {e}")
        return time_dict

    # 如果 endTime 小于等于 startTime，则交换两个时间
    if end_time <= start_time:
        start_time, end_time = end_time, start_time

    # 确保 endTime 比 startTime 晚至少2小时，否则前后各扩展1小时
    if end_time < start_time + datetime.timedelta(hours=2):
        start_time = start_time + datetime.timedelta(hours=-1)
        end_time = end_time + datetime.timedelta(hours=1)

    # 更新字典中的时间值，支持两种格式
    if "startTime" in time_dict and "endTime" in time_dict:
        time_dict['startTime'] = start_time.strftime('%Y-%m-%d %H:%M:%S')
        time_dict['endTime'] = end_time.strftime('%Y-%m-%d %H:%M:%S')
    if "start_time" in time_dict and "end_time" in time_dict:
        time_dict['start_time'] = start_time.strftime('%Y-%m-%d %H:%M:%S')
        time_dict['end_time'] = end_time.strftime('%Y-%m-%d %H:%M:%S')

    return time_dict


async def _generate_partial_list(responses) -> AsyncGenerator[list, None]:
    """
    异步生成器，用于从流式响应中逐步提取对象

    Args:
        responses: 异步响应流

    Yields:
        partial_list 提取到的对象组成的列表
    """
    analyzed_chars = 0
    param_contents = ""
    async for param_content_part, _ in responses:
        # 跳过空输出
        if is_blank(param_content_part):
            continue
        # 将输出内容追加到字符串中
        param_contents = param_contents + param_content_part
        # 分析输出内容，获取未完成的 JSON 对象
        partial_list, size = extract_items_from_incomplete_json(param_contents[analyzed_chars:])
        # 更新变量
        analyzed_chars += size
        # 产出提取到的对象列表
        yield partial_list


async def _convert_list_async(items):
    for item in items:
        yield item
