"""
Quick inspect of a machine, include: instance, host
"""

import asyncio
import logging
from typing import AsyncIterator, Dict, Any, List, Optional
from datetime import datetime, timedelta

from deep_diagnose.common.utils.machine_utils import MachineIdUtil
from deep_diagnose.core.events.inspect_event import InspectEvent
from deep_diagnose.core.interactive.workflows.inspect_workflow import stream_inspect_workflow
from deep_diagnose.core.agent.base_graph import BaseGraph


logger = logging.getLogger(__name__)


class InspectAgent(BaseGraph):
    """
    Inspect Agent - 机器状态检查智能体
    """
    
    def __init__(self, config):
        super().__init__(config)
        

        self._langfuse_handler = config.get("langfuse_handler") if config else None

    async def astream(self, question: str, messages: Optional[List[Dict[str, Any]]] = None, **kwargs) -> AsyncIterator[InspectEvent]:
        """
        主执行流程：事件驱动模式
        
        注意：该方法已重构为使用新的 LangGraph 工作流。
        内部调用 stream_inspect_workflow，支持 Langfuse 自动跟踪。
        
        Args:
            question: 用户问题（兼容性参数）
            messages: 历史消息列表（兼容性参数）
            **kwargs: 其他参数，包含 additional_info
            
        Yields:
            InspectEvent: 每次更新的事件
        """
        additional_info = kwargs.get("additional_info", {})
        
        # 提取参数
        machine_id = additional_info.get("machine_id", "")
        start_time = additional_info.get("start_time", "")
        end_time = additional_info.get("end_time", "")
        
        # 设置默认时间
        if start_time == "":
            start_time = (datetime.now() - timedelta(days=2)).strftime("%Y-%m-%d %H:%M:%S")
        if end_time == "":
            end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 生成会话标识符
        session_id = kwargs.get("session_id", "")
        user_id = kwargs.get("user_id", "")
        trace_id = kwargs.get("request_id", "")
        
        logger.info(f"Initiating inspect workflow for machine_id={machine_id}, time_range=[{start_time}, {end_time}]")
        
        try:
            # 使用新的流式工作流
            async for event in stream_inspect_workflow(
                machine_id=machine_id,
                start_time=start_time,
                end_time=end_time,
                session_id=session_id,
                user_id=user_id,
                trace_id=trace_id,
                with_memory=False,  # 默认不使用内存
                langfuse_handler=self._langfuse_handler,
            ):
                yield event
                
        except Exception as e:
            logger.error(f"Inspect workflow execution failed for machine_id={machine_id}: {e}", exc_info=True)
            
            # 返回错误事件
            error_event = InspectEvent()
            error_event.set_machine_info(
                machine_id,
                start_time,
                end_time,
                MachineIdUtil.get_machine_id_type(machine_id)
            )
            error_event.overview = f"Workflow execution failed: {str(e)}"
            error_event.finished = True
            yield error_event




if __name__ == "__main__":
    agent = InspectAgent({})
    test_resource = {
        "machine_id": "i-8vbauim5xf7ivezxxouc",
        "start_time": "2025-08-21 01:29:38",
        "end_time": "2025-08-21 22:57:55",
    }
    
    async def main():
        async for event in agent.astream("123", additional_info=test_resource):
            print(event.to_sse_format())
    
    asyncio.run(main())

