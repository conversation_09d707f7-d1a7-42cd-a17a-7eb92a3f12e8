# coding=utf-8

import base64
import hashlib

from Crypto import Random
from Crypto.Cipher import AES
from Crypto.Cipher import PKCS1_v1_5 as pk
from Crypto.Hash import MD5
from Crypto.Hash import SHA
from Crypto.Hash import SHA224
from Crypto.Hash import SHA256
from Crypto.Hash import SHA384
from Crypto.Hash import SHA512
from Crypto.PublicKey import RSA
from Crypto.Signature import PKCS1_v1_5

from deep_diagnose.security.keycenter import KeyCenterError
from deep_diagnose.security.keycenter import kc_key_sync as sync

# supported algorithm list
g_supported_encrypt = {"KeyCenter_AES": "_aes_encrypt", "KeyCenter_RSA": "_rsa_encrypt"}
g_supported_decrypt = {"KeyCenter_AES": "_aes_decrypt", "KeyCenter_RSA": "_rsa_decrypt"}
g_supported_sign_algorithm = ["KeyCenter_SHA", "KeyCenter_MD5", "KeyCenter_RSA"]
g_supported_sha_type = ["SHA1", "SHA224", "SHA256", "SHA384", "SHA512"]
g_supported_rsa_sign_type = ["MD5withRSA", "SHA1withRSA", "SHA224withRSA", "SHA256withRSA", "SHA384withRSA",
                             "SHA512withRSA"]


def _get_key_detail(key_name):
    """
    get key detail by key name
    :param key_name: name of key, created on keycenter console
    """
    key_dict = sync.get_key_dict()
    if not key_name or not key_name.replace(' ', ''):
        raise KeyCenterError('key_name is empty')
    return key_dict.get(key_name)


def encrypt(key_name, plain_text):
    global g_supported_encrypt
    key_detail = _get_key_detail(key_name)
    if not key_detail:
        raise KeyCenterError('key_name %s is not exist' % key_name)
    algorithm = key_detail["algorithm"]
    if algorithm not in g_supported_encrypt:
        raise KeyCenterError('python client can not support %s encrypt' % algorithm)
    encrypted = eval(g_supported_encrypt[algorithm])(key_detail, plain_text)
    return encrypted


def decrypt(key_name, encrypted_text):
    global g_supported_decrypt
    key_detail = _get_key_detail(key_name)
    if not key_detail:
        raise KeyCenterError('key_name %s is not exist' % key_name)
    algorithm = key_detail["algorithm"]
    if algorithm not in g_supported_decrypt:
        raise KeyCenterError('python client can not support %s decrypt' % algorithm)
    decrypted = eval(g_supported_decrypt[algorithm])(key_detail, encrypted_text)
    return decrypted


def sign(key_name, plain_text):
    global g_supported_sign_algorithm
    key_detail = _get_key_detail(key_name)
    if not key_detail:
        raise KeyCenterError('key_name %s is not exist' % key_name)
    algorithm = key_detail["algorithm"]
    if algorithm not in g_supported_sign_algorithm:
        raise KeyCenterError('python client can not support %s sign' % algorithm)
    if algorithm == "KeyCenter_RSA":
        signed = _rsa_sign(key_detail, plain_text)
    else:
        signed = _hash_sign(key_detail, plain_text)
    return signed


def verify(key_name, plain_text, sign_text):
    key_detail = _get_key_detail(key_name)
    algorithm = key_detail["algorithm"]
    if algorithm == "KeyCenter_RSA":
        if _rsa_verify(key_detail, plain_text, sign_text):
            return 'verify success'
        else:
            return 'verify failed'
    else:
        signed = sign(key_name, plain_text)
        if base64.b64encode(signed.encode('utf-8')) == sign_text:
            return 'verify success'
        else:
            return 'verify failed'


def _rsa_sign(key_detail, plain_text):
    key = RSA.importKey(base64.b64decode(key_detail["content"]))
    if key_detail["workingMetaJson"] == '{}':
        sign_type = "SHA1withRSA"
    else:
        sign_type = key_detail["workingMetaJson"]["signType"]
    if sign_type not in g_supported_rsa_sign_type:
        raise KeyCenterError('python client can not support %s signType' % hash_type)
    if sign_type == "SHA1withRSA":
        h = SHA.new()
    elif sign_type == "SHA224withRSA":
        h = SHA224.new()
    elif sign_type == "SHA256withRSA":
        h = SHA256.new()
    elif sign_type == "SHA384withRSA":
        h = SHA384.new()
    elif sign_type == "SHA512withRSA":
        h = SHA512.new()
    elif sign_type == "MD5withRSA":
        h = MD5.new()
    signer = PKCS1_v1_5.new(key)
    h.update(plain_text)
    return signer.sign(h)


def _rsa_verify(key_detail, plain_text, sign_text):
    sign = base64.b64decode(sign_text)
    key = RSA.importKey(base64.b64decode(key_detail["content"]))
    if key_detail["workingMetaJson"] == '{}':
        sign_type = "SHA1withRSA"
    else:
        sign_type = key_detail["workingMetaJson"]["signType"]
    if sign_type not in g_supported_rsa_sign_type:
        raise KeyCenterError('python client can not support %s signType' % hash_type)
    if sign_type == "SHA1withRSA":
        h = SHA.new()
    elif sign_type == "SHA224withRSA":
        h = SHA224.new()
    elif sign_type == "SHA256withRSA":
        h = SHA256.new()
    elif sign_type == "SHA384withRSA":
        h = SHA384.new()
    elif sign_type == "SHA512withRSA":
        h = SHA512.new()
    elif sign_type == "MD5withRSA":
        h = MD5.new()
    verifier = PKCS1_v1_5.new(key)
    h.update(plain_text)
    return verifier.verify(h, sign)


def _hash_sign(key_detail, plain_text):
    algorithm = key_detail["algorithm"]
    if algorithm == "KeyCenter_MD5":
        hash = hashlib.md5(plain_text.encode('utf-8'))
    elif algorithm == "KeyCenter_SHA":
        hash_type = key_detail["workingMetaJson"]["hashType"]
        if hash_type not in g_supported_sha_type:
            raise KeyCenterError('python client can not support %s signType' % hash_type)
        if hash_type == "SHA1":
            hash = hashlib.sha1(plain_text.encode('utf-8'))
        elif hash_type == "SHA224":
            hash = hashlib.sha224(plain_text.encode('utf-8'))
        elif hash_type == "SHA256":
            hash = hashlib.sha256(plain_text.encode('utf-8'))
        elif hash_type == "SHA384":
            hash = hashlib.sha384(plain_text.encode('utf-8'))
        elif hash_type == "SHA512":
            hash = hashlib.sha512(plain_text.encode('utf-8'))
    hash.update(sync.base64.b64decode(key_detail["content"]))
    signed = hash.digest()
    return signed


def _aes_encrypt(key_detail, plain_text):
    cipher = None
    bs = AES.block_size
    pad = lambda s: s + (bs - len(s) % bs) * chr(bs - len(s) % bs)

    if key_detail["workingMetaJson"]["workMode"] == "ECB":
        cipher = AES.new(sync.base64.b64decode(key_detail["content"]), AES.MODE_ECB)
    if not cipher:
        raise KeyCenterError('python client can not support aes mode ' % key_detail["workingMetaJson"]["workMode"])
    content = cipher.encrypt(pad(plain_text).encode("utf8"))
    return content


def _aes_decrypt(key_detail, encrypted_text):
    cipher = None
    unpad = lambda s: s[0:-ord(s[-1])]

    if key_detail["workingMetaJson"]["workMode"] == "ECB":
        cipher = AES.new(sync.base64.b64decode(key_detail["content"]), AES.MODE_ECB)
    if not cipher:
        raise KeyCenterError('python client can not support aes mode ' % key_detail["workingMetaJson"]["workMode"])
    content = cipher.decrypt(encrypted_text)
    return content


def _rsa_encrypt(key_detail, plain_text):
    PubKey = RSA.importKey(base64.b64decode(key_detail["content"]))

    cipher = pk.new(PubKey)
    content = cipher.encrypt(plain_text)
    return content


def _rsa_decrypt(key_detail, encrypted_text):
    if key_detail["workingMetaJson"] == '{}':
        sign_type = "SHA1withRSA"
    else:
        sign_type = key_detail["workingMetaJson"]["signType"]
    if sign_type not in g_supported_rsa_sign_type:
        raise KeyCenterError('python client can not support %s signType' % hash_type)
    if sign_type == "SHA1withRSA":
        dsize = SHA.digest_size
    elif sign_type == "SHA224withRSA":
        dsize = SHA224.digest_size
    elif sign_type == "SHA256withRSA":
        dsize = SHA256.digest_size
    elif sign_type == "SHA384withRSA":
        dsize = SHA384.digest_size
    elif sign_type == "SHA512withRSA":
        dsize = SHA512.digest_size
    elif sign_type == "MD5withRSA":
        dsize = MD5.digest_size
    PriKey = RSA.importKey(base64.b64decode(key_detail["content"]))
    sentinel = Random.new().read(15 + dsize)
    cipher = pk.new(PriKey)
    content = cipher.decrypt(encrypted_text, sentinel)
    return content


if __name__ == "__main__":
    encrypted = encrypt("keycenter_demo_aes", "hello world")
    print(decrypt("ecs-deep-diagnose_aone_key", encrypted))
