import hashlib
import hmac
import json
import re
from urllib.parse import quote, unquote

from fastapi import Request
from starlette.datastructures import QueryParams

from deep_diagnose.security.auth.constants import X_ACS_AUTH_HEADER_NAME
from deep_diagnose.api.models.user import UserModel

LINE_SEPARATOR = "\n"
HEADER_CONTENT_HASH = "x-acs-content-sha256"
HEADER_AUTHORIZATION = "Authorization"
SIGN_PROTOCOL = "ACS3-HMAC-SHA256"


class POPAuth:

    async def authenticate(self, request: Request):
        x_acs_request_id = request.headers.get(X_ACS_AUTH_HEADER_NAME)
        if x_acs_request_id is not None:
            return True
        else:
            return False

    async def get_user(self, request: Request):
        x_acs_request_id = request.headers.get(X_ACS_AUTH_HEADER_NAME)
        if x_acs_request_id:
            return UserModel(
                access_key="x_acs_request_id",
                user_id="x_acs_request_id",
                user_name="",
                user_type="pop",
                pop_user="x_acs_request_id",
            )
        else:
            return None


def is_from_pop(request: Request) -> bool:
    auth_str = request.headers.get(HEADER_AUTHORIZATION)
    if auth_str is None:
        return False
    match = re.match(
        r"(\S+) Credential=(\S+),SignedHeaders=(\S+),Signature=(\S+)", auth_str
    )
    if not match:
        return False
    algorithm, credential, signed_headers, signature = (
        match.group(1),
        match.group(2),
        match.group(3),
        match.group(4),
    )

    canonical_request = build_canonical_request(request, signed_headers)
    string_to_sign = generate_string_to_sign(canonical_request)
    server_sign = do_sign(string_to_sign)

    return server_sign == signature


def generate_string_to_sign(canonical_request: str):
    return (
        SIGN_PROTOCOL
        + LINE_SEPARATOR
        + hex_encoded_hash(canonical_request.encode("utf-8"))
    )


def do_sign(string_to_sign: str):
    secret = ""  # Env.get("ali.pop.verify_secret").encode('utf-8')
    hmac_instance = hmac.new(secret, digestmod="sha256").copy()
    hmac_instance.update(string_to_sign.encode("utf-8"))
    return "".join("{:02x}".format(x) for x in hmac_instance.digest())


def build_canonical_request(request: Request, signed_headers: str) -> str:
    return (
        request.method
        + LINE_SEPARATOR
        + get_canonical_uri(request.url.path)
        + LINE_SEPARATOR
        + get_canonical_query_string(request.query_params)
        + LINE_SEPARATOR
        + get_canonical_headers(request, signed_headers)
        + LINE_SEPARATOR
        + signed_headers
        + LINE_SEPARATOR
        + request.headers.get(HEADER_CONTENT_HASH)
    )


def get_canonical_uri(request_uri) -> str:
    if not request_uri:
        return "/"
    try:
        # 解码URL，然后用percent-encoding重新编码，替换空格为%20
        return percent_encode_uri(request_uri)
    except Exception as e:
        # 如果有异常，直接将异常抛出
        raise RuntimeError(e)


def percent_encode_uri(uri: str) -> str:
    return percent_encode_param(uri).replace("%2F", "/")


def percent_encode_param(param: str) -> str:
    return quote(unquote(param), safe="")


def to_list_of_str(s):
    try:
        # 尝试使用json.loads()方法转换字符串
        result = json.loads(s)
        # 如果结果是一个列表，我们返回它
        if isinstance(result, list):
            return [json.dumps(result)]
        # 如果结果是其他数据类型，我们将其包装在一个列表中
        else:
            return [str(result)]
    except json.JSONDecodeError:
        # 如果json.loads()失败，我们假设输入是一个单一的字符串
        # 并将其包装在一个列表中
        return [s]


def get_canonical_query_string(query_params: QueryParams) -> str:
    if query_params is None:
        return ""
    encoded_params = {}
    for k, v in query_params.items():
        encoded_param_name = percent_encode_param(k)
        param_values = to_list_of_str(v)
        encoded_values = [
            percent_encode_param(value) if value is not None else ""
            for value in param_values
        ]
        encoded_values.sort()
        encoded_params[encoded_param_name] = encoded_values

    result = []
    for key, values in sorted(encoded_params.items()):
        for value in values:
            result.append(f"{key}={value}")
    return "&".join(result)


def get_canonical_headers(request: Request, signed_headers: str) -> str:
    if not signed_headers:
        return ""
    signed_header_list = signed_headers.split(";")
    result = []
    for header_name in signed_header_list:
        values = request.headers.getlist(header_name)
        if values:
            values.sort()
            header_value = ",".join(value.strip() for value in values)
        else:
            header_value = ""
        result.append(f"{header_name}:{header_value}")
    return "\n".join(result) + "\n"


def hex_encoded_hash(input: bytes):
    digest = hashlib.sha256()
    digest.update(input)
    hashed_bytes = digest.digest()
    return "".join("{:02x}".format(x) for x in hashed_bytes)
