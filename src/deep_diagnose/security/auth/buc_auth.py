from fastapi import Request

from deep_diagnose.api.models.user import UserModel


class BUCAuth:

    async def authenticate(self, request: Request) -> bool:
        """
        异步认证方法，用于检查用户会话是否已认证。

        参数:
        - self: 表示实例自身。
        - request: Request 类型，表示当前的请求对象。

        返回值:
        - bool: 如果用户会话已认证，则返回 True；否则返回 False。
        """

        user_info = request.session.get("user_info")  # 尝试从会话中获取用户信息

        session_authentication = False  # 默认会话未认证

        if user_info != None:  # 检查用户信息是否存在
            session_authentication = True  # 如果用户信息存在，则会话已认证

        return session_authentication  # 返回会话认证状态


    async def get_user(self, request: Request) -> UserModel:
        """
        异步获取当前用户的信息。

        参数:
        - self: 表示实例自身。
        - request: Request 类型，表示当前的请求对象。

        返回值:
        - UserModel: 如果用户已登录，返回一个 UserMode 实例，包含用户的基本信息；如果用户未登录，返回 None。
        """
        user_info = request.session.get("user_info")  # 尝试从会话中获取用户信息
        if user_info is not None:
            # 如果用户信息存在，则构造并返回一个 UserModel 实例
            return UserModel(
                access_key="",
                user_id=user_info["id"],
                user_name=user_info["realname"],
                user_type="login",
            )
        else:
            # 如果用户信息不存在，表示用户未登录，返回 None
            return None

