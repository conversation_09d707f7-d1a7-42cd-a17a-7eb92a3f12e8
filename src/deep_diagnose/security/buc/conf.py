# -*- coding: utf-8 -*-
import deep_diagnose.common.configparser
import glob


class Conf():

    def strip(self, string):
        return string.strip('"').strip("'")

    def __init__(self, conf=''):
        self.config = configparser.ConfigParser()
        self.config.read(glob.glob('conf/other.cfg'))
        if conf != '':
            self.config.read(conf)
        print(conf)

    def get_buc_sso_host(self):
        return self.strip(self.config.get('buc', 'host'))
