"""
Enhanced YAML configuration loader.
"""

import os
import yaml
import base64
import functools
from typing import Dict, Any, Optional
from ..core.base_config import BaseConfig
from ..core.environment import get_environment, get_config_file_name
from ..core.security_config import security_config


# Custom YAML constructors for backward compatibility
class StringConcatenator(yaml.YAMLObject):
    yaml_loader = yaml.SafeLoader
    yaml_tag = '!join'

    @classmethod
    def from_yaml(cls, loader, node):
        seq = loader.construct_sequence(node)
        return functools.reduce(lambda a, b: a + b, seq)


class SecretDecrypter(yaml.YAMLObject):
    yaml_loader = yaml.SafeLoader
    yaml_tag = '!decrypt'

    @classmethod
    def from_yaml(cls, loader, node):
        seq = loader.construct_sequence(node)
        if len(seq) >= 2:
            return security_config.decrypt_secret(seq[0], seq[1])
        return seq[0] if seq else ""


class SecretDecoder(yaml.YAMLObject):
    yaml_loader = yaml.SafeLoader
    yaml_tag = '!decode'

    @classmethod
    def from_yaml(cls, loader, node):
        encoded_value = loader.construct_sequence(node)
        decoded_bytes = base64.b64decode(encoded_value[0])
        
        # Try UTF-8 first, fall back to latin-1 if that fails
        try:
            return decoded_bytes.decode('utf-8')
        except UnicodeDecodeError:
            # For binary data or non-UTF-8 encoded passwords, use latin-1
            # which can decode any byte sequence
            return decoded_bytes.decode('latin-1')


class PathSetter(yaml.YAMLObject):
    yaml_loader = yaml.SafeLoader
    yaml_tag = '!get_path'

    @classmethod
    def from_yaml(cls, loader, node):
        file_path = loader.construct_sequence(node)
        work_path = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        file_abs_path = os.path.join(work_path, *file_path)
        return file_abs_path


class GetProdUrl(yaml.YAMLObject):
    yaml_loader = yaml.SafeLoader
    yaml_tag = '!get_prod_url'

    @classmethod
    def from_yaml(cls, loader, node):
        url_mapping = loader.construct_mapping(node)
        return url_mapping[os.getenv('app_env', 'prod')]


class YamlConfigLoader(BaseConfig):
    """
    Enhanced YAML configuration loader with environment variable support.
    """

    def __init__(self, config_file: Optional[str] = None, config_dir: Optional[str] = None):
        super().__init__()
        self.config_dir = config_dir or os.path.join(os.path.dirname(__file__), '..', 'files')
        self.config_file = config_file or get_config_file_name()
        self._config_path = os.path.join(self.config_dir, self.config_file)
    
    def load(self) -> Dict[str, Any]:
        """
        Load YAML configuration file with environment variable processing.

        Returns:
            Dict[str, Any]: Processed configuration data
        """
        # Initialize with empty config
        config = {}

        # Load from file if it exists
        if os.path.exists(self._config_path):
            with open(self._config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f) or {}

        # Process environment variables
        processed_config = self._process_env_vars(config)

        # Add runtime environment info (always add these)
        env = get_environment()
        processed_config["app_env"] = env.value
        processed_config["app_queue"] = f"ecs-deep-diagnose_{env.value}"

        return processed_config
    
    def _process_env_vars(self, config: Any) -> Any:
        """
        Recursively process configuration to replace environment variables.
        
        Args:
            config: Configuration data to process
            
        Returns:
            Any: Processed configuration data
        """
        if isinstance(config, dict):
            return {key: self._process_env_vars(value) for key, value in config.items()}
        elif isinstance(config, list):
            return [self._process_env_vars(item) for item in config]
        elif isinstance(config, str):
            return self._replace_env_var(config)
        else:
            return config
    
    def _replace_env_var(self, value: str) -> str:
        """
        Replace environment variable in string value.
        
        Args:
            value: String value that may contain environment variable
            
        Returns:
            str: Processed string value
        """
        if not isinstance(value, str):
            return value
        
        if value.startswith("$"):
            env_var = value[1:]
            return os.getenv(env_var, value)
        
        return value


# For backward compatibility with existing loader.py
def load_yaml_config(file_path: str) -> Dict[str, Any]:
    """
    Load and process YAML configuration file (legacy function).
    
    Args:
        file_path: Path to YAML configuration file
        
    Returns:
        Dict[str, Any]: Processed configuration data
    """
    loader = YamlConfigLoader(config_file=os.path.basename(file_path), 
                             config_dir=os.path.dirname(file_path))
    return loader.load()


def replace_env_vars(value: str) -> str:
    """Replace environment variables in string values (legacy function)."""
    loader = YamlConfigLoader()
    return loader._replace_env_var(value)


def process_dict(config: Dict[str, Any]) -> Dict[str, Any]:
    """Recursively process dictionary to replace environment variables (legacy function)."""
    loader = YamlConfigLoader()
    return loader._process_env_vars(config)
