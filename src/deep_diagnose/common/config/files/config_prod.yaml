app:
  port: 8000
  secret: ecs_deep_diagnose
  name: ecs-deep-diagnose
  home_directory: /home/<USER>/ecs-deep-diagnose

# Workflow configuration
workflow:
  max_steps: 100
  auto_accept_plan: true
  enable_background_investigation: false

auth:
  # 数据库认证开关 (false: 使用YAML认证, true: 使用数据库认证)
  enable_database_auth: true
  buc_sso:
    host: https://login.alibaba-inc.com
    app_code: 40cbff03-d950-4b8e-8f38-e7b14f4c631d
  jwt_users:
    - user_name: admin
      password: admin
    - user_name: viewer
      password: viewer

mcp_servers:
  fvt:
    protocol: streamable_http
    base_url: http://pre-xmca-cloudbot.aliyun-inc.com
    timeout: 20
    path: /fvt/mcp/
    token: M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU=
    auth: bearer
    enabled_tools:
      - createFvtJob
      - getFvtResource
      - getFvtSetCaseInfo
      - getFvtJobDetails

  basic_info:
    protocol: streamable_http
    base_url: http://xmca-cloudbot.aliyun-inc.com
    timeout: 20
    path: /basic_info/mcp/
    token: M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU=
    auth: bearer
    enabled_tools:
      - getVmBasicInfo
      - getNcBasicInfo
      - getDiskInfo
      - getUserInfo
      - listVmPerformanceMetrics
      - listVmHostHistory
      - listVmsOnNc
      - getVmHealthStatusHistory
      - listMonitorExceptions
      - listOnDutyStaffs
      - listCategorizedMonitorExceptions
      - listKnowledge
      - getVmLiveMigrateInfo
      - getCurrentDateTime

  nc_down_prod:
    protocol: streamable_http
    base_url: http://xmca-cloudbot.aliyun-inc.com
    timeout: 20
    path: /nc_down/mcp/
    token: M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU=
    auth: bearer
    enabled_tools:
      - analyzeVmcoreFromCore
      - analyzeVmcoreFromVirt
      - analyzeVmcoreFromJinlun

  nc_down_pre:
    protocol: streamable_http
    base_url: http://pre-xmca-cloudbot.aliyun-inc.com
    timeout: 20
    path: /nc_down/mcp/
    token: M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU=
    auth: bearer
    enabled_tools:
      - getNcDownRecord
      - getNcDownLog

  operation:
    protocol: streamable_http
    base_url: http://xmca-cloudbot.aliyun-inc.com
    timeout: 20
    path: /operation/mcp/
    token: M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU=
    auth: bearer
    enabled_tools:
      - listReportedOperationalEvents
      - listActionTrail
      - listOperationRuleMatchRecords
      - listOperationRecords
      - listChangeRecords
      - runOperationSubmit
      - runOpsEventPostpone
      - listColdMigrationRecords
      - listLiveMigrationRecords
      - runColdMigrationCheck
      - runLiveMigrationCheck

  diagnose:
    protocol: streamable_http
    base_url: http://xmca-cloudbot.aliyun-inc.com
    timeout: 20
    path: /diagnose/mcp/
    token: M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU=
    auth: bearer
    enabled_tools:
      - runVmUnavailableDiagnose
      - runDiagnose
      - runPerformanceDiagnose
      - runScreenShotDiagnose
      - runVmStartStopDiagnose
      - checkHealthForCustomerVms


  vm_coredump:
    protocol: streamable_http
    base_url: http://ecs-mcp.alibaba-inc.com
    path: /vm_coredump/mcp/
    token: ************************************************
    auth: token
    enabled_tools:
      - query_vm_coredump


  vm_migration:
    protocol: streamable_http
    base_url: https://ecs-mcp.alibaba-inc.com
    path: /vm_migration/mcp/
    token: ****************************************************
    auth: token
    enabled_tools:
      - query_vm_live_migration_eligibility
      - query_live_migration_eligibility_code_explanation
      - query_live_migration_records
      - query_latest_live_migration_record
      - query_user_live_migration_overview
      - query_live_migration_error_code_explanation
      - query_live_migration_performance_metrics
      - query_live_migration_advanced_features
      - query_cold_migration_records
# MCP 工具配置
mcp:
  # 是否启用Redis缓存 (true: 启用缓存, false: 每次都从MCP服务器获取)
  enable_cache: true



security:
  # 定义用于解密的公钥名锚点(&keycenter_pub_name)，供全局引用
  key_center_public_name: &keycenter_pub_name ecs-deep-diagnose_aone_key

  # 密钥库：存储所有需要加密管理的密钥
  secrets_vault:
    qwen_api_key: &qwen_ak AKIsqn3/M5NSMshB4OJ6ZqdQwyEpYj4DiMdBjEtOjcZSQ1ulaGqinPQmZRYQm9wJ
    langfuse_secret_key: &langfuse_secret_key Jp9iO1h92IOBdD++hf0baAG0KuUGVJDYbKDfTXAAopKTrMlfX2pnqmUwLJSGcZNv
    langfuse_public_key: &langfuse_public_key blWQfahUx4KUxcLhdBprNYLUI7rsSEwe8PBid0uf4dXegx+KNErFY5ctczMunh/j
    osc_secret_key: &osc_secret_key t0MHBPKMf0nU4srsLeAEnW+vC54Iw3sOnPv4n8NXw6Y=
    redis_sk: &redis_sk ZWNzX2RlZXBfZGlhZ25vc2VAMTIzNA==
    rds_sk: &rds_sk 2+oo3wqbT+11XZ25A2Go3mAKDSgc8OOFl2c+mLUeNF8=
    postgre_password: &postgre_password JXUh1Atnvz3MbaaD7qFcQSbDrdWqA+IZNyZhAa2nRTI=
    dingtalk_sk: &dingtalk_sk /55yCX4/MXDlJlZOgk9UIG95pgr1JW700pNDuckmpCXWY6c7chnsPjHsmnreuOaQ75z9AFtXicqTBHz3DiAAQihImLTmlc9na2psdXQmU3U=
    fc_secret: &fc_secret hhuptLT01JPLRrkwWY2U7C1AHRfayyE7n7EZe3oPNGQ=


# SOP服务配置
sop_service_config:
  strategy_type: llm  # 可选: llm, similarity
  storage_type: filesystem  # 可选: filesystem, pgvector
  config:
    storage_config: {}
      # config_path: 使用默认路径计算，无需手动指定
    strategy_config:
      similarity_threshold: 0.6
      semantic_weight: 0.7
      example_weight: 0.3
      default_sop_id: general_troubleshooting
      embedding_config:
        api_key: !decrypt [*qwen_ak, *keycenter_pub_name] # 如果使用similarity策略，需要配置
        model: text-embedding-v4
llm:
  tongyi_provider:
    base_url: https://dashscope.aliyuncs.com/compatible-mode/v1
    api_key: !decrypt [*qwen_ak, *keycenter_pub_name]

  profiles:
    reasoning:
      base_url: https://dashscope.aliyuncs.com/compatible-mode/v1
      model: "qwen3-235b-a22b"
      api_key: !decrypt [ *qwen_ak, *keycenter_pub_name ]
      fallback_models: [ "qwen-plus-latest" ]
    basic:
      base_url: https://dashscope.aliyuncs.com/compatible-mode/v1
      model: "qwen-plus-latest"
      api_key: !decrypt [ *qwen_ak, *keycenter_pub_name ]
      fallback_models: [ ]
    vision:
      base_url: https://dashscope.aliyuncs.com/compatible-mode/v1
      model: "qwen-max-latest"
      api_key: !decrypt [ *qwen_ak, *keycenter_pub_name ]
      fallback_models: [ "qwen-plus-latest" ]
    code:
      base_url: https://dashscope.aliyuncs.com/compatible-mode/v1
      model: "qwen3-coder-plus"
      api_key: !decrypt [ *qwen_ak, *keycenter_pub_name ]
      fallback_models: ["qwen3-coder-flash", "qwen-plus-latest"]
    code_flash:
      base_url: https://dashscope.aliyuncs.com/compatible-mode/v1
      model: "qwen3-coder-flash"
      api_key: !decrypt [ *qwen_ak, *keycenter_pub_name ]
      fallback_models: ["qwen-plus-latest"]
    glm:
      base_url: https://dashscope.aliyuncs.com/compatible-mode/v1
      model: "glm-4.5"
      api_key: !decrypt [ *qwen_ak, *keycenter_pub_name ]
      fallback_models: [ "qwen-plus-latest" ]

# Embedding配置
embedding:
  model: text-embedding-v4
  api_key: !decrypt [ *qwen_ak, *keycenter_pub_name ]
  dimension: 2048


observability:
  langfuse:
    public_key: !decrypt [*langfuse_public_key, *keycenter_pub_name]
    secret_key: !decrypt [*langfuse_secret_key, *keycenter_pub_name]
    endpoint: https://chatecs-trace.alibaba-inc.com

infrastructure:
  e2b_sandbox:
    alibaba_cloud:
      access_key_id: "LTAI5t8xzQMmRixUTfa1NhTP"
      access_key_secret: !decrypt [*fc_secret, *keycenter_pub_name]
      endpoint: "1350630971860093.cn-shanghai.fc.aliyuncs.com"
      bearer_token: "test-fc-bearer-token-12345"  # Bearer token for FC authentication (临时测试)
    container:
      image: "registry.cn-shanghai.aliyuncs.com/fc-demo2/custom-container-repository:E2B-Base-V1"
    resources:
      cpu: 0.35
      memory: 512
    network:
      security_group_id: ""
      v_switch_id: ""
      vpc_id: ""
    logging:
      log_store: ""
      log_project: ""
  oss:
    endpoint: oss-cn-hangzhou-zmf.aliyuncs.com
    region: oss-cn-hangzhou-zmf
    bucket_name: xdragon-metric-zmf
    access_key_id: LTAI5tLPKJc7C3RcQtAqiWvk
    access_key_secret: !decrypt [*osc_secret_key, *keycenter_pub_name ]
    upload_dir: ecs-deep-diagnose/diagnostic-report

  redis:
    host: r-ecs-deep-diagnose.redis.rds.aliyuncs.com
    port: 6379
    user: r-ecs_deep_diagnose
    password: !decode &password [*redis_sk]
    broker_url:
      !join [
        "redis://r-ecs_deep_diagnose:",
        *password,
        "@r-ecs-deep-diagnose.redis.rds.aliyuncs.com:6379/2",
      ]
    result_backend_url:
      !join [
        "redis://r-ecs_deep_diagnose:",
        *password,
        "@r-ecs-deep-diagnose.redis.rds.aliyuncs.com:6379/3",
      ]
    refresh_time: 1800
  rds:
    host: rm-ecs-deep-diagnose.mysql.rds.aliyuncs.com
    port: 3306
    user: cloudbotagent_user
    pwd: !decrypt [*rds_sk, *keycenter_pub_name]
    db: cloudbotagent

  postgres:
    host: pgm-0jl0cs369ffti2a9.pg.rds.aliyuncs.com
    port: 5432
    user: ecs_deep_diagnose
    password:  !decrypt [ *postgre_password, *keycenter_pub_name ]
    database: ecs_deep_diagnose


dingtalk:
  token_url: https://oapi.dingtalk.com/gettoken
  send_message_url: https://oapi.dingtalk.com/topapi/message/send
  corp_secret: !decrypt [*dingtalk_sk, *keycenter_pub_name ]
  corp_id: ding7kpnyytq6gnjqbup
  agent_id: 2486052901
  card_template_id: 75fddb84-5d25-4dae-9a9c-34f8ddabf8c5.schema
