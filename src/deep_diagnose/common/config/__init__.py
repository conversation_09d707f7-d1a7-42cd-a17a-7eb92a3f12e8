
"""
Configuration Management System

简洁的配置管理系统，只暴露必要的对外接口：

核心接口：
- get_config() - 获取应用配置
常用配置常量：
- 搜索引擎、问题列表、Agent映射等
"""

import os
from typing import Dict, Any, Optional

from .core.base_config import DotDict

# 全局配置缓存
_config_cache: Optional[Dict[str, Any]] = None


def get_config() -> DotDict:
    """获取应用配置"""
    global _config_cache
    if _config_cache is None:
        _config_cache = _load_config()
    return DotDict(_config_cache)


def get_secret(secret: str, pub_name: str = "ecs-deep-diagnose_aone_key") -> str:
    """获取解密后的密钥"""
    from .core.security_config import security_config
    return security_config.decrypt_secret(secret, pub_name)

def _load_config() -> Dict[str, Any]:
    """内部函数：加载配置"""
    # 延迟导入避免循环依赖
    from dotenv import load_dotenv
    from .core.environment import get_environment, is_production_environment
    from .loaders.yaml_loader import YamlConfigLoader
    from .loaders.env_loader import EnvConfigLoader

    # 确保环境变量已加载
    load_dotenv()

    # 获取当前环境
    env = get_environment()

    # 初始化加载器，YamlConfigLoader会根据环境自动选择配置文件
    yaml_loader = YamlConfigLoader()
    env_loader = EnvConfigLoader()

    # 从YAML加载基础配置（会根据环境自动选择config_daily.yaml或config_prod.yaml）
    config = yaml_loader.load()

    # 环境变量覆盖
    env_config = env_loader.load()
    config.update(env_config)

    # 添加MCP设置 - 延迟加载避免循环依赖
    # MCP设置将在首次使用时动态生成

    # 添加环境信息
    config.update({
        'environment': env.value,
        'is_production': is_production_environment(env),
        'app_name': 'ecs-deep-diagnose',
        'app_env': env.value
    })

    return config


