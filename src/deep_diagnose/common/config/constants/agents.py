"""
Agent configuration constants.

This module contains agent-related configurations, including
LLM type definitions and agent-to-LLM mappings.
"""

from typing import Literal

# Define available LLM types
LLMType = Literal["basic", "reasoning", "vision", "code", "code_flash","glm"]

# Define agent-LLM mapping
AGENT_LLM_MAP: dict[str, LLMType] = {
    "coordinator": "reasoning",
    "planner": "reasoning",
    "replanner": "reasoning",
    "researcher": "reasoning",
    "coder": "code",
    "reporter": "reasoning",
    "html_generator": "code",
    "html_merger": "code_flash",
    "html_validator": "reasoning",
    "podcast_script_writer": "basic",
    "ppt_composer": "basic",
    "prose_writer": "basic",
}

SILENT_LLM_CALL_TAG = "silent_llm_call"
REWRITE_LLM_CALL_TAG = "rewrite_llm_call"

__all__ = [
    'LLMType',
    'AGENT_LLM_MAP',
    'SILENT_LLM_CALL_TAG',
    'REWRITE_LLM_CALL_TAG'
]
