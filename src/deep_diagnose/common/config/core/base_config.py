"""
Base configuration class with common functionality.
"""

import os
from typing import Any, Dict, Optional
from abc import ABC, abstractmethod


class DotDict(dict):
    """Dictionary that supports dot notation access."""
    
    def __init__(self, *args, **kwargs):
        super(DotDict, self).__init__(*args, **kwargs)

    def __getattr__(self, key):
        try:
            value = self[key]
            if isinstance(value, dict):
                value = DotDict(value)
            return value
        except KeyError:
            raise AttributeError(f"'{self.__class__.__name__}' object has no attribute '{key}'")


class BaseConfig(ABC):
    """
    Base configuration class providing common functionality.
    """
    
    def __init__(self):
        self._config_data: Optional[Dict[str, Any]] = None
        self._config_path: Optional[str] = None
    
    @abstractmethod
    def load(self) -> Dict[str, Any]:
        """
        Load configuration data.
        
        Returns:
            Dict[str, Any]: Configuration data
        """
        pass
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        Get configuration value by key.
        
        Args:
            key: Configuration key (supports dot notation)
            default: Default value if key not found
            
        Returns:
            Any: Configuration value
        """
        if self._config_data is None:
            self._config_data = self.load()
        
        # Support dot notation
        keys = key.split('.')
        value = self._config_data
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_all(self) -> Dict[str, Any]:
        """
        Get all configuration data.
        
        Returns:
            Dict[str, Any]: All configuration data
        """
        if self._config_data is None:
            self._config_data = self.load()
        
        return self._config_data
    
    def to_dot_dict(self) -> DotDict:
        """
        Convert configuration to DotDict for dot notation access.
        
        Returns:
            DotDict: Configuration as DotDict
        """
        return DotDict(self.get_all())
    
    def reload(self) -> None:
        """Reload configuration data."""
        self._config_data = None
