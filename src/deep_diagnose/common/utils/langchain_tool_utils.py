from typing import Dict, Any, cast

from langchain_core.tools import StructuredTool, BaseTool
from langchain_mcp_adapters.sessions import create_session
from langchain_mcp_adapters.tools import _convert_call_tool_result, NonTextContent
from mcp import ClientSession


def structured_tool_2_dict(
        tool: BaseTool
) -> Dict:
    return {
        "name": tool.name,
        "description": tool.description,
        "args_schema": tool.args_schema
    }


def dict_2_structured_tool(
        tool_info: Dict,
        mcp_server: Dict
) -> StructuredTool:
    async def call_tool(
            **arguments: dict[str, Any],
    ) -> tuple[str | list[str], list[NonTextContent] | None]:
        async with create_session(mcp_server) as tool_session:
            await tool_session.initialize()
            call_tool_result = await cast(ClientSession, tool_session).call_tool(
                tool_info.get("name"), arguments
            )

        return _convert_call_tool_result(call_tool_result)
    return StructuredTool(
        name=tool_info.get("name"),
        description=tool_info.get("description"),
        args_schema=tool_info.get("args_schema"),
        coroutine=call_tool,
        response_format="content_and_artifact"
    )
