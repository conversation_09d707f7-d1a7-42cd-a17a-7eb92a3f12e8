from __future__ import annotations

import asyncio
import logging
from dataclasses import dataclass
from typing import Any, Callable, Collection, Dict, Optional

SCHEDULING_INTERVAL = 0.01  # 调度间隔，单位为秒

logger = logging.getLogger(__name__)


@dataclass
class Task:
    idx: int
    name: str
    func: Callable
    args: Collection[Any]
    dependencies: Collection[int]
    is_join: bool = False

    async def __call__(self) -> Any:
        return await self.func(*self.args)


class TaskScheduler:
    """
    任务调度器，用于管理和调度异步任务执行。
    
    Attributes:
        tasks: 存储所有任务的字典，键为任务索引，值为Task对象
        tasks_done: 存储任务完成状态的字典，键为任务索引，值为asyncio.Event对象
        remaining_tasks: 存储尚未执行的任务索引集合
    """
    tasks: Dict[int, Task]
    tasks_done: Dict[int, asyncio.Event]
    remaining_tasks: set[int]

    def __init__(self):
        """初始化任务调度器"""
        self.tasks = {}
        self.tasks_done = {}
        self.remaining_tasks = set()

    def _set_tasks(self, tasks: dict[int, Any]):
        """
        设置任务到调度器中
        
        Args:
            tasks: 任务字典，键为任务索引，值为任务对象
        """
        self.tasks.update(tasks)
        self.tasks_done.update({task_idx: asyncio.Event() for task_idx in tasks})
        self.remaining_tasks.update(set(tasks.keys()))

    def _all_tasks_done(self):
        """
        检查所有任务是否都已完成
        
        Returns:
            bool: 如果所有任务都已完成返回True，否则返回False
        """
        return all(self.tasks_done[d].is_set() for d in self.tasks_done)

    def _get_all_executable_tasks(self):
        """
        获取所有可执行的任务（依赖已满足的任务）
        
        Returns:
            list: 可执行任务索引的列表
        """
        return [
            task_id
            for task_id in self.remaining_tasks
            if all(
                self.tasks_done[d].is_set() for d in self.tasks[task_id].dependencies
            )
        ]

    async def _run_task(self, task: Task):
        """
        执行单个任务
        
        Args:
            task: 要执行的任务对象
        """
        if not task.is_join:
            await task()
        self.tasks_done[task.idx].set()

    async def aschedule(self, task_queue: asyncio.Queue[Optional[Task]], max_schedule_time: float = 300.0):
        """
        异步调度任务队列中的任务，支持动态添加和依赖管理。
        
        该方法会持续监听任务队列，当有新任务加入时会将其纳入调度管理，
        并根据任务依赖关系自动调度可执行的任务并发执行。
        当队列中加入None哨兵值时表示不再有新任务加入，
        此时会等待所有已添加的任务执行完成后退出。
        
        Args:
            task_queue: 任务队列，用于接收待调度的任务。当加入None时表示任务添加结束。
            max_schedule_time: 最长调度时间，单位为秒，默认值为300秒。
        
        Returns:
            None
        """
        # 设置最大调度次数，防止无限循环
        max_iterations = int(max_schedule_time / SCHEDULING_INTERVAL)
        no_more_tasks = False

        for _ in range(max_iterations):
            # 如果还有任务需要处理，从队列中获取任务
            if not no_more_tasks:
                task = await task_queue.get()

                if task is None:
                    # 收到了结束任务，说明队列中没有更多任务
                    no_more_tasks = True
                else:
                    # 将新任务添加到调度器中
                    self._set_tasks({task.idx: task})

            # 获取当前所有可执行的任务
            executable_tasks = self._get_all_executable_tasks()

            if executable_tasks:
                # 并行执行所有可执行任务
                for task_id in executable_tasks:
                    asyncio.create_task(self._run_task(self.tasks[task_id]))
                    self.remaining_tasks.remove(task_id)
            elif no_more_tasks and self._all_tasks_done():
                # 所有任务都已完成，退出调度循环
                logger.info(f"Task scheduling finished with {len(self.tasks_done)} / {len(self.tasks)} tasks.")
                return
            else:
                # 暂时没有可执行任务，等待一段时间后重新检查
                await asyncio.sleep(SCHEDULING_INTERVAL)
        logger.error(f"Task scheduler exceeds max iterations {max_iterations}")
