import re
from enum import StrEnum
from typing import Optional


class MachineIdType(StrEnum):
    """Machine ID type enumeration."""

    INSTANCE_ID = "instance_id"
    NC_IP = "nc_ip"
    UID = "uid"
    NC_ID = "nc_id"
    UNKNOWN = "unknown"
        
    def __repr__(self):
        """Return the representation of the enum value."""
        return f"MachineIdType.{self.name}"

class MachineIdUtil:
    """Machine ID utility class for identifying machine ID types (Instance ID or NC IP)."""

    # ECS instance ID regex (from DiagnoseServiceImpl)
    ECS_INSTANCE_REGEX = r"^(i-(?!auto)|hbm-|AY|eci-|cp-|ic-|ay|acs-)[a-z0-9-]+$"
    # IPv4 regex (from existing code)
    IPV4_REGEX = r"^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$"
    # UID regex (16 digits)
    UID_REGEX = r"\d{16}"
    # NC ID regex (digits-digits pattern)
    NC_ID_REGEX = r"\d+-\d+"

    @classmethod
    def get_machine_id_type(cls, machine_id: Optional[str]) -> MachineIdType:
        """
        Check if machineId is an instance ID, NC IP, UID, or NC ID

        Args:
            machine_id: machine ID

        Returns:
            MachineIdType: enum type
        """
        if not machine_id or not machine_id.strip():
            return MachineIdType.UNKNOWN

        # Check if machineId is a UID (16 digits)
        if re.match(cls.UID_REGEX, machine_id):
            return MachineIdType.UID

        # Check if machineId is an NC ID (digits-digits pattern)
        if re.match(cls.NC_ID_REGEX, machine_id):
            return MachineIdType.NC_ID

        # Check if machineId is an ECS instance ID
        if re.match(cls.ECS_INSTANCE_REGEX, machine_id):
            return MachineIdType.INSTANCE_ID

        # Check if machineId is an IP address
        if re.match(cls.IPV4_REGEX, machine_id):
            return MachineIdType.NC_IP

        return MachineIdType.UNKNOWN

    @classmethod
    def is_instance_id(cls, machine_id: Optional[str]) -> bool:
        """
        Check if machineId is an ECS instance ID

        Args:
            machine_id: machine ID

        Returns:
            bool: True if machineId is an ECS instance ID, False otherwise
        """
        return cls.get_machine_id_type(machine_id) == MachineIdType.INSTANCE_ID

    @classmethod
    def is_nc_ip(cls, machine_id: Optional[str]) -> bool:
        """
        Check if machineId is an NC IP address

        Args:
            machine_id: machine ID

        Returns:
            bool: True if machineId is an NC IP address, False otherwise
        """
        return cls.get_machine_id_type(machine_id) == MachineIdType.NC_IP

    @classmethod
    def is_uid(cls, machine_id: Optional[str]) -> bool:
        """
        Check if machineId is a UID (16 digits)

        Args:
            machine_id: machine ID

        Returns:
            bool: True if machineId is a UID (16 digits), False otherwise
        """
        if not machine_id:
            return False
        return bool(re.match(cls.UID_REGEX, machine_id))

    @classmethod
    def is_nc_id(cls, machine_id: Optional[str]) -> bool:
        """
        Check if machineId is an NC ID (digits-digits pattern)

        Args:
            machine_id: machine ID

        Returns:
            bool: True if machineId is an NC ID (digits-digits pattern), False otherwise
        """
        if not machine_id:
            return False
        return bool(re.match(cls.NC_ID_REGEX, machine_id))
