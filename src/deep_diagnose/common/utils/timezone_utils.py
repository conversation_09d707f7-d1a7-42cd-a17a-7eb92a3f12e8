"""
时区处理工具模块
"""
from datetime import datetime, timezone
import pytz
from typing import Optional

# 定义上海时区
SHANGHAI_TZ = pytz.timezone('Asia/Shanghai')
UTC_TZ = pytz.UTC


def get_shanghai_now() -> datetime:
    """获取当前上海时区的时间"""
    return datetime.now(SHANGHAI_TZ)


def get_utc_now() -> datetime:
    """获取当前UTC时间"""
    return datetime.now(UTC_TZ)


def to_shanghai_time(dt: datetime) -> datetime:
    """将datetime转换为上海时区时间"""
    if dt.tzinfo is None:
        # 如果没有时区信息，假设是UTC时间
        dt = UTC_TZ.localize(dt)
    return dt.astimezone(SHANGHAI_TZ)


def to_utc_time(dt: datetime) -> datetime:
    """将datetime转换为UTC时间"""
    if dt.tzinfo is None:
        # 如果没有时区信息，假设是上海时区时间
        dt = SHANGHAI_TZ.localize(dt)
    return dt.astimezone(UTC_TZ)


def format_shanghai_time(dt: Optional[datetime] = None, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """格式化上海时区时间为字符串"""
    if dt is None:
        dt = get_shanghai_now()
    elif dt.tzinfo is None:
        dt = SHANGHAI_TZ.localize(dt)
    else:
        dt = dt.astimezone(SHANGHAI_TZ)
    
    return dt.strftime(format_str)


def parse_time_with_timezone(time_str: str, timezone_name: str = 'Asia/Shanghai') -> datetime:
    """解析时间字符串并添加时区信息"""
    tz = pytz.timezone(timezone_name)
    # 假设输入的时间字符串格式为 "YYYY-MM-DD HH:MM:SS"
    dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
    return tz.localize(dt)