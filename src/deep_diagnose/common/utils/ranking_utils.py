import math
import re
from collections import Counter, defaultdict
from typing import Dict, <PERSON>, <PERSON><PERSON>


def tokenize(text: str) -> List[str]:
    return [t for t in re.split(r"[^\w\u4e00-\u9fff]+", (text or "").lower()) if t]


def bm25_scores(query: str, docs: List[str], k1: float = 1.5, b: float = 0.75) -> List[float]:
    tokenized_docs = [tokenize(d) for d in docs]
    tokenized_query = tokenize(query)
    N = len(tokenized_docs) or 1
    doc_freq: defaultdict[str, int] = defaultdict(int)
    doc_len = [len(d) for d in tokenized_docs]
    avgdl = (sum(doc_len) / N) if N > 0 else 0.0
    for d in tokenized_docs:
        seen_terms = set(d)
        for t in seen_terms:
            doc_freq[t] += 1
    idf = {t: math.log((N - doc_freq.get(t, 0) + 0.5) / (doc_freq.get(t, 0) + 0.5) + 1.0) for t in set(tokenized_query)}
    scores: List[float] = []
    for d_idx, d in enumerate(tokenized_docs):
        tf = Counter(d)
        score = 0.0
        for t in tokenized_query:
            if t not in tf:
                continue
            numerator = tf[t] * (k1 + 1)
            denominator = tf[t] + k1 * (1 - b + b * (doc_len[d_idx] / (avgdl or 1.0)))
            score += idf.get(t, 0.0) * (numerator / (denominator or 1.0))
        scores.append(score)
    return scores


def tfidf_cosine_scores(query: str, docs: List[str]) -> List[float]:
    tokenized_docs = [tokenize(d) for d in docs]
    tokenized_query = tokenize(query)
    N = len(tokenized_docs) or 1
    # DF
    df: defaultdict[str, int] = defaultdict(int)
    for d in tokenized_docs:
        for t in set(d):
            df[t] += 1
    # IDF
    vocab = set()
    for d in tokenized_docs:
        vocab.update(d)
    idf = {t: math.log((N + 1) / (df.get(t, 0) + 1)) + 1.0 for t in vocab}

    def vec(tokens: List[str]) -> Dict[str, float]:
        tf = Counter(tokens)
        return {t: (tf[t] * idf.get(t, 1.0)) for t in tf}

    qv = vec(tokenized_query)
    scores: List[float] = []
    q_norm = math.sqrt(sum(v * v for v in qv.values())) or 1.0
    for d in tokenized_docs:
        dv = vec(d)
        dot = sum(qv.get(t, 0.0) * dv.get(t, 0.0) for t in set(qv.keys()) | set(dv.keys()))
        d_norm = math.sqrt(sum(v * v for v in dv.values())) or 1.0
        scores.append(dot / (q_norm * d_norm))
    return scores


def normalize_scores(scores: List[float]) -> List[float]:
    if not scores:
        return []
    s_min, s_max = min(scores), max(scores)
    rng = (s_max - s_min) if (s_max - s_min) > 1e-9 else 1.0
    return [max(0.0, min(1.0, (s - s_min) / rng)) for s in scores]


def rank_and_dedup(
    candidates: List[str],
    query: str,
    method: str,
    top_k: int,
    scores: List[float] | None = None,
    dup_threshold: float = 0.9,
) -> List[Tuple[str, float]]:
    if not candidates:
        return []
    method = (method or "bm25").lower()
    if scores is None:
        if method == "embedding":
            scores = tfidf_cosine_scores(query, candidates)
        elif method == "hybrid":
            s1 = bm25_scores(query, candidates)
            s2 = tfidf_cosine_scores(query, candidates)
            scores = [0.6 * a + 0.4 * b for a, b in zip(s1, s2)]
        else:
            scores = bm25_scores(query, candidates)

    indexed = list(enumerate(candidates))
    ranked = sorted(indexed, key=lambda x: scores[x[0]], reverse=True)
    kept: List[Tuple[str, float]] = []
    norm_scores = normalize_scores(scores)
    for idx, cand in ranked:
        norm = cand.strip()
        if not norm:
            continue
        is_dup = False
        for k, _ in kept:
            sim = tfidf_cosine_scores(k, [norm])[0]
            if sim >= dup_threshold:
                is_dup = True
                break
        if not is_dup:
            kept.append((norm, norm_scores[idx]))
        if len(kept) >= top_k:
            break
    return kept
