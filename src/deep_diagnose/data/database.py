"""
数据库连接和配置 - Tortoise ORM
"""

import asyncio
from tortoise import Tortoise
from tortoise.contrib.fastapi import register_tortoise
import logging
from typing import Optional

from deep_diagnose.common.config import get_config

logger = logging.getLogger(__name__)

# Aerich配置 - 连接URL会在运行时动态设置
TORTOISE_ORM = {
    "connections": {"default": ""},  # 运行时动态设置
    "apps": {
        "models": {
            "models": ["deep_diagnose.domain.user.models", "deep_diagnose.domain.chat.models", "aerich.models"],
            "default_connection": "default",
        },
    },
}


class DatabaseManager:
    """数据库管理器 - Tortoise ORM"""

    def __init__(self):
        self.database_url: Optional[str] = None
        self.is_initialized = False
        self._setup_database_url()

    def _setup_database_url(self):
        """设置数据库连接URL"""
        try:
            config = get_config()
            rds_config = config.infrastructure.rds

            # 构建数据库连接URL for aiomysql
            self.database_url = f"mysql://{rds_config.user}:{rds_config.pwd}@" f"{rds_config.host}:{rds_config.port}/{rds_config.db}" f"?charset=utf8mb4"

            # 更新全局配置
            TORTOISE_ORM["connections"]["default"] = self.database_url
            TORTOISE_ORM["apps"]["models"]["models"] = ["deep_diagnose.domain.user.models", "deep_diagnose.domain.chat.models", "aerich.models"]

            logger.info("Database URL configured successfully")

        except Exception as e:
            logger.error(f"Failed to setup database URL: {e}")
            raise

    async def init_database(self, generate_schemas: bool = True):
        """初始化数据库连接"""
        try:
            # 如果已经初始化过，先关闭现有连接
            if self.is_initialized:
                await self.close_database()
                
            await Tortoise.init(
                db_url=self.database_url, 
                modules={"models": ["deep_diagnose.domain.user.models", "deep_diagnose.domain.chat.models"]}
            )

            # 设置数据库时区为上海时区
            from tortoise import connections

            conn = connections.get("default")
            if conn:
                await conn.execute_query("SET time_zone = '+08:00'")
                logger.info("Database timezone set to Asia/Shanghai (+08:00)")

            # 只有在明确要求时才生成数据库表
            if generate_schemas:
                await Tortoise.generate_schemas()
                logger.info("Database schemas generated")

            self.is_initialized = True
            logger.info("Database initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            self.is_initialized = False
            raise

    async def close_database(self):
        """关闭数据库连接"""
        try:
            await Tortoise.close_connections()
            self.is_initialized = False
            logger.info("Database connections closed")
        except Exception as e:
            logger.error(f"Failed to close database: {e}")
            raise

    def register_with_fastapi(self, app):
        """注册到FastAPI应用"""
        register_tortoise(
            app,
            db_url=self.database_url,
            modules={"models": ["deep_diagnose.domain.user.models", "deep_diagnose.domain.chat.models"]},
            generate_schemas=True,
            add_exception_handlers=True,
        )
        logger.info("Database registered with FastAPI")


# 全局数据库管理器实例
db_manager = DatabaseManager()


# 便捷函数
async def init_db():
    """初始化数据库的便捷函数"""
    await db_manager.init_database()


async def close_db():
    """关闭数据库的便捷函数"""
    await db_manager.close_database()


if __name__ == "__main__":
    asyncio.run(init_db())
