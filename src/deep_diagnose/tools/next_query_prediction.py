import asyncio
import json
import logging
import math
import re
from collections import Counter, defaultdict
from typing import Annotated, Dict, List, Any, Optional, Tuple

from langchain_core.tools import tool

from deep_diagnose.common.utils.machine_utils import MachineIdType, MachineIdUtil
from deep_diagnose.tools.decorators import log_io
from deep_diagnose.common.utils.ranking_utils import (
    bm25_scores,
    tfidf_cosine_scores,
    rank_and_dedup,
)

try:
    # 可选依赖：如果环境未配置百炼，也能退化到启发式
    from deep_diagnose.llms.app_bailian import app_bailian, BailianAppEnum
    from deep_diagnose.llms.bailian_application import get_recommendation_generator_llm
except Exception:  # noqa: BLE001
    app_bailian = None  # type: ignore[assignment]
    BailianAppEnum = None  # type: ignore[assignment]
    get_recommendation_generator_llm = None  # type: ignore[assignment]


logger = logging.getLogger(__name__)


def _normalize_messages(messages: List[Dict[str, Any]] | None) -> List[Dict[str, Any]]:
    if not messages:
        return []
    normalized: List[Dict[str, Any]] = []
    for m in messages:
        role = str(m.get("role", "")).strip() or "user"
        content = str(m.get("content", "")).strip()
        normalized.append({"role": role, "content": content})
    return normalized


def _build_context_text(messages: List[Dict[str, Any]], features: Dict[str, Any], history_limit: int = 12) -> str:
    # 将消息与特征信息整理为提示文本
    lines: List[str] = []
    if features:
        try:
            features_text = json.dumps(features, ensure_ascii=False)
        except Exception:  # noqa: BLE001
            features_text = str(features)
        lines.append(f"[Features]\n{features_text}\n")

    if messages:
        lines.append("[Conversation]")
        for m in messages[-history_limit:]:  # 限制长度
            lines.append(f"- {m.get('role','user')}: {m.get('content','')}")
        lines.append("")

    return "\n".join(lines)


# 模板配置：根据场景、机型补充候选追问
# 注意：为兼容 scene 下的两种结构（list 或 dict），这里不做强类型声明
TEMPLATES = {
    # scene 下支持两种写法：
    # 1) 直接列表 -> 不区分机器类型
    # 2) 字典 -> 支持 default + 按 machine_id_type 精细化覆盖
    "scene": {
        "down": {
            "default": ["是否记录到异常事件或告警"],
            MachineIdType.INSTANCE_ID.value: ["实例为什么会发生宕机？", "实例收到事件的原因是什么，应该如何处理？"],
            # 这两个 Key 的结果一致，合并为一个列表，包含两个 key
            **{k: ["宿主机为什么会发生宕机？"] for k in [MachineIdType.NC_IP.value, MachineIdType.NC_ID.value]},
        },
    },
    # 通用追问模板，按机器ID类型细化
    "general": {
        "default": [
            "过去一天内有没有变更、发布或运维操作？",
        ],
        MachineIdType.INSTANCE_ID.value: [
            "展示一下实例的详细信息",
            "提交热迁移运维",
            "提交冷迁移运维",
        ],
        **{
            k: [
                "展示一下宿主机的详细信息",
                "宿主机提交下线运维",
                "宿主机提交锁定运维",
            ]
            for k in [MachineIdType.NC_IP.value, MachineIdType.NC_ID.value]
        },
    },
    "anomaly": {
        "event": [
            "实例收到了一个客户侧事件，推迟它的计划执行时间。",
        ],
        "performance_contention": [
            "实例发生了性能争抢，性能争抢的原因是什么？",
        ],
        "migration": [
            "实例发生迁移的原因是什么？",
        ],
    },
}

# 关键词到异常类别的映射（正则片段 -> 模板类别）
ANOMALY_KEYWORDS: List[Tuple[str, str]] = [
    (r"迁移|migrate|live migration|冷迁移", "migration"),
    (r"性能争抢", "performance_contention"),
    (r"事件", "event"),
]


def _template_candidates(features: Dict[str, Any]) -> List[str]:
    candidates: List[str] = []
    scene = str(features.get("scene", "")).strip().lower()
    machine_id_type = str(features.get("machine_id_type", "")).strip().lower()
    if scene and scene in TEMPLATES.get("scene", {}):
        scene_tpl = TEMPLATES["scene"][scene]
        if isinstance(scene_tpl, list):
            candidates.extend(scene_tpl)
        elif isinstance(scene_tpl, dict):
            # default 先加入
            default_list = scene_tpl.get("default", [])
            if isinstance(default_list, list):
                candidates.extend(default_list)
            # 根据机器类型细分
            if machine_id_type and machine_id_type in scene_tpl:
                spec_list = scene_tpl.get(machine_id_type, [])
                if isinstance(spec_list, list):
                    candidates.extend(spec_list)
    if machine_id_type and machine_id_type in TEMPLATES.get("machine_id_type", {}):
        candidates.extend(TEMPLATES["machine_id_type"][machine_id_type])
    return candidates


def _anomaly_candidates(messages: List[Dict[str, Any]]) -> List[str]:
    if not messages:
        return []
    text = "\n".join(str(m.get("content", "")) for m in messages[-20:]).lower()
    cats: List[str] = []
    for pattern, cat in ANOMALY_KEYWORDS:
        try:
            if re.search(pattern, text, flags=re.IGNORECASE):
                cats.append(cat)
        except re.error:
            continue
    cats = list(dict.fromkeys(cats))  # 去重且保序
    cands: List[str] = []
    for cat in cats:
        group = TEMPLATES.get("anomaly", {}).get(cat, [])
        cands.extend(group)
    return cands


def _heuristic_predict(messages: List[Dict[str, Any]], features: Dict[str, Any], top_k: int) -> List[Dict[str, Any]]:
    # 简单启发式：基于最近一条用户消息和通用诊断信息生成建议
    last_user = next((m for m in reversed(messages) if m.get("role") == "user" and m.get("content")), {})
    last_text = str(last_user.get("content", "")).strip()

    candidates: List[str] = []
    # 通用追问模板（按 machine_id_type 细化）
    general_tpl = TEMPLATES.get("general", {})
    default_general = general_tpl.get("default", []) if isinstance(general_tpl, dict) else []
    candidates.extend(default_general if isinstance(default_general, list) else [])
    if features:
        mit = str(features.get("machine_id_type", "")).strip().lower()
        if isinstance(general_tpl, dict) and mit in general_tpl:
            spec_general = general_tpl.get(mit, [])
            candidates.extend(spec_general if isinstance(spec_general, list) else [])

    # 结合领域特征
    if features:
        machine_id = features.get("machine_id")
        if machine_id and MachineIdUtil.is_instance_id(machine_id):
            candidates.append(f"是否对 {machine_id} 进行了变更或迁移操作？")
    # 模板：场景/机器ID类型
    candidates.extend(_template_candidates(features))
    # 模板：异常关键词
    candidates.extend(_anomaly_candidates(messages))

    # 去重并截断
    uniq: List[str] = []
    seen = set()
    for c in candidates:
        k = c.strip()
        if not k or k in seen:
            continue
        seen.add(k)
        uniq.append(k)

    return [{"question": q, "rationale": "heuristic"} for q in uniq]


async def _llm_predict(context_text: str, top_k: int) -> List[Dict[str, Any]]:
    if get_recommendation_generator_llm is None:
        return []
    prompt = (
        "请基于以下上下文预测用户的后续追问（给出3-6条，精炼、可执行）：\n\n" f"{context_text}\n\n" '输出要求：每行一个问题，以"- "开头。不要添加多余解释。'
    )
    try:
        # 使用LangChain包装器，让LangChain自动处理回调
        llm = get_recommendation_generator_llm()
        result = await llm.ainvoke(prompt)
        # 处理返回结果，可能是AIMessage对象或字符串
        if hasattr(result, 'content'):
            text = result.content
        else:
            text = str(result)
        
        if not text:
            return []
        lines = [l.strip() for l in text.split("\n") if l.strip()]
        preds: List[Dict[str, Any]] = []
        for l in lines:
            if l.startswith("-"):
                q = l[1:].strip()
                if q:
                    preds.append({"question": q, "rationale": "llm"})
        if not preds:
            return []
        return preds[: max(1, min(top_k, 10))]
    except Exception as e:  # noqa: BLE001
        logger.error(f"next_query_prediction llm error: {e}", exc_info=True)
        return []


async def _generic_llm_predict(context_text: str, top_k: int) -> List[Dict[str, Any]]:
    """通用LLM路径：不依赖百炼Agent，走项目内通用模型。"""
    try:
        from langchain_core.messages import HumanMessage
        from deep_diagnose.llms.llm import get_llm_by_type
    except Exception as e:  # noqa: BLE001
        logger.warning(f"generic llm not available: {e}")
        return []

    prompt = (
        "你是助理。基于用户上下文，给出3-6条下一步追问建议，追问问题通常只关注云VM的使用，而不关注具体的底层细节，所以推荐问题注意重点侧重。推荐问题字数约束在15字以内。每行以'- '开头，不要解释：\n\n"
        f"{context_text}\n\n"
        "仅输出问题列表。"
    )

    def _extract_text_from_content(content: Any) -> str:
        if isinstance(content, str):
            return content
        if isinstance(content, list):
            parts: List[str] = []
            for item in content:
                if isinstance(item, dict) and "text" in item:
                    parts.append(str(item.get("text", "")))
                else:
                    parts.append(str(item))
            return "".join(parts)
        if isinstance(content, dict):
            return str(content.get("text") or content.get("content") or "")
        return str(content or "")

    try:
        llm = get_llm_by_type("code")
        res = await llm.ainvoke([HumanMessage(content=prompt)])
        text = _extract_text_from_content(getattr(res, "content", "")).strip()
        if not text:
            return []
        lines = [l.strip() for l in text.split("\n") if l.strip()]
        preds: List[Dict[str, Any]] = []
        for l in lines:
            if l.startswith("-"):
                q = l[1:].strip()
                if q:
                    preds.append({"question": q, "rationale": "generic_llm"})
        return preds
    except Exception as e:  # noqa: BLE001
        logger.error(f"generic llm predict error: {e}", exc_info=True)
        return []


def _tokenize(text: str) -> List[str]:
    return [t for t in re.split(r"[^\w\u4e00-\u9fff]+", (text or "").lower()) if t]


def _bm25_scores(query: str, docs: List[str]) -> List[float]:
    return bm25_scores(query, docs)


def _tfidf_cosine_scores(query: str, docs: List[str]) -> List[float]:
    return tfidf_cosine_scores(query, docs)


def _rank_and_dedup(
    candidates: List[str], query: str, method: str, top_k: int, scores: Optional[List[float]] = None, dup_threshold: float = 0.9
) -> List[Tuple[str, float]]:
    return rank_and_dedup(candidates, query, method, top_k, scores=scores, dup_threshold=dup_threshold)


@tool
@log_io
async def next_query_prediction_tool(
    messages: Annotated[
        List[Dict[str, Any]],
        "消息上下文，元素如 {role: user|assistant, content: str}",
    ],
    features: Annotated[
        Optional[Dict[str, Any]],
        "其他特征信息（可选）：如业务场景、机器ID、时间范围等",
    ] = None,
    top_k: Annotated[int, "返回条数上限（1-10）"] = 5,
    strategy: Annotated[
        str,
        "预测策略：heuristic（默认）、llm、hybrid",
    ] = "heuristic",
    llm_mode: Annotated[
        str,
        "llm 模式：agent（百炼，默认）或 generic（通用LLM）",
    ] = "agent",
    ranker: Annotated[
        str,
        "打分去重：bm25（默认）| embedding | hybrid",
    ] = "bm25",
    hybrid_weight: Annotated[float, "embedding 与 BM25 融合权重（0-1），仅 hybrid 模式生效"] = 0.6,
    dup_threshold: Annotated[float, "去重阈值（0-1），越高越严格"] = 0.9,
):
    """
    Next Query Prediction 工具：基于对话上下文与特征信息，推荐后续追问问题。

    返回：JSON字符串，形如：
    {
      "predictions": [
        {"question": "...", "confidence": 0.7, "rationale": "llm|heuristic"},
        ...
      ]
    }
    """
    # 规范化输入
    safe_top_k = max(1, min(int(top_k or 5), 10))
    msgs = _normalize_messages(messages)
    feats = features or {}

    context_text = _build_context_text(msgs, feats)

    # 选择策略
    predictions: List[Dict[str, Any]] = []
    if strategy in ("heuristic", "hybrid"):  # 基线
        predictions = _heuristic_predict(msgs, feats, safe_top_k)

    if strategy in ("llm", "hybrid"):
        # 追加或替换为 LLM 结果（根据 llm_mode 决定 agent 或通用LLM）
        try:
            if llm_mode == "generic":
                llm_preds = await _generic_llm_predict(context_text, safe_top_k)
            else:
                llm_preds = await _llm_predict(context_text, safe_top_k)
        except Exception as e:  # noqa: BLE001
            logger.error(f"next_query_prediction llm scheduling error: {e}")
            llm_preds = []

        if strategy == "llm" and llm_preds:
            predictions = llm_preds
        elif strategy == "hybrid":
            # 合并去重，LLM优先
            merged: List[Dict[str, Any]] = []
            seen: set[str] = set()
            for item in llm_preds + predictions:
                q = item.get("question", "").strip()
                if q and q not in seen:
                    seen.add(q)
                    merged.append(item)
            predictions = merged

    # Ranking 去重输出
    query_text = next((m.get("content", "") for m in reversed(msgs) if m.get("role") == "user"), "")
    # 计算排序分数（支持 DashScope embedding）
    candidate_questions = [p.get("question", "") for p in predictions]
    precomputed_scores: Optional[List[float]] = None
    if ranker in ("embedding", "hybrid") and candidate_questions:
        try:
            # 使用项目中的 DashScopeEmbeddingService 计算相似度
            from deep_diagnose.core.reasoning.planning.strategy.embedding_service import (
                get_embedding_service,
                DashScopeEmbeddingService,
            )

            service = get_embedding_service()
            # 批量计算 embedding：第一个是 query，其余是候选
            embs = await service.get_embeddings_batch([query_text] + candidate_questions)
            if len(embs) == len(candidate_questions) + 1:
                q_emb = embs[0]
                c_embs = embs[1:]
                precomputed_scores = [DashScopeEmbeddingService.calculate_similarity(q_emb, e) for e in c_embs]

                if ranker == "hybrid":
                    _bm25 = _bm25_scores(query_text, candidate_questions)
                    w = max(0.0, min(1.0, float(hybrid_weight)))
                    precomputed_scores = [w * s + (1.0 - w) * b for s, b in zip(precomputed_scores, _bm25)]
        except Exception as e:  # noqa: BLE001
            logger.warning(f"embedding ranking fallback to TF-IDF/BM25 due to error: {e}")
            precomputed_scores = None

    ranked_questions = _rank_and_dedup(
        candidate_questions,
        query_text,
        ranker,
        safe_top_k,
        scores=precomputed_scores,
        dup_threshold=max(0.0, min(1.0, float(dup_threshold))),
    )
    final_predictions: List[Dict[str, Any]] = []
    for q, conf in ranked_questions:
        src = next((p.get("rationale") for p in predictions if p.get("question") == q), "heuristic")
        final_predictions.append({"question": q, "confidence": round(conf, 3), "rationale": src})
    predictions = final_predictions

    # 输出为 JSON 字符串
    try:
        return json.dumps({"predictions": predictions}, ensure_ascii=False)
    except Exception:  # noqa: BLE001
        # 兜底：返回简单字符串
        return str({"predictions": predictions})


if __name__ == "__main__":
    messages = [
        {"role": "user", "content": "检查一下目标机器的运行状态如何？"},
        {
            "role": "tool",
            "content": "**关键异常项** - 2025-08-26 09:47:18 - 2025-08-27 10:27:18 **虚拟机处于暂停状态需要虚拟化调查** **其他异常项** - 2025-08-26 20:06:23 - 2025-08-26 20:06:23 **下载iso或qboot kernel失败导致虚拟机启动失败** - 2025-08-26 09:20:00 - 2025-08-26 18:24:00 **VM性能指标趋势异常**: spike **低级别异常项** <details> <summary>共18项异常</summary> - `NC性能指标趋势异常` - `物理网络异常事件` - `云助手心跳长时间异常` - `云助手心跳异常` - `VM ArpPing超时疑似VM_OS内部故障` - `云助手心跳uptime掉0` - `TDC和存储后端节点出现异常通信` - `主动运维有执行中的工作流(补充)` - `主动运维有执行中的工作流` - `用户region内流量徒增` - `虚拟机libvirt状态变更` - `控制面操作虚拟机事件` - `虚拟机变成paused状态` - `物理网络异常事件` - `nc功耗抖变` - `虚拟机CPU利用率陡增` - `虚拟机内部关机或者重启` - `vm内部重启事件` </details>",
        },
        {"role": "assistant", "content": "虚拟机处于**暂停状态**，且存在**启动失败**和**性能异常波动**问题，需排查虚拟化层状态及启动流程。"},
    ]
    result = asyncio.run(
        next_query_prediction_tool.ainvoke(
            {
                "messages": messages,
                # 可选项示例：
                "features": {"scene": "down", "machine_id_type": MachineIdType.INSTANCE_ID.value, "machine_id": "i-8vbeub3v7iwcofgqsvts"},
                "top_k": 10,
                "strategy": "heuristic",
                "llm_mode": "generic",
                "ranker": "hybrid",
                "hybrid_weight": 0.6,
                "dup_threshold": 0.9,
            }
        )
    )
    print(result)
