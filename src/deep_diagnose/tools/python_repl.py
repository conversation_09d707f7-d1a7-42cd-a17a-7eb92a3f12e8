

import logging
import sys
import os
from typing import Annotated

from langchain_core.tools import tool
from langchain_experimental.utilities import PythonREPL

from .decorators import log_io

# Initialize logger
logger = logging.getLogger(__name__)

# Add the src directory to Python path
src_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
if src_path not in sys.path:
    sys.path.insert(0, src_path)

# Create REPL instance with current environment
repl = PythonREPL()

# 确保REPL使用当前Python环境，而不是隔离环境
# 将当前模块的全局命名空间复制到REPL中，这样所有已导入的模块都可用
def setup_repl_environment():
    """Setup REPL environment to use current Python environment"""
    # 将当前环境的关键模块和路径设置复制到REPL
    current_globals = globals().copy()
    
    # 更新REPL的全局命名空间，使其包含当前环境
    repl.globals.update(current_globals)
    
    # 确保sys.path包含src目录
    setup_code = f"""
import sys
src_path = r"{src_path}"
if src_path not in sys.path:
    sys.path.insert(0, src_path)
print("✅ REPL环境初始化成功")
"""
    
    try:
        result = repl.run(setup_code)
        logger.info("🔧 REPL environment setup completed")
        return True
    except Exception as e:
        logger.warning(f"🔧 REPL setup failed: {e}")
        return False


@tool
@log_io
def python_repl_tool(
    code: Annotated[
        str, "The python code to execute to do further analysis or calculation."
    ],
):
    """Use this to execute python code and do data analysis or calculation. If you want to see the output of a value,
    you should print it out with `print(...)`. This is visible to the user."""
    logger.info(f"🔧 python_repl_tool called with code type: {type(code)}")
    logger.info(f"🔧 python_repl_tool code preview: {str(code)[:200]}...")
    
    if not isinstance(code, str):
        error_msg = f"Invalid input: code must be a string, got {type(code)}"
        logger.error(error_msg)
        return f"Error executing code:\n```python\n{code}\n```\nError: {error_msg}"

    # Setup REPL environment before execution
    setup_repl_environment()
    
    # 为了解决用户代码中函数定义的作用域问题，我们需要确保代码在全局作用域中执行
    # 特别是当代码包含函数定义和异步调用时

    logger.info("🔧 Starting Python code execution")
    try:
        # 为了解决函数定义作用域问题，我们需要确保代码在全局作用域中执行
        # 特别是当代码包含函数定义和异步调用时，需要使用exec而不是直接run
        
        # 检查代码是否包含函数定义和异步调用
        has_function_def = 'def ' in code
        has_async_call = 'asyncio.run(' in code or 'await ' in code
        
        if has_function_def and has_async_call:
            logger.info("🔧 Detected function definitions with async calls, using global exec mode")
            # 使用exec在全局作用域中执行代码
            import io
            import sys
            from contextlib import redirect_stdout, redirect_stderr
            
            # 捕获输出
            stdout_buffer = io.StringIO()
            stderr_buffer = io.StringIO()
            
            try:
                with redirect_stdout(stdout_buffer), redirect_stderr(stderr_buffer):
                    # 在REPL的全局命名空间中执行代码
                    # 确保函数定义进入全局命名空间
                    exec(code, repl.globals)
                
                # 获取输出
                stdout_content = stdout_buffer.getvalue()
                stderr_content = stderr_buffer.getvalue()
                
                if stderr_content:
                    result = f"Error: {stderr_content}"
                else:
                    result = stdout_content
                    
            except Exception as e:
                result = f"Error: {str(e)}"
        else:
            # 对于简单代码，使用原来的方式
            result = repl.run(code)
        
        # Check if the result is an error message by looking for typical error patterns
        if isinstance(result, str) and ("Error" in result or "Exception" in result or "Traceback" in result):
            logger.error(result)
            return f"Error executing code:\n```python\n{code}\n```\nError: {result}"
        logger.info("Code execution successful")
    except BaseException as e:
        error_msg = repr(e)
        logger.error(error_msg)
        return f"Error executing code:\n```python\n{code}\n```\nError: {error_msg}"

    result_str = f"Successfully executed:\n```python\n{code}\n```\nStdout: {result}"
    return result_str
