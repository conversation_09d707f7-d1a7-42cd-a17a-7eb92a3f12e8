#!/usr/bin/env python3
"""
简化的文件资源管理器
主要功能：
1. 数据写入文件
2. 文件注册到file_resource.csv
"""
import csv
import json
import os
from datetime import datetime
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


def get_request_data_directory(request_id, base_output_dir="./vm_data_output"):
    """
    获取指定request_id的数据目录，如果不存在则创建
    
    Args:
        request_id: 请求ID
        base_output_dir: 基础输出目录，默认为"./vm_data_output"
    
    Returns:
        Path: 数据目录路径对象
    """
    data_dir = Path(base_output_dir) / "file_system" / request_id
    data_dir.mkdir(parents=True, exist_ok=True)
    return data_dir


def save_data_to_file(data, filename, output_dir="."):
    """保存数据到JSON文件"""
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    file_path = output_path / filename
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    return str(file_path)


def register_file_to_csv(file_name, file_purpose="", output_dir=".", request_id="", file_title=None):
    """注册文件到file_resource.csv"""
    # CSV文件路径（与数据文件在同一目录）
    csv_path = Path(output_dir) / "file_resource.csv"
    
    # 检查CSV文件是否存在，不存在则创建表头
    if not csv_path.exists():
        with open(csv_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['file_name', 'file_title', 'file_purpose', 'data_purpose', 'created_time'])
    
    # 添加文件记录
    created_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    final_file_title = file_title if file_title else file_name
    
    with open(csv_path, 'a', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerow([file_name, final_file_title, file_purpose, file_purpose, created_time])
    
    return True


def save_and_register_data(request_id, instance_id, data, file_title=None, file_purpose=None):
    """
    保存数据并注册到CSV - 通用版本
    
    Args:
        request_id: 请求ID
        instance_id: 实例ID
        data: 要保存的数据
        file_title: 文件标题，如果不提供则使用默认格式
        file_purpose: 文件用途描述，如果不提供则使用默认描述
    
    Returns:
        Dict: 保存结果
    """
    try:
        # 1. 获取包含request_id的目录结构
        data_dir = get_request_data_directory(request_id)
        
        # 2. 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"vm_performance_{instance_id}_{timestamp}.json"
        
        # 3. 保存数据到文件（保存到request_id目录下）
        file_path = save_data_to_file(data, filename, str(data_dir))
        
        # 3. 获取文件大小
        file_size_bytes = os.path.getsize(file_path)
        file_size_readable = f"{file_size_bytes / 1024:.1f}KB" if file_size_bytes < 1024*1024 else f"{file_size_bytes / (1024*1024):.1f}MB"
        
        # 4. 注册到CSV（CSV文件与数据文件在同一目录）
        # 使用提供的标题和用途，如果没有提供则使用默认值
        final_file_title = file_title if file_title else f"VM性能数据_{instance_id}_{timestamp}"
        final_file_purpose = file_purpose if file_purpose else "VM性能数据"
        
        register_success = register_file_to_csv(
            file_name=filename,
            file_purpose=final_file_purpose,
            output_dir=str(data_dir),
            request_id=request_id,
            file_title=final_file_title
        )
        
        # 5. 统计数据点
        metrics_count = len(data)
        data_points = 0
        for metric_data in data.values():
            if isinstance(metric_data, dict) and 'datapoints' in metric_data:
                data_points += len(metric_data['datapoints'])
            elif isinstance(metric_data, list):
                data_points += len(metric_data)
        
        return {
            "success": True,
            "json_file": file_path,
            "filename": filename,
            "file_title": final_file_title,
            "file_purpose": final_file_purpose,
            "file_size": file_size_readable,
            "file_size_bytes": file_size_bytes,
            "metrics_count": metrics_count,
            "data_points": data_points,
            "request_id": request_id,
            "resource_registered": register_success
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e),
            "request_id": request_id
        }


def read_performance_data_from_csv(request_id, base_output_dir="./vm_data_output"):
    """
    从file_resource.csv读取性能数据文件列表并加载数据
    
    Args:
        request_id: 请求ID
        base_output_dir: 基础输出目录
        
    Returns:
        dict: 合并后的性能数据，如果没有性能数据则返回None
    """
    try:
        # 获取数据目录
        data_dir = get_request_data_directory(request_id, base_output_dir)
        csv_path = data_dir / "file_resource.csv"
        
        if not csv_path.exists():
            logger.info(f"CSV文件不存在: {csv_path}")
            return None
        
        # 读取CSV文件，查找性能数据文件
        performance_files = []
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                file_purpose = row.get('file_purpose', '').lower()
                file_name = row.get('file_name', '')
                
                # 检查是否为性能数据文件
                if ('vm性能数据' in file_purpose or 
                    'performance' in file_purpose.lower() or
                    'vm_performance' in file_name.lower()):
                    performance_files.append({
                        'file_name': file_name,
                        'file_title': row.get('file_title', ''),
                        'file_purpose': row.get('file_purpose', ''),
                        'created_time': row.get('created_time', '')
                    })
        
        if not performance_files:
            logger.info(f"未找到性能数据文件，请求ID: {request_id}")
            return None
        
        logger.info(f"找到 {len(performance_files)} 个性能数据文件")
        
        # 加载并合并所有性能数据文件
        merged_data = {}
        for file_info in performance_files:
            file_path = data_dir / file_info['file_name']
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        # 合并数据，如果有重复的指标，保留最新的
                        merged_data.update(data)
                        logger.info(f"成功加载性能数据文件: {file_info['file_name']}")
                except Exception as e:
                    logger.error(f"加载性能数据文件失败 {file_info['file_name']}: {e}")
            else:
                logger.warning(f"性能数据文件不存在: {file_path}")
        
        if merged_data:
            logger.info(f"成功合并性能数据，包含 {len(merged_data)} 个指标")
            return merged_data
        else:
            logger.warning(f"没有成功加载任何性能数据，请求ID: {request_id}")
            return None
            
    except Exception as e:
        logger.error(f"读取性能数据失败，请求ID: {request_id}: {e}")
        return None


# 注意：generate_performance_section_html 函数已迁移到 PerformanceService
# 为了保持向后兼容性，这里提供一个过渡性的包装函数
def generate_performance_section_html(request_id, base_output_dir="./vm_data_output"):
    """
    生成性能数据的HTML section - 过渡性包装函数
    
    注意：此函数已被弃用，请使用 PerformanceService.generate_performance_section_html()
    
    Args:
        request_id: 请求ID
        base_output_dir: 基础输出目录
        
    Returns:
        str: 性能数据的HTML section，如果没有数据则返回None
    """
    import warnings
    warnings.warn(
        "generate_performance_section_html 已被弃用，请使用 PerformanceService.generate_performance_section_html()",
        DeprecationWarning,
        stacklevel=2
    )
    
    try:
        from deep_diagnose.core.reasoning.services import PerformanceService
        import asyncio
        
        # 创建性能服务实例
        performance_service = PerformanceService(base_output_dir)
        
        # 检查是否已有运行中的事件循环
        try:
            # 尝试获取当前事件循环
            current_loop = asyncio.get_running_loop()
            # 如果有运行中的循环，使用同步方法
            logger.info("检测到运行中的事件循环，使用同步方法")
            return performance_service._generate_html_section_sync(
                performance_service._read_performance_data_sync(request_id), 
                request_id
            ) if performance_service._read_performance_data_sync(request_id) else None
        except RuntimeError:
            # 没有运行中的循环，创建新的循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(
                    performance_service.generate_performance_section_html(request_id)
                )
                return result
            finally:
                loop.close()
                asyncio.set_event_loop(None)
            
    except Exception as e:
        logger.error(f"生成性能数据HTML section失败，请求ID: {request_id}: {e}")
        return None


if __name__ == "__main__":
    # 测试示例
    test_data = {
        "cpu_usage": {
            "datapoints": [
                {"timestamp": "2025-01-01 10:00:00", "value": 50.5},
                {"timestamp": "2025-01-01 10:01:00", "value": 52.3}
            ]
        }
    }
    
    result = save_and_register_data(
        request_id="test_123",
        instance_id="i-test",
        data=test_data
    )
    
    print(f"结果: {result}")