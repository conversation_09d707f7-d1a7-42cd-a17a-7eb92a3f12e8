#!/usr/bin/env python3
"""简洁的数据采集器"""

import asyncio
import logging
from typing import Optional, List, Dict, Any
from deep_diagnose.tools.utils.vm_performance import list_vm_performance_metrics
from deep_diagnose.tools.utils.file_resource_manager import save_and_register_data
logger = logging.getLogger(__name__)


def _generate_file_metadata(instance_id: str, 
                           metrics: Optional[List[str]], 
                           start_time: Optional[str], 
                           end_time: Optional[str],
                           data_purpose: str,
                           custom_title: Optional[str] = None) -> tuple:
    """
    生成文件标题和用途描述
    
    Args:
        instance_id: 实例ID
        metrics: 指标列表
        start_time: 开始时间
        end_time: 结束时间
        data_purpose: 数据用途
        custom_title: 自定义标题
    
    Returns:
        tuple: (file_title, file_purpose)
    """
    # 生成标题
    if custom_title:
        file_title = custom_title
    else:
        # 自动生成标题
        if metrics and len(metrics) == 1:
            # 单个指标
            metric_name = _get_metric_display_name(metrics[0])
            file_title = f"实例{instance_id} {metric_name}性能数据"
        elif metrics and len(metrics) > 1:
            # 多个指标
            file_title = f"实例{instance_id} 多指标性能数据({len(metrics)}个指标)"
        else:
            # 默认指标
            file_title = f"实例{instance_id} 综合性能数据"
        
        # 添加时间范围
        if start_time and end_time:
            file_title += f"[{start_time}~{end_time}]"
        elif start_time:
            file_title += f"[{start_time}起]"
        elif end_time:
            file_title += f"[至{end_time}]"
    
    # 生成用途描述
    purpose_mapping = {
        "time_series": "时间序列图表数据",
        "line_chart": "折线图数据", 
        "bar_chart": "柱状图数据",
        "pie_chart": "饼图数据",
        "table": "表格数据",
        "scatter_plot": "散点图数据",
        "heatmap": "热力图数据"
    }
    
    file_purpose = purpose_mapping.get(data_purpose, f"{data_purpose}图表数据")
    
    return file_title, file_purpose


def _get_metric_display_name(metric: str) -> str:
    """获取指标的显示名称"""
    metric_names = {
        "VmStealMetric/vcpucpu": "CPU利用率",
        "VmStealMetric/vmsteal": "CPU争抢",
        "VmStorageIOLatency/read_lat_ms": "IO读延迟",
        "VmStorageIOLatency/write_lat_ms": "IO写延迟", 
        "VmStorageIOLatency/read_iops": "读IOPS",
        "VmStorageIOLatency/write_iops": "写IOPS",
        "VmPpsBps/tx_pps": "出方向网络PPS",
        "VmPpsBps/rx_pps": "入方向网络PPS",
        "VmVportDropMetric/drop_ratio": "网络丢包率",
        "VmMemBWMetric/memory_bw": "内存带宽"
    }
    return metric_names.get(metric, metric)




async def collect_vm_data(request_id: str,
                         instance_id: str, 
                         metrics: Optional[List[str]] = None,
                         start_time: Optional[str] = None, 
                         end_time: Optional[str] = None,
                         data_purpose: str = "time_series",
                         custom_title: Optional[str] = None) -> Dict[str, Any]:
    """
    采集VM性能数据
    
    Args:
        request_id: 请求ID
        instance_id: 实例ID
        metrics: 指标列表，可选
        start_time: 开始时间，格式: YYYY-MM-DD HH:MM:SS
        end_time: 结束时间，格式: YYYY-MM-DD HH:MM:SS
        data_purpose: 数据用途，支持: time_series, bar_chart, pie_chart, table, line_chart
        custom_title: 自定义标题，如果不提供则自动生成
    
    Returns:
        Dict: 包含采集结果的字典
    """
    try:
        logger.info(f"采集VM数据: {instance_id} (请求ID: {request_id})")

        # 获取并处理数据（数据处理已集成到 list_vm_performance_metrics 中）
        processed_data = await list_vm_performance_metrics(instance_id, metrics, start_time, end_time)
        
        if not processed_data:
            return {
                "success": False,
                "error": "未获取到有效数据",
                "request_id": request_id
            }
        
        # 生成文件标题和用途描述
        file_title, file_purpose = _generate_file_metadata(
            instance_id, metrics, start_time, end_time, data_purpose, custom_title
        )
        
        # 一体化保存和注册（包含request_id和目录结构）
        return save_and_register_data(
            request_id, instance_id, processed_data, file_title, file_purpose
        )
        
    except Exception as e:
        logger.error(f"采集失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "request_id": request_id
        }




async def main():
    """测试示例"""
    import uuid
    
    # 生成测试用的request_id
    request_id = f"test_req_{uuid.uuid4().hex[:8]}"
    
    result = await collect_vm_data(
        request_id=request_id,
        instance_id="eci-0xi16p1femf8dbmxdqpp",
        metrics=["VmStealMetric/vcpucpu"],
        start_time='2025-08-28 00:00:00',
        end_time='2025-08-28 05:00:00',
        data_purpose="time_series",
        custom_title="测试实例CPU性能监控数据"
    )
    
    if result["success"]:
        print(f"✅ 采集成功: {result['json_file']}")
        print(f"📋 文件标题: {result['file_title']}")
        print(f"🎯 数据用途: {result['file_purpose']}")
        print(f"📊 {result['metrics_count']}个指标, {result['data_points']}个数据点")
        print(f"📁 文件大小: {result['file_size']}")
        print(f"🆔 请求ID: {result['request_id']}")
        print(f"📋 资源注册: {'成功' if result['resource_registered'] else '失败'}")
    else:
        print(f"❌ 采集失败: {result['error']}")
        print(f"🆔 请求ID: {result.get('request_id', 'N/A')}")


if __name__ == "__main__":
    asyncio.run(main())