
import logging
from typing import Optional, Dict, Any
from deep_diagnose.tools.mcp.mcp_tool_manager import MCPToolManager
from datetime import datetime, timedelta
logger = logging.getLogger(__name__)


async def check_customer_health(
    ali_uid: str, 
    start_time: Optional[str] = None,
    end_time: Optional[str] = None
) -> Optional[Dict[str, Any]]:

    try:

        # 参数规范化与校验
        ali_uid = str(ali_uid).strip() if ali_uid is not None else ""
        if not ali_uid:
            logger.error("ali_uid 不能为空")
            return None
        
        # 参数验证和时间计算：始终传递合法的字符串时间，满足工具 schema（startTime/endTime 必填）
        from datetime import datetime, timedelta

        def _parse_time(t: Optional[str], *, is_start: bool) -> Optional[datetime]:
            if t is None:
                return None
            t = str(t).strip()
            if not t:
                return None
            # 支持多种常见输入：YYYYMMDD、YYYY-MM-DD、YYYY-MM-DD HH:MM:SS
            try:
                if len(t) == 8 and t.isdigit():  # YYYYMMDD
                    base = datetime.strptime(t, "%Y%m%d")
                    return base.replace(hour=0, minute=0, second=0) if is_start else base.replace(hour=23, minute=59, second=59)
                if len(t) == 10 and t[4] == '-' and t[7] == '-':  # YYYY-MM-DD
                    base = datetime.strptime(t, "%Y-%m-%d")
                    return base.replace(hour=0, minute=0, second=0) if is_start else base.replace(hour=23, minute=59, second=59)
                # 尝试完整时间
                return datetime.strptime(t, "%Y-%m-%d %H:%M:%S")
            except Exception:
                logger.warning(f"时间格式无法解析: {t}, 期望 YYYYMMDD/YYYY-MM-DD/YYYY-MM-DD HH:MM:SS")
                return None

        now = datetime.now()
        start_dt = _parse_time(start_time, is_start=True)
        end_dt = _parse_time(end_time, is_start=False)

        if start_dt is None and end_dt is None:
            # 默认最近2天区间
            end_dt = now
            start_dt = end_dt - timedelta(days=2)
            logger.debug("使用默认时间范围（最近2天）")
        elif start_dt is None and end_dt is not None:
            # 只有结束时间：回退到结束前2天
            start_dt = end_dt - timedelta(days=2)
            logger.debug("仅提供结束时间，自动回退开始时间为结束前2天")
        elif start_dt is not None and end_dt is None:
            # 只有开始时间：设置结束为开始后2天（最多2天）
            end_dt = start_dt + timedelta(days=2)
            logger.debug("仅提供开始时间，自动设置结束时间为开始后2天")

        # 约束：结束>=开始
        if end_dt < start_dt:
            logger.debug("结束时间早于开始时间，交换处理")
            start_dt, end_dt = end_dt, start_dt

        # 长度限制：最多2天
        if (end_dt - start_dt) > timedelta(days=2):
            logger.debug("时间范围超过2天，自动裁剪为2天")
            end_dt = start_dt + timedelta(days=2)

        start_time_str = start_dt.strftime("%Y-%m-%d %H:%M:%S")
        end_time_str = end_dt.strftime("%Y-%m-%d %H:%M:%S")
        logger.debug(f"使用的时间范围: {start_time_str} 到 {end_time_str}")
        
        # 获取MCP工具管理器
        tool_manager = MCPToolManager()
        
        # 获取所有可用的MCP工具
        tools = await tool_manager.get_enabled_mcp_tools()
        
        # 查找checkHealthForCustomerVms工具
        check_health_tool = None
        for tool in tools:
            if tool.name == "checkHealthForCustomerVms":
                check_health_tool = tool
                break
        
        if check_health_tool is None:
            logger.error("checkHealthForCustomerVms工具未找到")
            return None
        
        # 构造工具入参：该工具 schema 要求 startTime/endTime 必填，我们已经确保为合法字符串
        tool_input = {
            "aliUid": ali_uid,  # 注意：使用camelCase
            "startTime": start_time_str,
            "endTime": end_time_str,
        }
        logger.info(f"开始客户健康检查 ali_uid={ali_uid}, range={start_time_str}~{end_time_str}")
        
        # 调用工具
        result = await check_health_tool.ainvoke(tool_input)
        
        logger.debug(f"客户 {ali_uid} 健康检查完成")
        return result
        
    except Exception as e:
        logger.error(f"检查客户健康状态失败: {e}")
        # 如需完整堆栈，请开启 DEBUG 级别
        try:
            import traceback, logging as _logging
            if logger.isEnabledFor(_logging.DEBUG):
                logger.debug(f"完整错误信息: {traceback.format_exc()}")
        except Exception:
            pass
        return None





async def main():
    """
    主函数 - 测试客户健康检查功能
    """
    import asyncio
    
    # 测试客户ID
    test_ali_uid = "1075639319543162"
    
    logger.info("=" * 60)
    logger.info("开始测试客户健康检查功能")
    logger.info(f"客户ID: {test_ali_uid}")
    logger.info("使用默认时间范围")
    logger.info("=" * 60)
    
    try:
        # 异步调用
        result = await check_customer_health(test_ali_uid,'2025-09-01 00:00:00','2025-09-02 00:00:00')
        
        if result is not None:
            logger.info("✅ 健康检查成功完成!")
            logger.info(f"结果类型: {type(result)}")
            logger.info("=" * 50)
            logger.info("📋 健康检查结果:")
            logger.info("=" * 50)
            logger.info(f"{result}")
            logger.info("=" * 50)
            
            # 如果结果是字符串，显示统计信息
            if isinstance(result, str):
                logger.info(f"📊 结果统计:")
                logger.info(f"   字符总数: {len(result)}")
                logger.info(f"   行数: {result.count(chr(10)) + 1}")
                logger.info(f"   是否为空: {'是' if len(result.strip()) == 0 else '否'}")
            elif isinstance(result, list):
                logger.info(f"📊 结果统计:")
                logger.info(f"   检测到的问题数量: {len(result)}")
                for i, item in enumerate(result[:3]):  # 显示前3个问题
                    logger.info(f"   问题 {i+1}: {item.get('exceptionName', '未知异常')}")
        else:
            logger.warning("⚠️ 健康检查返回空结果")
            
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        try:
            import traceback, logging as _logging
            if logger.isEnabledFor(_logging.DEBUG):
                logger.debug(f"完整错误信息: {traceback.format_exc()}")
        except Exception:
            pass
        return False
    
    logger.info("=" * 60)
    logger.info("🎉 测试完成!")
    logger.info("=" * 60)
    
    return True


if __name__ == "__main__":
    import asyncio
    import logging
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 运行测试
    asyncio.run(main())