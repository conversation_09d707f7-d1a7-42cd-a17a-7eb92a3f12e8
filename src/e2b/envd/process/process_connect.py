# Code generated by protoc-gen-connect-python 0.1.0.dev2, DO NOT EDIT.
from typing import Any, Generator, Coroutine, AsyncGenerator, Optional
from httpcore import ConnectionPool, AsyncConnectionPool

import e2b_connect as connect

from e2b.envd.process import process_pb2 as process_dot_process__pb2

ProcessName = "process.Process"


class ProcessClient:
    def __init__(
        self,
        base_url: str,
        *,
        pool: Optional[ConnectionPool] = None,
        async_pool: Optional[AsyncConnectionPool] = None,
        compressor=None,
        json=False,
        **opts,
    ):
        self._list = connect.Client(
            pool=pool,
            async_pool=async_pool,
            url=f"{base_url}/{ProcessName}/List",
            response_type=process_dot_process__pb2.ListResponse,
            compressor=compressor,
            json=json,
            **opts,
        )
        self._connect = connect.Client(
            pool=pool,
            async_pool=async_pool,
            url=f"{base_url}/{ProcessName}/Connect",
            response_type=process_dot_process__pb2.ConnectResponse,
            compressor=compressor,
            json=json,
            **opts,
        )
        self._start = connect.Client(
            pool=pool,
            async_pool=async_pool,
            url=f"{base_url}/{ProcessName}/Start",
            response_type=process_dot_process__pb2.StartResponse,
            compressor=compressor,
            json=json,
            **opts,
        )
        self._update = connect.Client(
            pool=pool,
            async_pool=async_pool,
            url=f"{base_url}/{ProcessName}/Update",
            response_type=process_dot_process__pb2.UpdateResponse,
            compressor=compressor,
            json=json,
            **opts,
        )
        self._stream_input = connect.Client(
            pool=pool,
            async_pool=async_pool,
            url=f"{base_url}/{ProcessName}/StreamInput",
            response_type=process_dot_process__pb2.StreamInputResponse,
            compressor=compressor,
            json=json,
            **opts,
        )
        self._send_input = connect.Client(
            pool=pool,
            async_pool=async_pool,
            url=f"{base_url}/{ProcessName}/SendInput",
            response_type=process_dot_process__pb2.SendInputResponse,
            compressor=compressor,
            json=json,
            **opts,
        )
        self._send_signal = connect.Client(
            pool=pool,
            async_pool=async_pool,
            url=f"{base_url}/{ProcessName}/SendSignal",
            response_type=process_dot_process__pb2.SendSignalResponse,
            compressor=compressor,
            json=json,
            **opts,
        )

    def list(
        self, req: process_dot_process__pb2.ListRequest, **opts
    ) -> process_dot_process__pb2.ListResponse:
        return self._list.call_unary(req, **opts)

    def alist(
        self, req: process_dot_process__pb2.ListRequest, **opts
    ) -> Coroutine[Any, Any, process_dot_process__pb2.ListResponse]:
        return self._list.acall_unary(req, **opts)

    def connect(
        self, req: process_dot_process__pb2.ConnectRequest, **opts
    ) -> Generator[process_dot_process__pb2.ConnectResponse, Any, None]:
        return self._connect.call_server_stream(req, **opts)

    def aconnect(
        self, req: process_dot_process__pb2.ConnectRequest, **opts
    ) -> AsyncGenerator[process_dot_process__pb2.ConnectResponse, Any]:
        return self._connect.acall_server_stream(req, **opts)

    def start(
        self, req: process_dot_process__pb2.StartRequest, **opts
    ) -> Generator[process_dot_process__pb2.StartResponse, Any, None]:
        return self._start.call_server_stream(req, **opts)

    def astart(
        self, req: process_dot_process__pb2.StartRequest, **opts
    ) -> AsyncGenerator[process_dot_process__pb2.StartResponse, Any]:
        return self._start.acall_server_stream(req, **opts)

    def update(
        self, req: process_dot_process__pb2.UpdateRequest, **opts
    ) -> process_dot_process__pb2.UpdateResponse:
        return self._update.call_unary(req, **opts)

    def aupdate(
        self, req: process_dot_process__pb2.UpdateRequest, **opts
    ) -> Coroutine[Any, Any, process_dot_process__pb2.UpdateResponse]:
        return self._update.acall_unary(req, **opts)

    def stream_input(
        self, req: process_dot_process__pb2.StreamInputRequest, **opts
    ) -> process_dot_process__pb2.StreamInputResponse:
        return self._stream_input.call_client_stream(req, **opts)

    def astream_input(
        self, req: process_dot_process__pb2.StreamInputRequest, **opts
    ) -> Coroutine[Any, Any, process_dot_process__pb2.StreamInputResponse]:
        return self._stream_input.acall_client_stream(req, **opts)

    def send_input(
        self, req: process_dot_process__pb2.SendInputRequest, **opts
    ) -> process_dot_process__pb2.SendInputResponse:
        return self._send_input.call_unary(req, **opts)

    def asend_input(
        self, req: process_dot_process__pb2.SendInputRequest, **opts
    ) -> Coroutine[Any, Any, process_dot_process__pb2.SendInputResponse]:
        return self._send_input.acall_unary(req, **opts)

    def send_signal(
        self, req: process_dot_process__pb2.SendSignalRequest, **opts
    ) -> process_dot_process__pb2.SendSignalResponse:
        return self._send_signal.call_unary(req, **opts)

    def asend_signal(
        self, req: process_dot_process__pb2.SendSignalRequest, **opts
    ) -> Coroutine[Any, Any, process_dot_process__pb2.SendSignalResponse]:
        return self._send_signal.acall_unary(req, **opts)
