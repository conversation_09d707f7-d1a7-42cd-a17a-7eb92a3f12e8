import base64
import os

from typing import Optional
from e2b_connect.client import Code, ConnectException

from e2b.exceptions import (
    SandboxException,
    InvalidArgumentException,
    NotFoundException,
    TimeoutException,
    format_sandbox_timeout_exception,
    AuthenticationException,
)
from e2b.connection_config import Username, default_username


def handle_rpc_exception(e: Exception):
    if isinstance(e, ConnectException):
        if e.status == Code.invalid_argument:
            return InvalidArgumentException(e.message)
        elif e.status == Code.unauthenticated:
            return AuthenticationException(e.message)
        elif e.status == Code.not_found:
            return NotFoundException(e.message)
        elif e.status == Code.unavailable:
            return format_sandbox_timeout_exception(e.message)
        elif e.status == Code.canceled:
            return TimeoutException(
                f"{e.message}: This error is likely due to exceeding 'request_timeout'. You can pass the request timeout value as an option when making the request."
            )
        elif e.status == Code.deadline_exceeded:
            return TimeoutException(
                f"{e.message}: This error is likely due to exceeding 'timeout' — the total time a long running request (like process or directory watch) can be active. It can be modified by passing 'timeout' when making the request. Use '0' to disable the timeout."
            )
        else:
            return SandboxException(f"{e.status}: {e.message}")
    else:
        return e


def authentication_header(user: Optional[Username] = None):
    # Use Bearer token for FC backend to be consistent with HTTP calls
    bearer_token = os.getenv('E2B_FC_BEARER_TOKEN')
    if bearer_token:
        # 修复：Bearer token认证时也要包含用户信息
        # FC后端需要知道是哪个用户在执行操作，解决"no user specified"错误
        username = user if user is not None else default_username
        return {
            "Authorization": f"Bearer {bearer_token}",
            "X-Username": username,  # 添加用户信息头部
        }

    # Fallback to Basic authentication for other backends
    value = f"{user if user is not None else default_username}:"
    encoded = base64.b64encode(value.encode("utf-8")).decode("utf-8")
    return {"Authorization": f"Basic {encoded}"}
