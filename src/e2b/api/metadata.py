import platform

from importlib import metadata

def _get_package_version():
    """Get package version with fallback for development environments."""
    try:
        return metadata.version("e2b")
    except metadata.PackageNotFoundError:
        # Fallback for development environment where e2b is not installed as a package
        try:
            # Try to get the main project version
            return metadata.version("ecs-deep-diagnose")
        except metadata.PackageNotFoundError:
            # Final fallback
            return "dev"

default_headers = {
    "lang": "python",
    "lang_version": platform.python_version(),
    "machine": platform.machine(),
    "os": platform.platform(),
    "package_version": _get_package_version(),
    "processor": platform.processor(),
    "publisher": "e2b",
    "release": platform.release(),
    "sdk_runtime": "python",
    "system": platform.system(),
}
