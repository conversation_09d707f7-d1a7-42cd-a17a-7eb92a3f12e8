"""
日志配置模块 - 提供应用的日志配置功能
"""

import os
import logging
import logging.config
from datetime import datetime
import os
APP_HOME_DIR = os.path.dirname(os.path.abspath(__file__))
# 检查是否为生产环境
import os
IS_PRODUCTION_ENV = os.getenv('app_env', 'default') in ["pre", "pre1", "prod"]

# 常量定义
MB_BYTES = 1024 * 1024
LOG_FILE_MAX_SIZE = 30 * MB_BYTES  # 日志大小 30M
LOG_FILE_BACKUP_COUNT = 30

def _get_log_directory() -> str:
    """
    根据应用环境确定日志目录。
    """
    if IS_PRODUCTION_ENV:
        # 生产或预发环境，使用 APP_HOME_DIR
        base_dir = APP_HOME_DIR
    else:
        # 开发环境，日志通常放在当前文件所在目录的相对位置
        base_dir = os.path.dirname(os.path.abspath(__file__))
    return os.path.join(base_dir, "logs")

WORK_PATH = _get_log_directory()

def _ensure_log_directory_exists(path: str):
    """确保日志目录存在，如果不存在则创建它。"""
    if not os.path.exists(path):
        os.makedirs(path, exist_ok=True)
_ensure_log_directory_exists(WORK_PATH)

# 自定义时间格式化函数，支持毫秒
class MillisecondFormatter(logging.Formatter):
    def formatTime(self, record, datefmt=None):
        # 获取当前时间并格式化为带毫秒的时间字符串
        ct = datetime.fromtimestamp(record.created)
        if datefmt:
            s = ct.strftime(datefmt)
        else:
            s = ct.strftime("%Y-%m-%d %H:%M:%S")
        # 添加毫秒部分
        return f"{s}.{int(record.msecs):03d}"

def _get_formatters() -> dict:
    """定义日志格式化器。"""
    return {
        'standard': {
            '()': MillisecondFormatter,  # 使用自定义格式化器
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
            'datefmt': '%Y-%m-%d %H:%M:%S',
        },
    }

def _get_handlers(log_path: str) -> dict:
    """定义日志处理器。"""
    return {
        'console': {
            'level': 'DEBUG',
            'formatter': 'standard',
            'class': 'logging.StreamHandler',
        },
        'request_detail': {
            "class": "logging.handlers.RotatingFileHandler",
            "level": "INFO",
            "formatter": "standard",
            "filename": os.path.join(log_path, "request_detail.log"),
            'maxBytes': LOG_FILE_MAX_SIZE,
            "backupCount": LOG_FILE_BACKUP_COUNT,
            'encoding': 'utf-8',
        }
    }

def _get_loggers() -> dict:
    """定义日志记录器及其配置。"""
    common_error_console_config = {
        'handlers': ['console'],
        'level': 'ERROR',
        'propagate': True,
    }
    return {
        '': {  # 根日志记录器
            'handlers': ['console'],
            'level': 'INFO',
            'propagate': True,
        },
        'uvicorn': {
            **common_error_console_config,
            'propagate': False, # Uvicorn 通常有自己的日志处理，避免重复记录
        },
        'fastmcp': {**common_error_console_config},
        'mcp_server': {**common_error_console_config},
        'utils': {**common_error_console_config},
        'mcp': {**common_error_console_config},
        'starlette': {**common_error_console_config},
        'request_detail': {
            'handlers': ['request_detail'],
            'level': 'INFO',
            'propagate': True, # 通常自定义应用日志会传播到根记录器
        }
    }

def configure_logging():
    """
    配置日志系统

    设置日志格式化器、处理器和记录器，确保应用中的日志输出一致且有用。
    """
    logging_config = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': _get_formatters(),
        'handlers': _get_handlers(WORK_PATH),
        'loggers': _get_loggers(),
    }
    logging.config.dictConfig(logging_config)

# 在模块加载时自动配置日志，通常这是期望的行为
# 如果需要在应用启动的特定阶段调用，可以将此行注释掉，并在主程序中显式调用 configure_logging()
# configure_logging() # 如果应用有明确的初始化流程，可以考虑在那里调用
