# Chat API V1 使用指南

## 📋 概述

Chat API V1 是一套标准化的流式聊天接口，为用户提供智能体对话、会话管理和消息历史查询功能。

### 🔧 基本信息
- **API 版本**: V1
- **基础路径**: `/api/v1`
- **认证方式**: JWT Token 认证
- **响应格式**: JSON / Server-Sent Events (SSE)

---

## 🏗️ 系统交互架构

下图展示了 CloudBot UI 与 ECS Deep Diagnose 后端服务的完整交互流程，包括认证机制、实时对话和历史查询两大核心功能模块：

![CloudBot UI 与 ECS Deep Diagnose 交互流程](../assets/png/11_cloudbot_ecs_interaction_flow.png)

### 🔑 架构要点

- **🔐 安全认证**: JWT Token Bearer认证 + 访问密钥验证
- **📡 实时通信**: Server-Sent Events 流式响应技术
- **💾 数据持久化**: PostgreSQL 数据库 + Redis 缓存加速
- **🔄 会话管理**: 用户会话上下文保持 + 历史记录查询
- **🎯 功能分离**: 实时对话交互 vs 历史数据查询
- **⚡ 性能优化**: 异步任务处理 + 流式传输
- **🏗️ 架构模式**: RESTful API + 微服务架构 + 事件驱动
- **🔧 技术栈**: FastAPI + LangChain + Celery + Redis
- **📊 监控运维**: 日志记录 + 错误处理 + 健康检查
- **🔀 负载均衡**: 多实例部署 + 请求分发
- **🛡️ 容错机制**: 重试策略 + 降级处理

### 📋 交互流程说明

1. **💬 对话窗口流程（实时交互）**
   - 用户通过 CloudBot UI 发起新建对话
   - 系统首先进行 JWT 认证获取访问令牌
   - 然后调用流式聊天接口进行实时对话
   - 通过 SSE 技术实现实时响应流传输

2. **📋 历史窗口流程（数据查询）**
   - 用户通过 CloudBot UI 查询历史对话
   - 调用会话列表 API 获取用户的所有会话
   - 调用会话详情 API 获取特定会话的消息历史
   - 返回结构化的历史数据供前端展示

---

## 🚀 快速开始

### 认证获取

在使用任何 API 接口前，您需要先获取认证 Token：

```bash
curl -X POST "http://localhost:8000/api/token" \
  -H "Content-Type: application/json" \
  -d '{
    "access_key": "admin",
    "secret_key": "admin",
    "token_lifetime_minutes": 60
  }'
```

获取到 Token 后，在后续的 API 调用中需要在请求头中包含：
```
Authorization: Bearer {您的Token}
```

---

## 📚 API 接口详情

### 💬 流式聊天接口

**接口路径**: `POST /api/v1/chat`

**功能描述**: 发起智能体对话，支持实时流式响应。该接口使用 Server-Sent Events (SSE) 技术，能够实时返回智能体的思考过程、执行步骤和最终结果。

#### 📝 请求参数

**Content-Type**: `application/json`

**请求体结构**:
```json
{
    "question": "用户问题",
    "agent": "智能体类型",
    "user_id": "用户ID（可选）",
    "session_id": "会话ID（可选）"
}
```

**参数说明**:

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| question | string | ✅ 是 | 用户问题内容<br/>• 长度限制：1-10000 字符<br/>• 支持中英文混合<br/>• 不能为空字符串 | "这些实例在2025年6月26日凌晨1-4点发生了不可用，请排查问题的原因。（i-t4n4vky24zw2w1qnqoyf, i-t4n74bsfzx58x0lj4qbh...）" |
| agent | string | ❌ 否 | 智能体类型<br/>• 默认值：ReasoningAgent<br/>• 可选值：ReasoningAgent | "ReasoningAgent" |
| user_id | string | ❌ 否 | 用户ID<br/>• 如果为空则从认证信息获取<br/>• 用于标识用户身份 | "emp123456" 或 "-1" |
| session_id | string | ❌ 否 | 会话ID<br/>• 如果为空则创建新会话<br/>• 用于继续已有对话 | "eb9deaeb-c489-4c82-8bd0-3500aad7c727" |

#### 📤 响应格式

**Content-Type**: `text/event-stream; charset=utf-8`

**响应头信息**:
```http
Cache-Control: no-cache
Connection: keep-alive
Content-Type: text/event-stream; charset=utf-8
```

**SSE 数据流格式**:
```
data: {"request_id": "req_123", "session_id": "session_456", "agent": "ReasoningAgent", "data": {...}, "status": "processing"}

data: {"request_id": "req_123", "session_id": "session_456", "agent": "ReasoningAgent", "data": {...}, "status": "processing"}

data: {"request_id": "req_123", "session_id": "session_456", "agent": "ReasoningAgent", "data": {...}, "status": "completed"}
```

**响应数据结构**:
```json
{
    "request_id": "请求追踪ID",
    "session_id": "会话ID（新会话时由服务生成）",
    "agent": "智能体类型",
    "data": "JSON字符串格式的智能体输出数据",
    "status": "处理状态：processing/completed/error"
}
```

**字段详细说明**:

| 字段名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| request_id | string | 请求追踪ID<br/>• 用于追踪整个请求生命周期<br/>• 便于问题排查和日志关联 | "req_e23e76a2-394e-4f5b-90bc-66c41dd442d6" |
| session_id | string | 会话ID<br/>• 新会话时由服务端自动生成<br/>• 用于会话管理和历史查询 | "3976e2e6-daf5-4595-a7ed-50725a2ae8cd" |
| agent | string | 智能体类型标识 | "ReasoningAgent" |
| data | string | 智能体输出数据<br/>• JSON 字符串格式<br/>• 包含完整的推理过程和结果 | "{\"thought\": \"...\", \"plan_steps\": [...], \"executions\": [...], \"result\": \"...\", \"urls\": [...]}" |
| status | string | 处理状态<br/>• `processing`: 处理中<br/>• `completed`: 完成<br/>• `error`: 错误 | "processing" |

**data 字段内容结构**（JSON 字符串解析后）:

| 字段名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| thought | string | 智能体思考过程<br/>• 实时累积更新<br/>• 展示推理逻辑 | "用户报告了多个ECS实例在2025年6月26日凌晨1-4点之间发生了不可用问题..." |
| plan_steps | array | 执行计划步骤<br/>• 包含 step、title、description<br/>• 结构化展示执行计划 | [{"step": 1, "title": "查询实例在出问题时间所在NC", "description": "..."}] |
| executions | array | 工具执行记录<br/>• 包含工具调用和结果<br/>• 追踪执行过程 | [{"tool_call_id": "call_abc123", "tool_name": "listVMHostHistory", "parameters": {...}, "result": {...}, "status": "completed"}] |
| result | string | 最终诊断结果<br/>• Markdown 格式<br/>• 包含完整的分析报告 | "## ECS实例不可用问题诊断报告\\n\\n### 问题概述\\n..." |
| urls | array | 相关报告链接<br/>• 生成的详细报告<br/>• 外部资源链接 | [{"name": "CloudBot智能体-长推理诊断报告", "url": "https://oss.example.com/reports/..."}] |
| event_type | string | 事件类型标识 | "reasoning" |

#### 🔢 状态码说明

| 状态码 | 状态描述 | 响应示例 | 说明 |
|--------|----------|----------|------|
| 200 | ✅ 成功 | SSE 数据流 | 正常返回事件流数据 |
| 400 | ❌ 请求错误 | `{"detail": "参数验证失败"}` | 请求参数格式错误或缺失 |
| 401 | 🔒 未授权 | `{"detail": "认证失败"}` | Token 无效或已过期 |
| 500 | ⚠️ 服务器错误 | `{"detail": "聊天服务执行失败: 错误信息"}` | 服务器内部处理异常 |

#### 📋 请求示例

**基本请求（新会话）**:
```bash
curl -X POST "http://localhost:8000/api/v1/chat" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {您的Token}" \
  -d '{
    "question": "这些实例在2025年6月26日凌晨1-4点发生了不可用，请排查问题的原因。（i-t4n4vky24zw2w1qnqoyf, i-t4n74bsfzx58x0lj4qbh, i-t4na3cc0c9mimcw9667x, i-j6ch2zf4qfy1rltbql6r, i-2vc5alcmxz75rw8aol4g, i-2vc0zliaw8ilg744cdrq, i-2vc6qv34j96hkmcwms5d",
    "agent": "ReasoningAgent"
  }'
```

**继续已有会话**:
```bash
curl -X POST "http://localhost:8000/api/v1/chat" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {您的Token}" \
  -d '{
    "question": "请详细分析一下这个变更任务的具体影响范围",
    "agent": "ReasoningAgent",
    "session_id": "eb9deaeb-c489-4c82-8bd0-3500aad7c727"
  }'
```

**指定用户ID**:
```bash
curl -X POST "http://localhost:8000/api/v1/chat" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {您的Token}" \
  -d '{
    "question": "帮我分析这个系统的性能瓶颈",
    "agent": "ReasoningAgent",
    "user_id": "emp123456"
  }'
```

#### 📄 响应示例

以下是基于真实 API 响应的示例数据：

```
data: {"request_id": "req_8e42c21e-0ae1-4d9c-8d29-a9ce6d55c33e", "session_id": "cbf7a067-d898-4514-b310-197ead67e8ee", "agent": "ReasoningAgent", "data": "{\"thought\": \"用户报告多个ECS实例在2025年6月26日凌晨1-4点之间发生了不可用问题，需要排查根本原因。首先需要确认这些实例在故障发生时是否位于同一物理机（NC）上，以判断是否存在物理机层面的聚集性故障。然后需要逐一诊断每个实例的不可用根本原因。最后需要检查这些实例是否受到高危变更的影响。\", \"plan_steps\": [{\"step\": 1, \"title\": \"查询实例在故障时间所在的物理机（NC）\", \"description\": \"使用listVMHostHistory工具查询实例[i-t4n4vky24zw2w1qnqoyf, i-t4n74bsfzx58x0lj4qbh, i-t4na3cc0c9mimcw9667x, i-j6ch2zf4qfy1rltbql6r, i-2vc5alcmxz75rw8aol4g, i-2vc0zliaw8ilg744cdrq, i-2vc6qv34j96hkmcwms5d]在2025-06-26 01:00:00至2025-06-26 04:00:00所在的物理机（NC），分析是否存在物理机层面的聚集性故障。\"}, {\"step\": 2, \"title\": \"诊断每个实例的不可用根本原因\", \"description\": \"使用runVMUnavailableDiagnose工具诊断实例[i-t4n4vky24zw2w1qnqoyf, i-t4n74bsfzx58x0lj4qbh, i-t4na3cc0c9mimcw9667x, i-j6ch2zf4qfy1rltbql6r, i-2vc5alcmxz75rw8aol4g, i-2vc0zliaw8ilg744cdrq, i-2vc6qv34j96hkmcwms5d]在2025-06-26 01:00:00至2025-06-26 04:00:00的不可用根本原因。\"}, {\"step\": 3, \"title\": \"查询实例的高危变更记录\", \"description\": \"使用listChangeRecords工具查询实例[i-t4n4vky24zw2w1qnqoyf, i-t4n74bsfzx58x0lj4qbh, i-t4na3cc0c9mimcw9667x, i-j6ch2zf4qfy1rltbql6r, i-2vc5alcmxz75rw8aol4g, i-2vc0zliaw8ilg744cdrq, i-2vc6qv34j96hkmcwms5d]在2025-06-26 01:00:00至2025-06-26 04:00:00的高危变更记录。\"}], \"executions\": [{\"tool_call_id\": \"\", \"tool_run_id\": \"\", \"tool_name\": \"listVmHostHistory\", \"parameters\": {\"instanceId\": \"i-2vc6qv34j96hkmcwms5d\", \"startTime\": \"2025-06-26 01:00:00\", \"endTime\": \"2025-06-26 04:00:00\"}, \"result\": null, \"status\": \"completed\"}], \"result\": \"\", \"urls\": [], \"event_type\": \"reasoning\"}", "status": "processing"}

data: {"request_id": "req_8e42c21e-0ae1-4d9c-8d29-a9ce6d55c33e", "session_id": "cbf7a067-d898-4514-b310-197ead67e8ee", "agent": "ReasoningAgent", "data": "{\"thought\": \"用户报告多个ECS实例在2025年6月26日凌晨1-4点之间发生了不可用问题，需要排查根本原因。首先需要确认这些实例在故障发生时是否位于同一物理机（NC）上，以判断是否存在物理机层面的聚集性故障。然后需要逐一诊断每个实例的不可用根本原因。最后需要检查这些实例是否受到高危变更的影响。\", \"plan_steps\": [{\"step\": 1, \"title\": \"查询实例在故障时间所在的物理机（NC）\", \"description\": \"使用listVMHostHistory工具查询实例在指定时间段的物理机信息\"}, {\"step\": 2, \"title\": \"诊断每个实例的不可用根本原因\", \"description\": \"使用runVMUnavailableDiagnose工具诊断实例的不可用根本原因\"}, {\"step\": 3, \"title\": \"查询实例的高危变更记录\", \"description\": \"使用listChangeRecords工具查询实例的高危变更记录\"}], \"executions\": [{\"tool_call_id\": \"\", \"tool_run_id\": \"\", \"tool_name\": \"listVmHostHistory\", \"parameters\": {\"instanceId\": \"i-2vc6qv34j96hkmcwms5d\", \"startTime\": \"2025-06-26 01:00:00\", \"endTime\": \"2025-06-26 04:00:00\"}, \"result\": null, \"status\": \"completed\"}, {\"tool_call_id\": \"\", \"tool_run_id\": \"\", \"tool_name\": \"runVmUnavailableDiagnose\", \"parameters\": {}, \"result\": null, \"status\": \"started\"}], \"result\": \"\", \"urls\": [], \"event_type\": \"reasoning\"}", "status": "processing"}
```

---

### 📋 获取用户聊天会话列表

**接口路径**: `GET /api/v1/chat/sessions`

**功能描述**: 获取指定用户的所有聊天会话列表，按创建时间倒序排列。

#### 📝 请求参数

**查询参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| user_id | string | ✅ 是 | 用户ID或员工工号<br/>• 用于标识用户身份<br/>• 支持数字和字符串格式 | "emp123456" 或 "-1" |

#### 📤 响应格式

**Content-Type**: `application/json`

**响应数据结构**:
```json
[
    {
        "session_id": "会话ID",
        "title": "会话标题",
        "gmt_create": "创建时间"
    }
]
```

**字段详细说明**:

| 字段名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| session_id | string | 会话唯一标识<br/>• UUID 格式<br/>• 用于后续消息查询 | "eb9deaeb-c489-4c82-8bd0-3500aad7c727" |
| title | string | 会话标题<br/>• 通常为用户首次提问内容<br/>• 自动截取前部分内容 | "这些实例在2025年6月26日凌晨1-4点发生了不可用，请排查问题的原因。（i-t4n4vky24zw2w1qnqoyf, i-t4n74bsfzx58x0lj4qbh...）" |
| gmt_create | datetime | 会话创建时间<br/>• ISO 8601 格式<br/>• UTC 时区 | "2025-07-22T10:07:04.787978Z" |

#### 🔢 状态码说明

| 状态码 | 状态描述 | 响应示例 | 说明 |
|--------|----------|----------|------|
| 200 | ✅ 成功 | 会话列表数组 | 成功返回用户会话列表 |
| 400 | ❌ 请求错误 | `{"detail": "user_id参数缺失"}` | 缺少必需的 user_id 参数 |
| 401 | 🔒 未授权 | `{"detail": "认证失败"}` | Token 无效或已过期 |
| 500 | ⚠️ 服务器错误 | `{"detail": "获取用户会话列表失败: 数据库连接异常"}` | 服务器内部处理异常 |

#### 📋 请求示例

```bash
curl -X GET "http://localhost:8000/api/v1/chat/sessions?user_id=-1" \
  -H "Authorization: Bearer {您的Token}"
```

#### 📄 响应示例

以下是基于真实 API 响应的示例数据：

```json
[
    {
        "session_id": "eb9deaeb-c489-4c82-8bd0-3500aad7c727",
        "title": "这些实例在2025年6月26日凌晨1-4点发生了不可用，请排查问题的原因。（i-t4n4vky24zw2w1qnqoyf, i-t4n74bsfzx58x0lj4qbh, i-t4na3cc0c9mimcw9667x, i-j6ch2zf4qfy1rltbql6r, i-2vc5alcmxz75rw8aol4g, i-2vc0zliaw8ilg744cdrq, i-2vc6qv34j96hkmcwms5d",
        "gmt_create": "2025-07-22T10:07:04.787978Z"
    },
    {
        "session_id": "5a3d3617-ea98-4199-ac89-f5aeac7fd4c5",
        "title": "这些实例在2025年6月26日凌晨1-4点发生了不可用，请排查问题的原因。（i-t4n4vky24zw2w1qnqoyf, i-t4n74bsfzx58x0lj4qbh, i-t4na3cc0c9mimcw9667x, i-j6ch2zf4qfy1rltbql6r, i-2vc5alcmxz75rw8aol4g, i-2vc0zliaw8ilg744cdrq, i-2vc6qv34j96hkmcwms5d",
        "gmt_create": "2025-07-22T10:01:08.230332Z"
    },
    {
        "session_id": "de5bad47-4ee3-459c-be0e-618443be0f3b",
        "title": "这些实例在2025年6月26日凌晨1-4点发生了不可用，请排查问题的原因。（i-t4n4vky24zw2w1qnqoyf, i-t4n74bsfzx58x0lj4qbh, i-t4na3cc0c9mimcw9667x, i-j6ch2zf4qfy1rltbql6r, i-2vc5alcmxz75rw8aol4g, i-2vc0zliaw8ilg744cdrq, i-2vc6qv34j96hkmcwms5d",
        "gmt_create": "2025-07-22T09:55:42.346860Z"
    }
]
```

---

### 💬 获取会话消息列表

**接口路径**: `GET /api/v1/chat/sessions/{session_id}/messages`

**功能描述**: 获取指定会话的完整消息历史记录，包括用户问题和 AI 回复。支持从 Redis 查询最新状态信息。

#### 📝 请求参数

**路径参数**:

| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| session_id | string | ✅ 是 | 会话唯一标识<br/>• UUID 格式<br/>• 从会话列表接口获取 | "eb9deaeb-c489-4c82-8bd0-3500aad7c727" |

#### 📤 响应格式

**Content-Type**: `application/json`

**响应数据结构**:
```json
[
    {
        "message": "消息内容",
        "message_type": "消息类型",
        "gmt_create": "创建时间"
    }
]
```

**字段详细说明**:

| 字段名 | 类型 | 描述 | 示例值 |
|--------|------|------|--------|
| message | string | 消息内容<br/>• 用户问题或 AI 回复<br/>• AI 回复为 Markdown 格式 | "这些实例在2025年6月26日凌晨1-4点发生了不可用，请排查问题的原因。（i-t4n4vky24zw2w1qnqoyf...）" |
| message_type | string | 消息类型<br/>• `human`: 用户消息<br/>• `ai`: AI 回复 | "human" 或 "ai" |
| gmt_create | datetime | 消息创建时间<br/>• ISO 8601 格式<br/>• UTC 时区 | "2025-07-22T10:07:05.257759Z" |

#### ⚡ 特殊处理机制

- **实时状态查询**: 如果消息状态为未完成 (status=1)，会从 Redis 查询最新信息
- **时间排序**: 返回的消息按创建时间正序排列
- **完整历史**: 包含会话中的所有用户问题和 AI 回复

#### 🔢 状态码说明

| 状态码 | 状态描述 | 响应示例 | 说明 |
|--------|----------|----------|------|
| 200 | ✅ 成功 | 消息列表数组 | 成功返回会话消息列表 |
| 400 | ❌ 请求错误 | `{"detail": "session_id参数无效"}` | session_id 格式错误或无效 |
| 401 | 🔒 未授权 | `{"detail": "认证失败"}` | Token 无效或已过期 |
| 404 | 🔍 未找到 | `{"detail": "会话不存在"}` | 指定的会话ID不存在 |
| 500 | ⚠️ 服务器错误 | `{"detail": "获取会话消息列表失败: Redis连接超时"}` | 服务器内部处理异常 |

#### 📋 请求示例

```bash
curl -X GET "http://localhost:8000/api/v1/chat/sessions/eb9deaeb-c489-4c82-8bd0-3500aad7c727/messages" \
  -H "Authorization: Bearer {您的Token}"
```

#### 📄 响应示例

以下是基于真实 API 响应的示例数据：

```json
[
    {
        "message": "这些实例在2025年6月26日凌晨1-4点发生了不可用，请排查问题的原因。（i-t4n4vky24zw2w1qnqoyf, i-t4n74bsfzx58x0lj4qbh, i-t4na3cc0c9mimcw9667x, i-j6ch2zf4qfy1rltbql6r, i-2vc5alcmxz75rw8aol4g, i-2vc0zliaw8ilg744cdrq, i-2vc6qv34j96hkmcwms5d",
        "message_type": "human",
        "gmt_create": "2025-07-22T10:07:05.257759Z"
    },
    {
        "message": "# 批量实例不可用问题排查诊断报告\n\n## 诊断信息\n**受影响实例列表**:\n- i-t4n4vky24zw2w1qnqoyf  \n- i-t4n74bsfzx58x0lj4qbh  \n- i-t4na3cc0c9mimcw9667x  \n- i-j6ch2zf4qfy1rltbql6r  \n- i-2vc5alcmxz75rw8aol4g  \n- i-2vc0zliaw8ilg744cdrq  \n- i-2vc6qv34j96hkmcwms5d  \n\n**时间范围**: 2025-06-26 01:00:00 至 2025-06-26 04:00:00  \n**问题描述**: 多个ECS实例在凌晨1-4点之间出现不可用问题，表现为虚拟机VSOCK_ICMP_PING全丢包（`vm_vsock_icmp_ping_loss_new`）\n\n## 关键要点\n\n- **高危变更任务触发时间**：2025-06-26 03:47:07，任务ID `procedure-gear-503299387-638189262`  \n- **变更服务名称**：`EcsVirtVMUpdateEngine.HotUpgradeCmd#`  \n- **变更影响**：导致 `vm_vsock_icmp_ping_loss_new` 异常，表现为VSOCK网络通信全丢包  \n- **受影响实例**：包括所有用户反馈中提及的7个实例  \n- **变更来源**：`tianji_vm`，表明该变更为虚拟机层面热升级操作  \n- **责任人信息**：078005, 汤誓, 徐云  \n\n## 推断过程\n\n1. **变更记录核查**\n   - 使用 `listChangeRecords` 工具查询所有受影响实例在 2025-06-26 01:00:00 至 2025-06-26 04:00:00 的变更记录  \n   - 发现一个高危变更任务 `procedure-gear-503299387-638189262` 于 03:47:07 提交，服务为 `EcsVirtVMUpdateEngine.HotUpgradeCmd#`  \n   - 变更描述明确指出其导致 `vm_vsock_icmp_ping_loss_new` 异常，表现为虚拟机VSOCK网络通信异常  \n\n2. **影响范围分析**\n   - 该变更明确列出了所有7个用户反馈的实例为受影响对象  \n   - 表明这些实例在故障时间段内均处于同一个NC（物理机）或共享同一类热升级任务  \n   - 异常类型 `vm_vsock_icmp_ping_loss_new` 直接指向虚拟机与宿主机之间的VSOCK通信中断  \n\n3. **时间相关性分析**\n   - 变更提交时间为 03:47:07，接近用户反馈的不可用时间段（凌晨1-4点）  \n   - 符合用户反馈的集中性故障特征  \n   - 表明该变更为此次批量实例不可用的主要诱因  \n\n4. **变更来源与责任人确认**\n   - 变更来源为 `tianji_vm`，属于虚拟机热升级任务，进一步确认其与VSOCK相关的网络异常  \n   - 责任人信息完整，可追溯至具体责任人（汤誓、徐云）  \n\n## 总结及建议\n\n综合本次诊断结果：\n\n- 此次多个ECS实例不可用的根本原因是 **虚拟机热升级任务** `EcsVirtVMUpdateEngine.HotUpgradeCmd#` 导致的 **VSOCK网络通信中断异常（`vm_vsock_icmp_ping_loss_new`）**\n- 该变更任务于 2025-06-26 03:47:07 提交，直接影响了包括用户反馈中的7个实例在内的虚拟机网络通信能力\n- 变更来源为 `tianji_vm`，且责任人信息明确\n\n**建议后续措施：**\n\n1. **我们将立即评估变更影响范围**：确认是否还有其他未被用户反馈的实例受到影响  \n2. **建议回滚变更**：若该异常尚未自动恢复，运维团队将协助回滚 `procedure-gear-503299387-638189262` 变更任务  \n3. **我们将联系责任人**：汤誓、徐云，获取该变更的详细内容、预期影响及修复计划  \n4. **建议加强变更前影响评估机制**：特别是涉及虚拟机底层通信机制的热升级操作  \n5. **我们将对受影响实例进行网络功能检查**：确保VSOCK通信恢复正常，服务状态稳定  \n\n---\n\n**引用数据**\n\n| 字段 | 内容 |\n|------|------|\n| 变更时间 | 2025-06-26 03:47:07 |\n| 变更任务ID | procedure-gear-503299387-638189262 |\n| 服务名称 | EcsVirtVMUpdateEngine.HotUpgradeCmd# |\n| 异常描述 | vm_vsock_icmp_ping_loss_new |\n| 责任人 | 078005, 汤誓, 徐云 |\n| 来源 | tianji_vm |\n| 受影响实例 | i-t4n4vky24zw2w1qnqoyf, i-t4n74bsfzx58x0lj4qbh, i-t4na3cc0c9mimcw9667x, i-j6ch2zf4qfy1rltbql6r, i-2vc5alcmxz75rw8aol4g, i-2vc0zliaw8ilg744cdrq, i-2vc6qv34j96hkmcwms5d |",
        "message_type": "ai",
        "gmt_create": "2025-07-22T10:10:17.193526Z"
    }
]
```

---

## 📖 使用指南

### 🔄 基本聊天流程

1. **🚀 发起聊天请求**
   - 调用 `POST /api/v1/chat` 接口
   - 必填参数：`question`（用户问题）
   - 可选参数：`agent`（智能体类型，默认 ReasoningAgent）
   - 可选参数：`user_id`（用户ID，为空时从认证信息获取）
   - 可选参数：`session_id`（会话ID，为空时创建新会话）

2. **📡 监听 SSE 事件流**
   - 客户端保持连接
   - 实时接收 Server-Sent Events 数据

3. **📊 状态判断**
   - 根据响应中的 `status` 字段判断处理状态
   - `processing`: 处理中，继续监听
   - `completed`: 处理完成
   - `error`: 处理出错

4. **✅ 完成处理**
   - 当 `status` 为 `completed` 时获取最终结果
   - 解析 `data` 字段中的完整诊断报告

### 📚 会话管理

1. **📋 获取会话列表**
   - 使用 `GET /api/v1/chat/sessions`
   - 传入 `user_id` 参数获取用户的所有会话
   - 如果不传 `user_id`，系统会从认证信息中获取

2. **💬 查看历史消息**
   - 使用 `GET /api/v1/chat/sessions/{session_id}/messages`
   - 查看特定会话的完整消息历史

3. **🔄 继续会话**
   - **新会话**: 不传 `session_id` 参数，系统自动创建新会话
   - **继续会话**: 在聊天请求中传入已有的 `session_id`
   - 系统会保持对话上下文，智能体能够理解之前的对话内容

4. **👤 用户身份管理**
   - **自动识别**: 不传 `user_id` 参数，系统从 JWT Token 中获取用户信息
   - **手动指定**: 传入 `user_id` 参数，明确指定用户身份
   - 用于多用户环境下的身份区分和权限控制

### 🔐 认证机制

所有 API 接口都需要通过 JWT Token 认证：

1. **🎫 获取 Token**
   ```bash
   curl -X POST "http://localhost:8000/api/token" \
     -H "Content-Type: application/json" \
     -d '{
       "access_key": "admin",
       "secret_key": "admin",
       "token_lifetime_minutes": 60
     }'
   ```

2. **🔑 使用 Token**
   ```http
   Authorization: Bearer {您的Token}
   ```

### 📊 数据特点

- **🔄 全量状态**: 流式聊天接口返回的 `data` 字段是智能体的全量累积状态
- **⚡ 实时更新**: 每次 SSE 事件都包含完整的当前状态信息
- **🔍 请求追踪**: `request_id` 用于追踪整个请求的生命周期
- **💾 Redis 缓存**: 未完成的消息会从 Redis 查询最新状态

### 💡 最佳实践

1. **🛡️ 错误处理**
   - 客户端应妥善处理各种 HTTP 状态码
   - 实现优雅的错误提示和重试机制
   - 对于参数验证错误（400），检查请求参数格式

2. **🔗 连接管理**
   - 实现自动重连机制处理网络中断
   - 监听连接状态，及时处理断线重连

3. **⚡ 性能优化**
   - 对会话列表和消息列表实现分页
   - 合理使用客户端缓存减少请求
   - 避免频繁创建新会话，优先使用现有会话
   
4**📋 参数使用建议**
   - **question**: 确保问题描述清晰具体，有助于获得更准确的诊断结果
   - **session_id**: 在多轮对话中保持使用同一个 session_id，确保上下文连贯性
   - **user_id**: 在多用户系统中明确指定，避免权限混乱
   - **agent**: 根据具体需求选择合适的智能体类型（目前支持 ReasoningAgent）

5**🎯 使用场景优化**
   - **新问题诊断**: 不传 session_id，让系统创建新会话
   - **追问和深入分析**: 传入已有 session_id，利用对话历史
   - **批量问题处理**: 为每个独立问题创建单独会话，避免上下文干扰

---
