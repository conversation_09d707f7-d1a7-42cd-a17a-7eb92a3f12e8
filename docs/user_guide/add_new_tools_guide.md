# 如何向智能助手添加新工具

## 背景

### 为什么需要增加工具

在 ECS 深度诊断系统中，智能助手通过调用各种工具来完成诊断任务。随着业务需求的增长和新功能的开发，我们需要不断扩展智能助手的能力，为其添加新的工具来支持更多的诊断场景。

新工具的添加能够：
- 扩展智能助手的诊断能力范围
- 支持新的业务场景和需求
- 提升诊断的准确性和效率
- 满足不同用户群体的特定需求

## 增加工具流程

### 步骤1：系统配置中添加新工具

首先需要在系统配置文件中添加新工具的配置信息。

#### 1.1 修改配置文件

编辑配置文件：`src/deep_diagnose/common/config/files/config_prod.yaml`

在 `mcp_servers` 部分的 `diagnose_prod` 节点下的 `enabled_tools` 列表中添加新工具：

```yaml
mcp_servers:
  diagnose_prod:
    protocol: streamable_http
    base_url: http://xmca-cloudbot.aliyun-inc.com
    timeout: 20
    path: /mcp/mcp/
    token: M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU=
    auth: bearer
    enabled_tools:
      # 现有工具...
      - runLiveMigrationCheck
      - runColdMigrationCheck
      - listLiveMigrationRecords
      - listColdMigrationRecords
      - runOpsEventPostpone
      - runOperationSubmit
      - listChangeRecords
      - listActionTrail
      - listReportedOperationalEvents
      - runVmStartStopDiagnose
      - runScreenShotDiagnose
      - runPerformanceDiagnose
      - runDiagnose
      - listMonitorExceptions
      - listCategorizedMonitorExceptions
      - listHealthStatus
      - listVmsOnNc
      - listVmHostHistory
      - getUserInfo
      - getNcBasicInfo
      - getVmBasicInfo
      - getDiskInfo
      - listOnDutyStaffs
      - listOperationRecords
      - listOperationRuleMatchRecords
      - listKnowledge
      - runVmUnavailableDiagnose
      - analyzeVmcoreFromCore
      - analyzeVmcoreFromVirt
      - analyzeVmcoreFromJinlun
      - checkHealthForCustomerVms
      # 新增工具示例
      - newToolName  # 在此处添加您的新工具名称
```

#### 1.2 配置说明

- `protocol`: 通信协议，通常为 `streamable_http`
- `base_url`: MCP 服务器的基础 URL
- `timeout`: 请求超时时间（秒）
- `path`: MCP 服务的路径
- `token`: 认证令牌
- `auth`: 认证方式，通常为 `bearer`
- `enabled_tools`: 启用的工具列表

### 步骤2：更新交互式智能体的工具白名单

#### 2.1 修改交互式智能体代码

编辑文件：`src/deep_diagnose/core/interactive/interactive_agent.py`

在 `__init__` 方法中更新 `mcp_safe_tools` 或 `mcp_privileged_tools` 列表：

```python
def __init__(self, config: Optional[Dict[str, Any]] = None):
    """
    初始化函数，接收一个可选的配置字典参数。

    参数:
    - config: Optional[Dict[str, Any]] - 一个可选的配置字典，用于初始化类实例。

    该函数主要负责初始化类实例，并设置MCP工具白名单，即允许使用的一组操作或功能。
    """
    # 调用父类的初始化方法，传递配置参数
    super().__init__(config)

    # MCP工具白名单，包含了一系列允许执行的操作或功能
    self.mcp_safe_tools = [
        "runLiveMigrationCheck", "runColdMigrationCheck", "listLiveMigrationRecords", "listColdMigrationRecords",
        "runOpsEventPostpone", "runOperationSubmit", "listChangeRecords", "listOperationRecords", "listActionTrail",
        "listReportedOperationalEvents", "runVmStartStopDiagnose", "runScreenShotDiagnose",
        "runPerformanceDiagnose", "runDiagnose", "listHealthStatus", "listOnDutyStaffs", "getDiskInfo",
        "listVmHostHistory", "getUserInfo", "getNcBasicInfo", "getVmBasicInfo", "listOperationRecords",
        "listMonitorExceptions", "listKnowledge",
        "newToolName"  # 在此处添加您的新工具名称
    ]
    
    # 特权工具列表（需要更高权限的工具）
    self.mcp_privileged_tools = [
        "listOperationRuleMatchRecords", "listVmsOnNc"
        # 如果新工具需要特权访问，可以添加到此列表
    ]
```

#### 2.2 工具分类说明

- **mcp_safe_tools**: 安全工具列表，TAM（技术支持）用户可以使用的低风险工具
- **mcp_privileged_tools**: 特权工具列表，只有开发者（dev）权限可以使用的高风险工具

根据新工具的风险等级和使用场景，选择合适的列表添加。

### 步骤3：重启服务

完成配置修改后，需要重启相关服务使配置生效：

```bash
# 重启后端服务
cd src
uv run server.py


```

## 测试


### 集成测试

参考文件：`tests/integration/api/test_chat_v1_integration.py`

创建集成测试来验证新工具在完整流程中的工作情况：

```python
async def test_new_tool_integration():
    """测试新工具的集成功能"""
    tester = ChatV1APITester()
    
    # 获取认证令牌
    token = await tester.authenticate()
    tester.token = token
    tester.headers["Authorization"] = f"Bearer {token}"
    
    # 构造测试请求，包含需要使用新工具的问题
    request_data = {
        "question": "请使用新工具进行相关操作",  # 根据实际工具功能修改
        "authority": "dev"  # 或 "tam"，根据工具权限设置
    }
    
    # 执行测试
    result = await tester._stream_chat(request_data)
    
    # 验证结果
    assert result["total_events"] > 0
    # 添加更多具体的验证逻辑
```

### 手动集成测试

使用预发布环境进行手动测试：

1. 访问预发布环境：https://pre-cloudbot2.alibaba-inc.com/cloudbot/ng2/?pageVersion=0.0.001#/operations/diagnostic-aid
2. 创建新的诊断会话
3. 输入需要使用新工具的问题
4. 观察智能助手是否能够正确调用新工具
5. 验证工具返回结果的准确性

## 验证清单

在添加新工具后，请确保完成以下验证：

### 配置验证
- [ ] 新工具已添加到 `config_prod.yaml` 的 `enabled_tools` 列表中
- [ ] 工具名称拼写正确，与 MCP 服务器提供的工具名称一致
- [ ] 配置文件语法正确（YAML 格式无误）

### 代码验证
- [ ] 新工具已添加到适当的工具白名单中（`mcp_safe_tools` 或 `mcp_privileged_tools`）
- [ ] 权限分类正确（根据工具风险等级选择合适的列表）
- [ ] 代码语法正确，无编译错误

### 功能验证
- [ ] 服务重启后新工具可用
- [ ] 智能助手能够发现并调用新工具
- [ ] 工具调用参数正确传递
- [ ] 工具返回结果格式正确
- [ ] 错误处理机制正常工作

### 测试验证
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 手动测试验证功能正常
- [ ] 性能测试满足要求

## 注意事项

### 安全考虑
1. **权限控制**: 根据工具的风险等级选择合适的权限分类
2. **参数验证**: 确保工具调用参数经过适当的验证和清理
3. **错误处理**: 实现完善的错误处理机制，避免敏感信息泄露

### 性能考虑
1. **超时设置**: 根据工具的执行时间合理设置超时参数
2. **并发控制**: 考虑工具在高并发场景下的表现
3. **资源管理**: 确保工具调用不会消耗过多系统资源

### 维护考虑
1. **文档更新**: 及时更新相关文档和注释
2. **版本兼容**: 确保新工具与现有系统版本兼容
3. **监控告警**: 为新工具添加适当的监控和告警机制

## 常见问题

### Q1: 新工具添加后不可用？
**A**: 检查以下几个方面：
- 确认配置文件中的工具名称与 MCP 服务器提供的名称完全一致
- 确认工具已添加到交互式智能体的白名单中
- 确认服务已重启并加载了新配置
- 检查日志中是否有相关错误信息

### Q2: 工具调用权限不足？
**A**: 检查权限配置：
- 确认工具在正确的权限列表中（`mcp_safe_tools` 或 `mcp_privileged_tools`）
- 确认用户具有相应的权限级别（`dev` 或 `tam`）
- 检查 MCP 服务器端的权限配置

### Q3: 工具调用超时？
**A**: 调整超时配置：
- 检查配置文件中的 `timeout` 设置
- 根据工具的实际执行时间调整超时值
- 考虑工具的异步执行模式

### Q4: 工具返回结果格式不正确？
**A**: 检查接口兼容性：
- 确认 MCP 服务器返回的数据格式符合预期
- 检查工具的输入参数是否正确
- 验证数据序列化和反序列化过程
