# SOP 开发与维护高级指南

本文档为专家用户提供了一套完整的SOP（Standard Operating Procedure）开发与维护指南，涵盖从撰写、调试、验证到线上应急响应的全生命周期，旨在帮助开发者构建和维护高质量的自动化诊断流程。

---

## 1. SOP核心概念与工作流

### 1.1. SOP在自动化诊断中的角色

在本系统中，SOP不仅是一份操作文档，更是驱动自动化诊断引擎执行的**“可执行剧本”**。它将领域专家的诊断知识范式化、结构化，使得Agent能够理解并自主调用工具，模拟专家进行问题排查。

一个设计良好的SOP是实现精准、高效、稳定自动化诊断的基石。

### 1.2. 核心工作流详解

整个自动化诊断流程由SOP驱动，遵循以下四个核心阶段。理解此工作流是开发和调试SOP的前提。

![SOP驱动的诊断工作流](../assets/png/21_sop_diagnostic_workflow.png)
*图1: SOP驱动的诊断工作流 *

1.  **SOP路由 (SOP Routing)**: 系统根据用户输入，通过匹配 `sop_diagnosis_config.yaml` 中的规则，选择最合适的SOP。
2.  **计划生成 (Planning)**: 加载SOP模板（.md文件），将其中的结构化步骤解析为Agent可执行的计划。
3.  **计划执行 (Execution)**: 基于LangGraph的状态机，Agent循环调用工具、分析结果、更新状态，直到所有步骤完成。
4.  **报告生成 (Report Generation)**: 汇总执行过程中的关键发现，生成结构化的诊断报告。

---

## 2. SOP开发规范

### 2.1. SOP模板撰写详解

SOP模板是诊断逻辑的核心载体，其撰写质量直接决定了诊断的成败。

**1. 文件位置**: SOP模板应根据领域（如`instance`, `nc`）和类型（`diagnosis`）放置于 `src/deep_diagnose/prompts/sop/` 下的对应目录。

**2. 核心结构**: 每个步骤（Step）是SOP的最小执行单元，必须包含以下五个要素：

*   `目标 (Goal)`: **(必需)** 清晰定义该步骤要达成的目的。让Agent“知其然，知其所以然”。
*   `工具 (Tool)`: **(必需)** 明确指定要调用的工具名称，必须与代码中注册的工具名完全一致。
*   `输入参数 (Input Params)`: **(必需)** 定义调用工具所需的参数。若参数来源于前序步骤，需明确说明（如：`NC IP（从步骤1获取）`），Agent将自动进行上下文关联。
*   `分析要点 (Analysis Points)`: **(推荐)** 提供分析工具输出的指导原则或逻辑，帮助Agent更精准地解读结果，作出判断。
*   `关键输出 (Key Output)`: **(推荐)** 定义需要从工具输出中提取的核心信息，这些信息将是构成最终报告的关键部分。

### 2.2. `sop_diagnosis_config.yaml` 配置详解

撰写完SOP模板后，必须在 `sop_diagnosis_config.yaml` 中进行注册，才能被系统识别和调度。

*   `sop_id`: **(必需)** 全局唯一的SOP标识符。
*   `name`: **(必需)** 人类可读的SOP名称。
*   `scenario`: **(必需)** **路由匹配的关键**。应详细描述适用场景和识别规则，规则越明确，路由越精准。
*   `template_file_path`: **(必需)** 指向SOP模板文件的相对路径。
*   `example_user_queries`: **(必需)** 提供至少3个高质量的用户问题示例，用于路由模型的微调和测试。
*   `tool_dependencies`: **(必需)** 清晰列出该SOP依赖的所有工具，便于依赖管理和静态分析。

### 2.3. 撰写最佳实践

*   **单一职责原则**: 每个SOP应聚焦于一个明确的诊断场景。避免创建过于庞大、逻辑分支复杂的“万能SOP”。
*   **步骤分解要彻底**: 将复杂的诊断过程分解为一系列逻辑清晰、目标单一的原子步骤。
*   **参数传递要明确**: 清晰标注依赖前序步骤输出的参数，确保数据流在SOP内部能够顺畅传递。
*   **分析要点要具体**: “分析要点”应提供具体、可操作的指引，而非模糊的描述。例如，用“检查NC是否有宕机事件”代替“分析异常”。

---

## 3. SOP调试与验证

### 3.1. 本地调试技巧

*   **工具单元测试**: 在SOP联调前，确保SOP依赖的每个工具都经过了充分的单元测试，保证其功能的稳定和输出的确定性。
*   **本地端到端触发**: 在本地环境中，通过脚本或API调用，使用典型的`example_user_queries`触发完整的SOP流程。
*   **解读LangGraph日志**: **调试核心**。密切关注应用日志中关于LangGraph状态机流转的记录。检查每个节点的输入、输出，工具调用的参数和返回结果，以及状态（State）的变化是否符合预期。

### 3.2. 端到端验证策略

*   **构建高质量测试用例**: 除了`example_user_queries`，还应设计覆盖边界条件和潜在失败场景的测试用例。
*   **建立评估指标**: 
    *   **成功率 (Success Rate)**: SOP是否能顺利执行完毕。
    *   **工具调用准确率 (Tool Call Accuracy)**: 调用的工具、传入的参数是否完全正确。
    *   **结论准确性 (Conclusion Accuracy)**: 最终生成的诊断报告是否准确找到了根本原因。
*   **回归测试**: 每当修改或新增SOP时，都应运行全量回归测试用例，确保不影响现有功能。

---
