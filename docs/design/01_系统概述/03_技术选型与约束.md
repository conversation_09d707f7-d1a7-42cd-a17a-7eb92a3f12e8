# 技术选型与约束

## 1. 技术栈概览

### 1.1 后端技术栈
- **Python 3.12+** - 主要开发语言
- **FastAPI** - Web 框架，提供高性能 RESTful API
- **LangGraph** - 多智能体工作流引擎
- **Celery** - 异步任务处理
- **Redis** - 缓存与消息队列
- **PostgreSQL** - 主数据库（通过向量化扩展）
- **OSS** - 对象存储服务

### 1.2 前端技术栈
- **Next.js 14+** - React 全栈框架
- **TypeScript** - 类型安全的 JavaScript
- **Tailwind CSS** - 原子化 CSS 框架
- **React Hook Form** - 表单处理
- **Zustand** - 状态管理

### 1.3 开发工具
- **uv** - Python 包管理器
- **pnpm** - Node.js 包管理器
- **PyCharm/VSCode** - IDE 推荐
- **Docker** - 容器化部署

## 2. 版本要求

### 2.1 运行环境
```bash
# Python 环境
Python >= 3.12
uv >= 0.1.0

# Node.js 环境  
Node.js >= 20.0
pnpm >= 8.0

# 数据库
Redis >= 6.0
PostgreSQL >= 14.0 (with pgvector)
```

### 2.2 开发环境搭建
```bash
# 安装 Python 依赖管理器
brew install pipx
pipx install uv

# 安装 Node.js 包管理器
brew install pnpm

# 安装 Node 版本管理器（可选）
brew install nvm
nvm install 20
```

## 3. 技术选型原则

### 3.1 性能优先
- **异步处理**：所有 I/O 密集型操作均采用异步模式
- **并发支持**：支持高并发诊断任务处理
- **缓存策略**：多层缓存机制提升响应速度

### 3.2 可扩展性
- **微服务架构**：模块化设计，支持独立部署
- **插件化工具**：MCP 协议支持动态工具扩展
- **水平扩展**：支持多实例部署

### 3.3 开发效率
- **类型安全**：TypeScript + Python 类型注解
- **自动化测试**：完整的单元测试和集成测试
- **文档驱动**：API 文档自动生成

## 4. 技术约束

### 4.1 性能约束
- **响应时间**：API 响应时间 < 2s
- **并发处理**：支持 100+ 并发诊断任务
- **内存使用**：单实例内存使用 < 2GB

### 4.2 安全约束
- **认证机制**：基于 Token 的身份验证
- **权限控制**：集成 BUC 权限管理
- **数据加密**：敏感数据传输加密

### 4.3 兼容性约束
- **浏览器支持**：现代浏览器（Chrome 90+，Firefox 88+）
- **移动端适配**：响应式设计，支持移动设备
- **API 版本**：向后兼容的 API 版本策略

## 5. 部署架构

### 5.1 生产环境
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   前端应用   │────│   API网关   │────│   后端服务   │
└─────────────┘    └─────────────┘    └─────────────┘
                                              │
                                    ┌─────────────┐
                                    │  Celery集群  │
                                    └─────────────┘
                                              │
                     ┌─────────────┬──────────────┬─────────────┐
                     │    Redis    │ PostgreSQL   │     OSS     │
                     └─────────────┴──────────────┴─────────────┘
```

### 5.2 开发环境
- **本地开发**：Docker Compose 一键启动
- **测试环境**：Kubernetes 集群部署
- **CI/CD**：GitLab CI/CD 流水线

## 6. 已知限制

### 6.1 当前限制
- **大文件处理**：单次上传文件 < 100MB
- **诊断时长**：单次诊断任务 < 30分钟
- **并发限制**：单用户并发任务 < 5个

### 6.2 未来优化
- **流式处理**：支持大文件流式处理
- **分布式存储**：引入分布式文件系统
- **智能调度**：基于负载的智能任务调度