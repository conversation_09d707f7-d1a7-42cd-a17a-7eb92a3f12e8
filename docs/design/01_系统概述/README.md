# 01 系统概述

本目录包含 ECS 深度诊断系统的总体概述文档，帮助读者快速了解系统的背景、目标和整体架构。

## 📁 目录结构

- **[01_项目背景与目标.md](01_项目背景与目标.md)** - 项目背景、业务目标与价值主张
- **[02_系统架构总览.md](02_系统架构总览.md)** - 系统整体架构概览与核心设计理念  
- **[03_技术选型与约束.md](03_技术选型与约束.md)** - 技术栈选择、版本要求与技术约束

## 🎯 阅读建议

1. **新手入门**：建议按顺序阅读，从项目背景开始，逐步了解系统全貌
2. **架构师视角**：重点关注 `02_系统架构总览.md` 和 `03_技术选型与约束.md`
3. **产品经理视角**：重点关注 `01_项目背景与目标.md`

## 🔗 后续阅读

- 深入了解架构设计 → [02_架构设计](../02_架构设计/)
- 了解核心组件实现 → [03_核心组件](../03_核心组件/)