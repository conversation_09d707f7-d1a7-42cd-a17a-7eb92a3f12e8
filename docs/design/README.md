# ECS 深度诊断系统 - 设计文档 (V3)

## 1 概述

本文档是 **ECS 深度诊断系统** 的核心设计文档，基于实际代码实现编写，为开发者、架构师和运维人员提供系统设计的权威参考。

### 🎯 系统特色
- **多智能体协同**：基于 LangGraph 的智能体工作流
- **状态管理**：统一的 ReasoningState 状态容器
- **并发处理**：支持并行HTML报告生成
- **工具集成**：MCP协议动态工具扩展
- **SOP智能匹配**：向量化存储的标准作业程序

### 📖 阅读建议
- **新手入门**：01 系统概述 → 02 架构设计 → 03 核心组件
- **开发者视角**：03 核心组件 → 04 服务层设计 → 05 工具与集成  
- **运维视角**：06 数据与存储 → 07 API与接口 → 08 部署与运维

---

## 2 文档结构

### 📋 **01 系统概述**
系统背景、架构总览和技术选型
- [项目背景与目标](./01_系统概述/01_项目背景与目标.md)
- [系统架构总览](./01_系统概述/02_系统架构总览.md) 
- [技术选型与约束](./01_系统概述/03_技术选型与约束.md)

### 🏗️ **02 架构设计**  
核心架构设计与实现原理
- [三层架构设计](./02_架构设计/01_三层架构设计.md)
- [多智能体协同架构](./02_架构设计/02_多智能体协同架构.md)
- [工作流引擎设计](./02_架构设计/03_工作流引擎设计.md)
- [数据流与状态管理](./02_架构设计/04_数据流与状态管理.md)

### ⚙️ **03 核心组件**
核心组件的具体实现
- [智能体设计](./03_核心组件/01_智能体设计.md)
- [工作流执行引擎](./03_核心组件/02_工作流执行引擎.md)
- [事件驱动架构](./03_核心组件/03_事件驱动架构.md)

### 🔧 **04 服务层设计**
业务服务层的设计与实现（预留目录）

### 🛠️ **05 工具与集成** 
工具集成与扩展机制
- [MCP工具集成](./05_工具与集成/01_MCP工具集成.md)
- [SOP智能匹配](./05_工具与集成/02_SOP智能匹配.md)

### 💾 **06 数据与存储**
数据存储方案与缓存策略
- [Redis缓存架构](./06_数据与存储/02_Redis缓存架构.md)

### 🌐 **07 API与接口**
API接口设计与安全机制
- [RESTful API设计](./07_API与接口/01_RESTful_API设计.md) 
- [认证与权限](./07_API与接口/03_认证与权限.md)

### 🚀 **08 部署与运维**
部署配置与运维监控
- 环境配置
- 容器化部署  
- 监控与日志

---

## 3 核心技术栈

### 后端技术
- **Python 3.12+** - 主要开发语言
- **FastAPI** - Web框架
- **LangGraph** - 多智能体工作流引擎
- **Celery** - 异步任务处理
- **Redis** - 缓存与消息队列

### 前端技术
- **Next.js 14+** - React全栈框架
- **TypeScript** - 类型安全
- **Tailwind CSS** - 原子化CSS

### 开发工具
- **uv** - Python包管理器
- **pnpm** - Node.js包管理器
- **Docker** - 容器化部署

---

## 4 快速导航

### 🎯 按角色阅读
- **产品经理**：系统概述 → API接口
- **架构师**：架构设计 → 核心组件
- **开发工程师**：核心组件 → 服务层设计 → 工具集成
- **测试工程师**：API接口 → 部署运维
- **运维工程师**：数据存储 → 部署运维

### 🔍 按主题阅读
- **多智能体系统**：架构设计/多智能体协同架构 → 核心组件/智能体设计
- **工作流机制**：架构设计/工作流引擎设计 → 核心组件/工作流执行引擎
- **状态管理**：架构设计/数据流与状态管理 → 数据存储/Redis缓存架构
- **工具扩展**：工具集成/MCP工具集成 → 工具集成/SOP智能匹配
- **API开发**：API接口/RESTful API设计 → API接口/认证与权限

---

## 5 版本历史

- **V3.0** (当前) - 基于实际代码实现，简化目录结构，突出核心特性
- **V2.0** - 详细设计文档，分层架构描述
- **V1.0** - 初始设计文档

---

## 6 贡献指南

### 文档更新原则
1. **代码一致性**：文档必须与实际代码实现保持一致
2. **简洁明了**：遵循用户简洁性偏好，避免冗余内容
3. **实用导向**：重点关注实际应用和开发指导
4. **及时更新**：代码变更后及时更新相关文档

### 文档维护
- 新增功能时，同步更新设计文档
- 架构调整时，重新审视文档结构
- 定期检查文档与代码的一致性
- 根据用户反馈优化文档结构和内容