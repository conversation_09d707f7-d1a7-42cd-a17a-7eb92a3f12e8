# 诊断智能体与执行机制

## 目录
- [1 图表索引](#1-图表索引)
- [2 概述](#2-概述)
  - [2.1 核心职责](#21-核心职责)
  - [2.2 设计原则](#22-设计原则)
- [3 架构设计](#3-架构设计)
  - [3.1 整体架构图](#31-整体架构图)
  - [3.2 类层次结构](#32-类层次结构)
  - [3.3 核心组件](#33-核心组件)
    - [3.3.1 ResearcherAgent](#331-researcheragent)
    - [3.3.2 StepExecutor](#332-stepexecutor)
    - [3.3.3 StepContextBuilder](#333-stepcontextbuilder)
- [4 核心执行机制详解](#4-核心执行机制详解)
  - [4.1 Step执行案例分析](#41-step执行案例分析)
    - [4.1.1 案例Step定义](#411-案例step定义)
    - [4.1.2 执行前的State状态](#412-执行前的state状态)
  - [4.2 Prompt构建详细机制](#42-prompt构建详细机制)
    - [4.2.1 StepContextBuilder.build_input()执行流程](#421-stepcontextbuilderbuildinput执行流程)
    - [4.2.2 历史上下文构建](#422-历史上下文构建)
  - [4.3 Researcher Agent Prompt模板](#43-researcher-agent-prompt模板)
    - [4.3.1 角色定义](#431-角色定义)
    - [4.3.2 指导原则](#432-指导原则)
    - [4.3.3 工作流程](#433-工作流程)
  - [4.4 工具集成机制](#44-工具集成机制)
    - [4.4.1 MCP集成架构图](#441-mcp集成架构图)
    - [4.4.2 MCP工具设置](#442-mcp工具设置)
    - [4.4.3 Agent创建与并发工具调用](#443-agent创建与并发工具调用)
- [5 执行流程详解](#5-执行流程详解)
  - [5.1 执行流程图](#51-执行流程图)
  - [5.2 完整执行流程](#52-完整执行流程)
  - [5.3 状态更新机制](#53-状态更新机制)
- [6 错误处理与恢复](#6-错误处理与恢复)
  - [6.1 错误处理策略](#61-错误处理策略)
  - [6.2 执行条件验证](#62-执行条件验证)
- [7 性能优化](#7-性能优化)
  - [7.1 并发工具调用](#71-并发工具调用)
  - [7.2 递归限制配置](#72-递归限制配置)
- [8 总结](#8-总结)


## 1 图表索引

本文档包含以下关键架构图表，建议结合图表阅读：

- **图5-1**: [诊断智能体详细架构图](../../assets/png/05_researcher_agent_detailed_architecture.png) - 展示完整的组件架构和核心方法
- **图5-2**: [MCP工具集成架构图](../../assets/png/06_researcher_mcp_integration.png) - 展示MCP工具集成的四层架构
- **图5-3**: [执行流程图](../../assets/png/07_researcher_execution_flow.png) - 展示优化布局的完整执行流程

## 2 概述

诊断智能体（ResearcherAgent）是深度诊断系统中的核心执行组件，负责执行具体的系统诊断任务。它通过与MCP工具集成，能够执行复杂的诊断命令、分析系统状态，并生成结构化的诊断报告。

### 2.1 核心职责

- **任务执行**：执行规划器生成的研究步骤
- **工具调用**：动态调用MCP工具进行系统诊断
- **结果分析**：分析工具输出并生成结构化报告
- **状态维护**：维护执行状态和观察结果

### 2.2 设计原则

1. **单一职责**：专注于研究任务的执行
2. **工具驱动**：通过MCP工具实现具体功能
3. **状态一致性**：确保执行状态的准确维护
4. **错误恢复**：具备完善的错误处理机制

## 3 架构设计

### 3.1 整体架构图

![诊断智能体详细架构](../../assets/png/05_researcher_agent_detailed_architecture.png)

<div align="center"><em>图5-1: 诊断智能体详细架构图</em></div>

上图展示了诊断智能体的完整架构设计，包括：

- **关键设计优点**：单一职责原则、组合优于继承、依赖注入、策略模式、模板方法、错误恢复
- **核心组件层次**：BaseAgent → ResearcherAgent → StepExecutor & StepContextBuilder
- **核心方法高亮**：`_do_execute()`, `execute_current_step()`, `build_input()` 三个最关键的方法
- **组件关系**：清晰的组合关系和依赖关系
- **工具函数支撑**：Utils层提供基础设施支撑

### 3.2 类层次结构

```python
BaseAgent (抽象基类)
    ├── ResearcherAgent (诊断智能体)
    │   ├── StepExecutor (步骤执行器)
    │   └── StepContextBuilder (上下文构建器)
```

### 3.3 核心组件

#### 3.3.1 ResearcherAgent

**职责**：诊断智能体的主控制器

**关键属性**：
- `name`: "researcher"
- `agent_type`: "researcher"  
- `step_executor`: StepExecutor实例
- `logger`: 日志记录器

**核心方法**：
```python
async def _do_execute(self, state: State, config: RunnableConfig) -> Command:
    """执行研究任务的核心逻辑"""

async def _validate_execution_conditions(self, state: State) -> bool:
    """验证执行条件"""

def _handle_error(self, error: Exception, state: State) -> Command:
    """错误处理"""
```

**实际代码实现**：
```python
class ResearcherAgent(BaseAgent):
    """诊断Agent - 系统诊断的核心执行者"""

    def __init__(self):
        super().__init__("researcher", "researcher")
        self.step_executor = StepExecutor(self)

    async def _do_execute(self, state: State, config: Optional[RunnableConfig] = None) -> Command:
        """执行研究任务的核心逻辑"""

        # 验证执行条件
        if not self._validate_execution_conditions(state):
            self.logger.warning("Execution conditions not met")
            return Command(goto="research_team")

        # 执行当前步骤
        return await self.step_executor.execute_current_step(state, config)

    def _validate_execution_conditions(self, state: State) -> bool:
        """验证是否具备执行条件"""
        current_plan = state.get("current_plan")

        # 检查是否有有效的计划
        if not current_plan:
            self.logger.error("No current plan found")
            return False

        # 检查是否有待执行的步骤
        if not hasattr(current_plan, 'steps') or not current_plan.steps:
            self.logger.error("No steps found in current plan")
            return False

        # 检查是否有未完成的研究步骤
        unfinished_steps = [step for step in current_plan.steps if not step.execution_res]
        if not unfinished_steps:
            self.logger.info("All research steps completed")
            return False

        return True
```

#### 3.3.2 StepExecutor

**职责**：步骤执行器，负责执行计划中的具体步骤

**核心方法**：
```python
async def execute_current_step(self, state: State, config: RunnableConfig) -> Command:
    """执行当前步骤"""

def _find_current_step(self, state: State):
    """找到当前需要执行的步骤"""

async def _prepare_tools(self, config: RunnableConfig):
    """准备工具"""

def _prepare_input(self, state: State, current_step):
    """准备执行输入"""
```

**实际代码实现**：
```python
class StepExecutor:
    """步骤执行器 - 负责执行计划中的具体步骤"""

    def __init__(self, agent_instance):
        self.agent = agent_instance
        self.logger = agent_instance.logger

    async def execute_current_step(self, state: State, config: RunnableConfig) -> Command:
        """执行当前步骤"""

        # 1. 找到当前步骤
        current_step = self._find_current_step(state)
        if not current_step:
            self.logger.info("No unexecuted step found")
            return Command(goto="research_team")

        self.logger.info(f"Executing step: {current_step.title}")

        # 2. 准备工具和创建执行器
        tools = await self._prepare_tools(config)
        executor = await create_agent_with_tools(self.agent.agent_type, tools, config)

        # 3. 准备输入并执行
        agent_input = self._prepare_input(state, current_step)
        result = await executor.ainvoke(
            input=agent_input, 
            config={"recursion_limit": get_recursion_limit()}
        )

        # 4. 更新结果
        response_content = result["messages"][-1].content
        current_step.execution_res = response_content

        self.logger.info(f"Step '{current_step.title}' completed")

        return Command(
            update={
                "messages": [HumanMessage(content=response_content, name=self.agent.agent_type)],
                "observations": state.get("observations", []) + [response_content],
            },
            goto="research_team",
        )
```

#### 3.3.3 StepContextBuilder

**职责**：步骤上下文构建器，负责构建清晰的执行上下文

**核心方法**：
```python
def build_input(self, agent_type: str) -> dict:
    """构建agent输入"""

def _build_history_context(self) -> str:
    """构建历史步骤上下文"""

def _build_current_task(self) -> str:
    """构建当前任务描述"""

def _build_agent_instruction(self, agent_type: str) -> Optional[str]:
    """构建特定agent的指令"""
```

**实际代码实现**：
```python
class StepContextBuilder:
    """步骤上下文构建器 - 负责构建清晰的执行上下文"""

    def __init__(self, state: State, current_step):
        self.state = state
        self.current_step = current_step
        self.current_plan = state.get("current_plan")

    def build_input(self, agent_type: str) -> dict:
        """构建agent输入"""
        messages = []

        # 添加历史步骤上下文
        history_context = self._build_history_context()
        if history_context:
            messages.append(HumanMessage(content=history_context))

        # 添加当前任务
        current_task = self._build_current_task()
        messages.append(HumanMessage(content=current_task))

        # 添加特定agent的指令
        agent_instruction = self._build_agent_instruction(agent_type)
        if agent_instruction:
            messages.append(HumanMessage(content=agent_instruction, name="system"))

        return {"messages": messages}

    def _build_history_context(self) -> str:
        """构建历史步骤上下文"""
        completed_steps = [
            step for step in self.current_plan.steps 
            if step.execution_res and step != self.current_step
        ]

        if not completed_steps:
            return ""

        context_parts = ["# 前置诊断步骤\n"]

        for i, step in enumerate(completed_steps, 1):
            context_parts.extend([
                f"## 步骤 {i}: {step.title}\n",
                f"**执行结果:**\n",
                f"```\n{step.execution_res}\n```\n"
            ])

        return "\n".join(context_parts)

    def _build_current_task(self) -> str:
        """构建当前任务描述"""
        return f"""# 当前诊断任务

## 任务标题
{self.current_step.title}

## 任务描述  
{self.current_step.description}

## 执行要求
请根据任务描述执行相应的诊断操作，并提供详细的分析结果。"""

    def _build_agent_instruction(self, agent_type: str) -> Optional[str]:
        """构建特定agent的指令"""
        instructions = {
            "researcher": """**重要提示:**
- 当工具执行返回的data字段为空时，表示"未查询到相关数据"
- 严禁编造或虚构任何信息
- 确保回应完全基于工具的实际输出
- 请用中文回答""",

            "coder": """**代码分析要求:**
- 仔细分析代码逻辑和潜在问题
- 提供具体的修复建议
- 如需执行代码，请确保安全性"""
        }

        return instructions.get(agent_type)
```

## 4 核心执行机制详解

### 4.1 Step执行案例分析

#### 4.1.1 案例Step定义

```python
step = Step(
    title="查询实例物理机历史",
    description="使用listVmHostHistory工具查询实例[i-t4n4vky24zw2w1qnqoyf, i-t4n74bsfzx58x0lj4qbh, i-t4na3cc0c9mimcw9667x, i-j6ch2zf4qfy1rltbql6r, i-2vc5alcmxz75rw8aol4g, i-2vc0zliaw8ilg744cdrq, i-2vc6qv34j96hkmcwms5d, i-2vcht9nfld3wrbv28qbs, i-j6c8465htk5rsyb13128, i-j6ch2zf4qfy1rltbql6p, i-j6ccxnd10yb809j28e8o, i-2vcglxct8xop32erzxgt]在2025-06-26 01:00:00至2025-06-26 04:00:00所在的物理机（NC），分析是否存在物理机层面的聚集性故障",
    step_type=StepType.RESEARCH,
    need_web_search=False,
    execution_res=None
)
```

#### 4.1.2 执行前的State状态

```python
state = {
    "messages": [
        HumanMessage(content="用户报告多个ECS实例在凌晨1-4点出现异常")
    ],
    "observations": [
        "发现12个实例在相同时间段出现问题",
        "问题集中在2025-06-26 01:00:00至04:00:00时间段"
    ],
    "current_plan": Plan(
        title="ECS实例聚集性故障诊断",
        steps=[
            # 前面已完成的步骤...
            step  # 当前要执行的步骤
        ]
    ),
    "mcp_servers_description": "## Available Tools\n- listVmHostHistory: 查询虚拟机宿主机历史信息..."
}
```

### 4.2 Prompt构建详细机制

#### 4.2.1 StepContextBuilder.build_input()执行流程

```python
def build_input(self, agent_type: str) -> dict:
    """构建完整的执行上下文"""
    messages = []

    # 1. 构建历史上下文
    history_context = self._build_history_context()
    if history_context:
        messages.append(HumanMessage(content=history_context))

    # 2. 构建当前任务
    current_task = self._build_current_task()
    messages.append(HumanMessage(content=current_task))

    # 3. 添加agent特定指令
    agent_instruction = self._build_agent_instruction(agent_type)
    if agent_instruction:
        messages.append(HumanMessage(content=agent_instruction, name="system"))

    return {"messages": messages}
```

#### 4.2.2 历史上下文构建

```python
def _build_history_context(self) -> str:
    """构建历史步骤上下文"""
    completed_steps = [
        step for step in self.current_plan.steps 
        if step.execution_res and step != self.current_step
    ]

    if not completed_steps:
        return ""

    context_parts = ["# 前置诊断步骤\n"]

    for i, step in enumerate(completed_steps, 1):
        context_parts.extend([
            f"## 步骤 {i}: {step.title}\n",
            f"**执行结果:**\n",
            f"```\n{step.execution_res}\n```\n"
        ])

    return "\n".join(context_parts)
```

#### 4.2.3 当前任务构建

```python
def _build_current_task(self) -> str:
    """构建当前任务描述"""
    return f"""# 当前诊断任务

## 任务标题
{self.current_step.title}

## 任务描述  
{self.current_step.description}

## 执行要求
请根据任务描述执行相应的诊断操作，并提供详细的分析结果。"""
```

### 4.3 Researcher Agent Prompt模板

诊断智能体使用的完整prompt模板位于 `src/deep_diagnose/prompts/researcher.md`，包含以下核心部分：

#### 4.3.1 角色定义
```markdown
## **角色**
您是一位顶级的阿里云ECS（弹性计算服务）故障诊断专家。您的核心任务是利用提供的工具集，以系统化的方式精准、高效地诊断并解决用户提交的ECS工单问题。
```

#### 4.3.2 指导原则
```markdown
## **指导原则**

在执行任务时，您必须始终遵循以下核心原则：

1. **系统性思维**：严格遵循定义的工作流程，从信息收集、分析、判断到结论，层层递进，避免跳跃性思维或过早下结论。
2. **数据驱动**：您的每一个判断、发现和结论都必须有明确的工具输出结果作为依据。在报告中，必须清晰地指明信息来源。
3. **效率优先**：**在收集信息阶段，如果多个信息点可以并行获取，应优先选择并发调用工具，以最快速度完成数据收集。**
4. **逻辑清晰**：确保整个诊断过程的逻辑链条完整且易于理解，能够清晰地展示从问题现象到根本原因的推导路径。
5. **闭环响应**：最终的解决方案必须完整回答用户提出的所有问题，并提供明确、可执行的后续步骤或建议。
```

#### 4.3.3 工作流程

**情况一：当前无 `tool_output` (通常是首次处理用户问题 或 需要调用新工具时)**

```markdown
**你的核心目标：分析用户问题，精确选择一个或多个工具，并生成 `tool_calls` JSON列表。**

* **输入**：用户原始问题描述，或上一轮分析后的中间结论。
* **核心任务**：
    1. **意图理解**：精确解析用户输入，识别关键实体（如实例ID、NC IP）、问题现象（如卡顿、宕机、无法连接）、时间范围等核心信息。
    2. **工具选择**：从`可用工具集` 中，选择所有必要且合适的工具。为了最大化诊断效率，如果多个工具的可以同时执行，请并发执行这些工具。
* **输出要求**：
    * **必须且仅能输出符合规范的、包含一个或多个 `tool_call` 对象的JSON列表（array/list）。**
```

**情况二：当前已接收到 `tool_output` (工具已执行并返回结果)**

```markdown
**你的核心目标：分析 `tool_output`，整合信息，并按照"输出规范"生成全面的总结报告。**

1. **结果解析与验证**:
    * 仔细审查 `tool_output` 的内容。<u>**请注意，`tool_output` 可能是包含多个工具结果的列表。**</u>
    * **错误处理**:
        * 如果 `tool_output` 指示工具执行错误，请理解错误信息。
        * 基于错误，你可能需要：
            * **调整参数并建议重新调用同一工具** (这将触发一次新的、目标为 `tool_call` 的交互，即回到"情况一"的逻辑)。
            * **建议调用一个不同的工具** (同样会触发一次新的、目标为 `tool_call` 的交互)。
            * 如果在当前 `tool_output` 中信息不足以做出进一步判断，请在总结中说明。
    * **时效性检查** (若任务指定时间范围): 确保 `tool_output` 中的信息符合指定的时间限制。
2. **信息整合与溯源**:
    * 整合当前 `tool_output` 以及（如果适用）来自先前步骤中其他工具调用的信息。
    * **至关重要：所有关键信息、数据点和判断，都必须有明确的 `tool_output` 作为依据，并清晰注明来源** (例如，"根据工具A返回的`tool_output`中的`items`字段...")。
3. **质量核查与报告生成**:
    * 验证所收集信息的关联性、准确性和完整性。
    * 确保最终响应清晰、简洁，并直接、全面地解答用户问题，严格遵循下面的"输出规范"。
```

### 4.4 工具集成机制

#### 4.4.1 MCP集成架构图

![诊断智能体MCP集成](../../assets/png/06_researcher_mcp_integration.png)

<div align="center"><em>图5-2: MCP工具集成架构图</em></div>

上图展示了诊断智能体与MCP工具的完整集成架构，包含四个核心层次：

**配置层 (Configuration Layer)**：
- `mcp_settings`: MCP服务器配置管理
- `enabled_tools`: 启用的工具列表控制
- `add_to_agents`: Agent分配配置
- `transport/url/headers`: 连接配置参数

**MCP工具设置层 (MCP Tools Setup)**：
- `setup_mcp_tools()`: 收集服务器配置并过滤agent工具
- `MultiServerMCPClient`: 创建和管理多服务器连接
- `get_tools()`: 动态工具发现和获取
- `filter_tools()`: 按enabled_tools过滤并添加服务器标识

**Agent创建层 (Agent Creation)**：
- `get_llm_by_type()`: 获取对应的LLM实例
- `parallel_tool_calls`: 启用并发工具调用优化
- `generate_prompt()`: 动态生成包含MCP工具描述的prompt
- `create_react_agent()`: 创建绑定LLM和工具的ReAct Agent

**工具描述生成层 (Tool Description Generation)**：
- `McpToolsService.fetch_mcp_tools()`: 获取所有可用工具
- `filter by enabled_tools`: 按配置过滤工具确保可用性
- `convert_to_openai_tool`: 转换工具格式并生成JSON Schema
- `format description`: 格式化工具描述包含输入Schema
- `## Available Tools`: 生成最终的工具文档供prompt使用

**关键特性**：
- **动态工具发现和过滤**: 支持运行时工具配置
- **多种传输协议支持**: stdio和http传输协议
- **并发工具调用优化**: 提高诊断执行效率
- **配置灵活性**: 按Agent分配工具，启用/禁用特定工具
- **智能集成**: 自动生成工具文档和动态prompt构建
- **性能优化**: 工具缓存、连接池管理、递归限制控制

#### 4.4.2 MCP工具设置

```python
async def setup_mcp_tools(agent_type: str, config: RunnableConfig) -> List:
    """为指定agent设置MCP工具 - 纯工具函数"""
    configurable = Configuration.from_runnable_config(config)

    if not configurable.mcp_settings:
        return []

    mcp_servers = {}
    enabled_tools = {}

    # 收集MCP服务器配置
    for server_name, server_config in configurable.mcp_settings["servers"].items():
        if (
            server_config["enabled_tools"] and
            agent_type in server_config["add_to_agents"]):

            mcp_servers[server_name] = {
                k: v for k, v in server_config.items()
                if k in ("transport", "command", "args", "url", "env", "headers")
            }

            for tool_name in server_config["enabled_tools"]:
                enabled_tools[tool_name] = server_name

    if not mcp_servers:
        return []

    # 获取并过滤工具
    client = MultiServerMCPClient(mcp_servers)
    tools = await client.get_enabled_tools()

    filtered_tools = []
    for tool in tools:
        if tool.name in enabled_tools:
            tool.description = f"Powered by '{enabled_tools[tool.name]}'.\n{tool.description}"
            filtered_tools.append(tool)

    logger.info(f"Loaded {len(filtered_tools)} MCP tools for {agent_type}")
    return filtered_tools
```

#### 4.4.3 Agent创建与并发工具调用

```python
async def create_agent_with_tools(agent_type: str, tools: List, config: RunnableConfig):
    """创建带工具的agent - 支持并发工具调用"""
    from langgraph.prebuilt import create_react_agent

    configurable = Configuration.from_runnable_config(config)

    async def generate_prompt(agent_state):
        agent_state["mcp_servers_description"] = await get_tools_description(
            configurable.mcp_settings
        )
        return apply_prompt_template(agent_type, agent_state)

    # 获取 LLM 并启用并发工具调用
    llm = get_llm_by_type(AGENT_LLM_MAP[agent_type])

    # 确保 LLM 支持并发工具调用
    if hasattr(llm, 'parallel_tool_calls'):
        llm.parallel_tool_calls = True
    elif hasattr(llm, 'bind'):
        # 使用 bind 方法启用并发工具调用
        llm = llm.bind(parallel_tool_calls=True)

    return create_react_agent(
        name=agent_type,
        model=llm,
        tools=tools,
        prompt=generate_prompt,
    )
```

## 5 执行流程详解

### 5.1 执行流程图

![诊断智能体执行流程](../../assets/png/07_researcher_execution_flow.png)

<div align="center"><em>图5-3: 诊断智能体执行流程图</em></div>

上图展示了诊断智能体的完整执行流程，采用优化的横向布局设计：

**主要执行路径**：
- **第一行**：入口验证（开始 → 验证条件 → StepExecutor入口）
- **第二行**：步骤查找（找到步骤 → 步骤判断）
- **第三行**：工具准备（准备工具 → 构建上下文）
- **第四行**：执行处理（创建Agent → 执行获取结果）
- **第五行**：结果处理（更新状态并返回）

**关键特性**：
- **错误处理路径**：条件不满足和未找到步骤时的统一错误处理
- **StepContextBuilder详细流程**：展示上下文构建的内部机制
- **性能优化**：并发工具调用、缓存机制、递归限制等
- **数据流转**：从State到Command的完整数据流

### 5.2 完整执行流程

1. **ResearcherAgent.execute()** 被调用
2. **验证执行条件** - `_validate_execution_conditions()`
3. **StepExecutor.execute_current_step()** 执行步骤
4. **找到当前步骤** - `_find_current_step()`
5. **准备工具** - `_prepare_tools()`
6. **构建输入上下文** - `StepContextBuilder.build_input()`
7. **创建Agent执行器** - `create_agent_with_tools()`
8. **执行并获取结果**
9. **更新状态并返回Command**

### 5.3 状态更新机制

执行完成后，系统会更新State并返回Command：

```python
return Command(
    update={
        "messages": [HumanMessage(content=response_content, name=self.agent.agent_type)],
        "observations": state.get("observations", []) + [response_content],
    },
    goto="research_team",
)
```

**状态更新包含**：
- **messages**: 添加执行结果到消息历史
- **observations**: 累积观察结果供后续步骤使用
- **goto**: 指定下一个执行节点（通常返回research_team进行下一轮分配）

## 6 错误处理与恢复

### 6.1 错误处理策略

```python
def _handle_error(self, error: Exception, state: State) -> Command:
    """诊断特定的错误处理"""
    self.logger.error(f"Research execution failed: {error}")
    # 研究失败时返回研究团队重新分配
    return Command(goto="research_team")
```

### 6.2 执行条件验证

```python
def _validate_execution_conditions(self, state: State) -> bool:
    """验证是否具备执行条件"""
    current_plan = state.get("current_plan")

    # 检查是否有有效的计划
    if not current_plan:
        self.logger.error("No current plan found")
        return False

    # 检查是否有待执行的步骤
    if not hasattr(current_plan, 'steps') or not current_plan.steps:
        self.logger.error("No steps found in current plan")
        return False

    # 检查是否有未完成的研究步骤
    unfinished_steps = [step for step in current_plan.steps if not step.execution_res]
    if not unfinished_steps:
        self.logger.info("All research steps completed")
        return False

    return True
```

## 7 性能优化

### 7.1 并发工具调用

系统支持并发工具调用以提高执行效率：

```python
# 确保 LLM 支持并发工具调用
if hasattr(llm, 'parallel_tool_calls'):
    llm.parallel_tool_calls = True
elif hasattr(llm, 'bind'):
    # 使用 bind 方法启用并发工具调用
    llm = llm.bind(parallel_tool_calls=True)
```

### 7.2 递归限制配置

```python
def get_recursion_limit() -> int:
    """获取递归限制配置 - 纯工具函数"""
    default_limit = DEFAULT_MAX_RECURSIVE_NUM

    try:
        env_value = os.getenv("AGENT_RECURSION_LIMIT", str(default_limit))
        limit = int(env_value)
        return limit if limit > 0 else default_limit
    except ValueError:
        logger.warning(f"Invalid AGENT_RECURSION_LIMIT, using default: {default_limit}")
        return default_limit
```

## 8 总结

诊断智能体通过精心设计的架构和执行机制，实现了：

1. **高效的步骤执行**：通过StepExecutor实现步骤的有序执行
2. **智能的上下文构建**：通过StepContextBuilder提供完整的执行上下文
3. **灵活的工具集成**：通过MCP工具实现动态功能扩展
4. **完善的错误处理**：确保系统的稳定性和可靠性
5. **并发工具调用**：提高诊断效率
6. **结构化的输出**：生成规范的诊断报告

该设计确保了系统能够高效、准确地执行各种复杂的诊断任务，为用户提供专业的技术支持服务。
