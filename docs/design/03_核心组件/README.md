# 03 核心组件

本目录详细介绍 ECS 深度诊断系统的核心组件实现，包括智能体设计、工作流引擎、事件驱动架构等关键技术实现。

## 📁 目录结构

- **[01_智能体设计.md](01_智能体设计.md)** - 智能体架构设计与实现细节
- **[02_工作流执行引擎.md](02_工作流执行引擎.md)** - 基于 LangGraph 的工作流引擎
- **[03_事件驱动架构.md](03_事件驱动架构.md)** - 异步事件处理与消息传递机制
- **[04_推理状态管理.md](04_推理状态管理.md)** - 分布式状态管理与持久化

## 🔧 核心特性

### ⚡ 高性能执行引擎
- **异步处理**：全异步架构，支持高并发任务处理
- **智能调度**：基于负载和能力的智能任务调度
- **状态持久化**：关键状态数据实时持久化

### 🧠 智能决策系统
- **多智能体协同**：专业化智能体分工协作
- **动态路径选择**：根据任务类型智能选择执行路径
- **自适应优化**：基于历史执行数据持续优化

### 🔄 事件驱动架构
- **松耦合设计**：组件间通过事件解耦
- **实时响应**：事件驱动的实时状态更新
- **可扩展性**：支持动态添加事件处理器

## 🏗️ 技术实现

### LangGraph 工作流引擎
```python
# 工作流定义示例
workflow = StateGraph(DiagnosisState)
workflow.add_node("planner", planner_agent)
workflow.add_node("diagnosis", diagnosis_agent)
workflow.add_node("research", research_agent)
workflow.add_edge("planner", "diagnosis")
workflow.add_conditional_edges("diagnosis", route_next_step)
```

### Redis 事件流
```python
# 事件发布订阅
await redis_client.xadd(
    "diagnosis_events",
    {
        "event_type": "task_completed",
        "task_id": task_id,
        "result": json.dumps(result)
    }
)
```

### 状态管理
```python
# 分布式状态同步
state_manager = StateManager(redis_client)
await state_manager.update_state(task_id, {
    "current_step": "analysis",
    "progress": 0.6,
    "intermediate_results": results
})
```

## 🎯 设计原则

### 1. 可观测性
- **全链路追踪**：完整的执行链路跟踪
- **性能监控**：关键性能指标实时监控
- **日志聚合**：结构化日志与错误追踪

### 2. 容错能力
- **优雅降级**：组件故障时的优雅降级策略
- **自动恢复**：故障检测与自动恢复机制
- **状态一致性**：分布式状态的最终一致性保证

### 3. 可扩展性
- **水平扩展**：支持多实例部署与负载均衡
- **插件化架构**：支持动态插件加载与卸载
- **版本兼容**：向后兼容的API版本管理

## 🔗 相关章节

- 了解架构设计原理 → [02_架构设计](../02_架构设计/)
- 查看服务层实现 → [04_服务层设计](../04_服务层设计/)
- 了解数据存储方案 → [06_数据与存储](../06_数据与存储/)