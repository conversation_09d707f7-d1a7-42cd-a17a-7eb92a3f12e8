# 05 工具与集成

本目录介绍 ECS 深度诊断系统的工具集成机制，包括 MCP 协议工具集成和 SOP 智能匹配系统。

## 📁 目录结构

- **[MCP工具集成](01_MCP工具集成.md)** - Model Context Protocol 工具集成方案
- **[SOP智能匹配](02_SOP智能匹配.md)** - 标准作业程序的智能匹配与向量化存储

## 🛠️ 集成特色

### 🔌 MCP工具集成
- **动态发现**：自动发现和注册MCP工具
- **统一接口**：标准化的工具调用接口
- **热插拔**：支持运行时添加/移除工具
- **连接管理**：自动连接管理与故障恢复

### 🧠 SOP智能匹配
- **向量化搜索**：基于语义相似度的SOP匹配
- **智能推荐**：根据问题特征推荐最佳SOP
- **版本管理**：SOP版本控制与更新机制
- **缓存优化**：高效的匹配结果缓存

## 🔗 相关章节

- 了解架构设计 → [02_架构设计](../02_架构设计/)
- 查看核心组件实现 → [03_核心组件](../03_核心组件/)
- 了解数据存储方案 → [06_数据与存储](../06_数据与存储/)