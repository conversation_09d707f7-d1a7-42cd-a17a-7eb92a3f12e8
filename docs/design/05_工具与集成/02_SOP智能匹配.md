# SOP智能匹配与向量化存储

## 🎯 概述

SOP（标准作业程序）智能匹配系统是 ECS 深度诊断系统的核心组件之一，采用先进的向量化技术和多策略选择机制，实现精准的 SOP 推荐和匹配。

### ✨ 核心特性
- **🧠 智能匹配**：基于语义理解的精准SOP推荐
- **🔄 多策略支持**：LLM策略与相似度策略双重保障
- **📊 向量化存储**：高效的向量检索与相似度计算
- **🏗️ 模块化设计**：可扩展的存储层与策略层架构
- **⚙️ 配置驱动**：灵活的配置管理与动态组装

### 🎯 设计目标
- **高精度匹配**：通过语义向量实现精准的SOP匹配
- **高可扩展性**：模块化架构支持多种存储和策略组合
- **高性能**：优化的向量检索算法确保快速响应
- **易维护性**：清晰的分层设计便于功能迭代

---

## 🏗️ 系统架构

系统采用 **策略模式** 和 **工厂模式**，由核心的 `SOPService`、抽象的 `SOPStore` 存储层和 `SOPSelectionStrategy` 策略层构成。

### 📊 架构总览

```mermaid
graph TB
    subgraph "📱 应用层"
        A["🤖 Planner Agent<br/>规划智能体"]
    end

    subgraph "🏗️ SOP 服务层"
        B["🔄 SOPService<br/>核心服务"]
        C["🗺️ SOPStore<br/>存储抽象"]
        D["🎯 SOPSelectionStrategy<br/>选择策略"]
    end

    subgraph "💾 存储实现层"
        C1["📁 FileSystemSOPStore<br/>文件系统存储"]
        C2["📊 PGVectorSOPStore<br/>向量化存储"]
    end

    subgraph "🧠 策略实现层"
        D1["🤖 LLMSelectionStrategy<br/>大模型策略"]
        D2["🔍 SimilaritySelectionStrategy<br/>相似度策略"]
    end
    
    subgraph "⚙️ 依赖服务"
        E["🌐 DashScope Embedding<br/>向量化服务"]
        F["🤖 LLM 服务<br/>大语言模型"]
        G["🗺️ PostgreSQL<br/>向量数据库"]
        H["📁 File System<br/>文件系统"]
    end

    A -->|"find_sop(query)"| B
    B -->|"组合"| C
    B -->|"组合"| D

    C -.->|"实现"| C1
    C -.->|"实现"| C2
    
    D -.->|"实现"| D1
    D -.->|"实现"| D2

    C1 -->|"读取YAML/MD"| H
    C2 -->|"向量检索"| G
    
    D1 -->|"调用LLM"| F
    D2 -->|"计算相似度"| E
    
    E -->|"生成向量"| dashscope["🌐 DashScope API"]
    C2 -->|"存储向量"| E

    %% 现代化配色方案
    classDef appLayer fill:#e1f5fe,stroke:#0288d1,stroke-width:3px,color:#01579b
    classDef serviceLayer fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px,color:#4a148c
    classDef storageLayer fill:#e8f5e8,stroke:#388e3c,stroke-width:3px,color:#1b5e20
    classDef strategyLayer fill:#fff3e0,stroke:#f57c00,stroke-width:3px,color:#e65100
    classDef dependencyLayer fill:#fce4ec,stroke:#c2185b,stroke-width:3px,color:#880e4f
    
    class A appLayer
    class B,C,D serviceLayer
    class C1,C2 storageLayer
    class D1,D2 strategyLayer
    class E,F,G,H,dashscope dependencyLayer
```

### 🔄 初始化流程

```mermaid
flowchart TD
    A["🚀 启动服务<br/>PlannerAgent 请求 SOPService"] --> B["🏠 工厂创建<br/>调用 create_sop_service"]
    B --> C{"📝 读取配置<br/>服务配置文件"}
    
    C -->|"storage_type: filesystem"| D["📁 文件存储<br/>初始化 FileSystemSOPStore"]
    C -->|"storage_type: pgvector"| E["📊 向量存储<br/>初始化 PGVectorSOPStore"]
    
    C -->|"strategy_type: llm"| F["🤖 LLM策略<br/>初始化 LLMSelectionStrategy"]
    C -->|"strategy_type: similarity"| G["🔍 相似度策略<br/>初始化 SimilaritySelectionStrategy"]
    
    subgraph "🔗 依赖注入"
        G --> H["🌐 Embedding 服务<br/>DashScopeEmbeddingService"]
    end

    D --> I{"🗺️ SOPStore<br/>存储层"}
    E --> I
    F --> J{"🎯 选择策略<br/>SOPSelectionStrategy"}
    G --> J
    
    I --> K["🔧 服务组装<br/>创建 SOPService 实例"]
    J --> K

    K --> L["✅ 服务就绪<br/>返回 SOPService 实例"]
    
    %% 配色方案
    classDef startNode fill:#e1f5fe,stroke:#0288d1,stroke-width:3px,color:#01579b
    classDef processNode fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px,color:#4a148c
    classDef decisionNode fill:#fff3e0,stroke:#f57c00,stroke-width:3px,color:#e65100
    classDef storageNode fill:#e8f5e8,stroke:#388e3c,stroke-width:3px,color:#1b5e20
    classDef endNode fill:#fce4ec,stroke:#c2185b,stroke-width:3px,color:#880e4f
    
    class A startNode
    class B,K processNode
    class C decisionNode
    class D,E,F,G,H,I,J storageNode
    class L endNode
```
```

## ⚙️ 核心组件

### 🔄 SOPService - 核心服务

`SOPService` 是 SOP 功能的核心入口，采用组合模式聚合存储和策略实例，实现灵活的 SOP 查询和选择。

**📁 文件位置**：`src/deep_diagnose/core/reasoning/planning/service.py`

**🔧 核心方法**：
- `__init__(self, store: SOPStore, strategy: SOPSelectionStrategy)` - 依赖注入，接收存储和策略实例
- `find_sop(self, query: str) -> SelectedSOP` - 主要业务方法，根据查询找到最合适的 SOP

**⚡ 执行流程**：
1. 从 `store` 获取所有可用 SOP
2. 交由 `strategy` 进行智能选择
3. 返回选择结果和原因说明

### 💾 SOPStore - 存储抽象层

`SOPStore` 定义了 SOP 数据访问的统一接口，支持多种存储后端实现。

**📁 文件位置**：`src/deep_diagnose/core/reasoning/planning/storage.py`

**🔧 核心接口**：
- `get_all_sops() -> List[SOP]` - 获取所有 SOP 列表
- `get_sop_by_id(sop_id: str) -> Optional[SOP]` - 根据 ID 获取特定 SOP

#### 📁 FileSystemSOPStore - 文件系统存储

基于本地文件系统的传统存储实现，简单可靠。

**💡 工作机制**：
1. 📄 读取主配置文件（`sop_diagnosis_config.yaml`）
2. 🔍 解析 SOP 列表，根据 `template_file_path` 读取 Markdown 内容
3. 🔧 组装成完整的 `SOP` 对象列表

**✅ 优点**：
- 简单直观，易于管理
- 无需额外数据库依赖
- 文件版本控制友好

**❌ 缺点**：
- 不支持复杂查询
- 无法利用向量化检索

#### 🔍 PGVectorSOPStore - 向量化存储

基于 PostgreSQL + PGVector 扩展的高级存储实现，支持语义检索。

**💡 工作机制**：
- 🔗 使用 `langchain_community.vectorstores.pgvector.PGVector` 与数据库交互
- 🧠 利用 `DashScopeEmbeddingService` 生成文本向量
- 📊 支持高效的相似度检索和语义匹配

**✅ 优点**：
- 支持语义相似度检索
- 高效的向量查询性能
- 可扩展的存储容量

**❌ 缺点**：
- 需要额外的数据库环境
- 实现复杂度较高

### 🎯 SOPSelectionStrategy - 选择策略层

`SOPSelectionStrategy` 定义了 SOP 选择的算法接口，支持多种智能选择策略。

**📁 文件位置**：`src/deep_diagnose/core/reasoning/planning/strategy.py`

**🔧 核心接口**：
- `select_sop(self, query: str, sops: List[SOP]) -> SelectedSOP` - 从候选列表中选择最佳 SOP

#### 🤖 LLMSelectionStrategy - 大模型策略

利用大语言模型的理解能力进行智能 SOP 选择。

**💡 工作机制**：
1. 🔧 构建包含用户查询和所有可用 SOP 信息的 Prompt
2. 🤖 调用指定的 LLM（如 `ali_qwen_max`）进行推理
3. 📋 解析 LLM 返回的 SOP ID，返回对应的 SOP 对象

**✅ 优点**：
- 🧠 理解复杂的语义和上下文
- 🎯 处理模糊或复杂的查询
- 🔄 持续学习和改进

**❌ 缺点**：
- ⏱️ 响应延迟较高
- 💰 API 调用成本
- 🔄 结果可能不稳定

#### 📊 SimilaritySelectionStrategy - 相似度策略

基于向量余弦相似度的高效 SOP 选择策略。

**💡 工作机制**：
1. 🔤 使用 `DashScopeEmbeddingService` 将用户查询向量化
2. 📊 计算查询与每个 SOP 的语义相似度和示例匹配度
3. ⚖️ 使用加权公式计算综合得分：
   ```
   combined_score = (semantic_weight × semantic_score) + 
                   (example_weight × max_example_score)
   ```
4. 🎯 选择得分最高且超过阈值的 SOP，否则返回默认 SOP

**✅ 优点**：
- ⚡ 响应速度快
- 💰 成本低廉
- 🎯 匹配精准度高
- 🔄 结果稳定可靠

**❌ 缺点**：
- 📝 依赖高质量的文本描述
- 🤖 需要优质的 Embedding 模型

### 🌐 DashScopeEmbeddingService - 向量化服务

专门负责文本到向量转换的核心服务，支持单文本和批量处理。

**📁 文件位置**：`src/deep_diagnose/core/reasoning/planning/strategy/embedding_service.py`

**🔧 核心功能**：
- **🤖 模型**：默认使用阿里通义千问的 `text-embedding-v4` 模型
- **🔗 API**：通过 `dashscope` SDK 调用服务
- **⚡ 功能**：支持单个文本和批量文本的向量化
- **📊 工具**：提供 `calculate_similarity` 计算向量余弦相似度

## 📊 数据模型

系统使用 Pydantic 模型管理 SOP 数据的传递和验证。

**📁 文件位置**：`src/deep_diagnose/core/reasoning/planning/models.py`

### 📄 SOP 数据模型

```python
class SOP(BaseModel):
    """📄 SOP 数据模型"""
    id: str = Field(..., description="SOP 的唯一标识符")
    name: str = Field(..., description="SOP 的名称")
    description: str = Field(..., description="SOP 功能的详细描述")
    content: str = Field(..., description="SOP 的具体执行内容，通常是 Markdown 格式")
    tool_dependencies: List[str] = Field(default_factory=list, description="执行此 SOP 所需的工具列表")
    example_user_queries: List[str] = Field(default_factory=list, description="可能触发此 SOP 的用户查询示例")
```

### 🎯 选择结果模型

```python
class SelectedSOP(BaseModel):
    """🎯 SOP 选择结果模型"""
    sop: Optional[SOP] = None
    reason: str = Field(..., description="选择此 SOP 的原因")
    success: bool = True
    error_message: Optional[str] = None
    meta: Dict[str, Any] = Field(default_factory=dict, description="附加信息，如相似度分数")
```

---

## ⚙️ 配置与初始化

系统通过工厂函数 `create_sop_service` 在系统启动时或首次需要时创建。

**📁 文件位置**：`src/deep_diagnose/core/reasoning/planning/__init__.py`

### 📝 配置示例

```yaml
sop_service_config:
  strategy_type: similarity    # 策略类型: 'llm' 或 'similarity'
  storage_type: filesystem     # 存储类型: 'filesystem' 或 'pgvector'
  config:
    storage_config:
      # 文件系统存储配置
      config_path: "deep_diagnose/prompts/sop/configs/sop_diagnosis_config.yaml"
      # 向量数据库配置
      # connection_string: "postgresql+psycopg2://user:pass@host:port/db"
      # collection_name: "sops"
    strategy_config:
      # 相似度策略配置
      similarity_threshold: 0.6      # 相似度阈值
      semantic_weight: 0.7           # 语义权重
      example_weight: 0.3            # 示例权重
      default_sop_id: "instance_diagnosis_default"
      embedding_config:
        model: "text-embedding-v4"
        api_key: "your_dashscope_api_key"
```

---

## 🔄 工作流程

以下是 SOP 智能匹配系统的完整工作流程：

```mermaid
flowchart TD
    A["🚀 用户查询<br/>PlannerAgent 接收用户问题"] --> B["🔄 调用服务<br/>sop_service.find_sop(query)"]
    
    B --> C["💾 获取数据<br/>store.get_all_sops()"]
    
    C --> D{"🔍 选择策略<br/>判断使用的策略类型"}
    
    D -->|"LLM Strategy"| E["🤖 LLM 策略执行<br/>1. 构建 Prompt<br/>2. 调用 LLM<br/>3. 解析结果"]
    
    D -->|"Similarity Strategy"| F["📊 相似度策略执行<br/>1. 查询向量化<br/>2. 计算相似度<br/>3. 加权评分"]
    
    E --> G["🎯 获取结果<br/>返回选中的 SOP"]
    F --> G
    
    G --> H{"✅ 结果验证<br/>检查选择结果"}
    
    H -->|"成功"| I["📝 状态注入<br/>将 SOP 内容注入 ReasoningState"]
    H -->|"失败"| J["⚠️ 错误处理<br/>返回错误信息"]
    
    I --> K["🚀 执行继续<br/>后续步骤使用 SOP"]
    J --> L["🔁 重试或退出<br/>根据策略处理"]
    
    subgraph "🎯 相似度策略细节"
        F1["🔤 文本向量化<br/>使用 DashScope Embedding"]
        F2["📊 语义相似度<br/>计算查询与 SOP 描述相似度"]
        F3["📁 示例匹配<br/>计算查询与示例问题相似度"]
        F4["⚖️ 加权评分<br/>综合计算最终得分"]
        
        F1 --> F2
        F1 --> F3
        F2 --> F4
        F3 --> F4
    end
    
    F -.-> F1
    
    %% 现代化配色方案
    classDef startNode fill:#e1f5fe,stroke:#0288d1,stroke-width:3px,color:#01579b
    classDef processNode fill:#f3e5f5,stroke:#7b1fa2,stroke-width:3px,color:#4a148c
    classDef decisionNode fill:#fff3e0,stroke:#f57c00,stroke-width:3px,color:#e65100
    classDef strategyNode fill:#e8f5e8,stroke:#388e3c,stroke-width:3px,color:#1b5e20
    classDef successNode fill:#e8f5e8,stroke:#388e3c,stroke-width:3px,color:#1b5e20
    classDef errorNode fill:#ffebee,stroke:#d32f2f,stroke-width:3px,color:#b71c1c
    classDef detailNode fill:#f1f8e9,stroke:#689f38,stroke-width:2px,color:#33691e
    
    class A startNode
    class B,C,G processNode
    class D,H decisionNode
    class E,F strategyNode
    class I,K successNode
    class J,L errorNode
    class F1,F2,F3,F4 detailNode
```

### 📋 流程说明

1. **🚀 初始化**：`PlannerAgent` 在构造函数中调用 `create_sop_service()`
2. **📝 接收查询**：`PlannerAgent` 接收到用户查询
3. **🔄 调用服务**：调用 `sop_service.find_sop(user_query)`
4. **💾 获取数据**：`SOPService` 调用 `store.get_all_sops()` 获取所有 SOP
5. **🎯 执行策略**：根据配置的策略类型执行相应逻辑
6. **📝 状态注入**：将选定的 SOP 内容注入到 `ReasoningState`
7. **🚀 执行继续**：后续步骤使用注入的 SOP 内容
