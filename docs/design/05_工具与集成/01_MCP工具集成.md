# MCP 配置与连接

## 目录
- [1 MCP 配置与缓存](#1-mcp-配置与缓存)
  - [1.1 配置文件结构 (`config_prod.yaml`)](#11-配置文件结构-configprodyaml)
  - [1.2 Redis 缓存结构](#12-redis-缓存结构)
- [2 详细流程图](#2-详细流程图)
- [3 完整流程详解](#3-完整流程详解)
  - [3.1 配置加载与处理阶段](#31-配置加载与处理阶段)
  - [3.2 工具获取与缓存阶段](#32-工具获取与缓存阶段)
  - [3.3 缓存命中路径](#33-缓存命中路径)
  - [3.4 MCP连接建立与工具调用](#34-mcp连接建立与工具调用)
  - [3.5 后台定时刷新机制](#35-后台定时刷新机制)
- [4 关键技术细节](#4-关键技术细节)
  - [4.1 协议支持](#41-协议支持)
  - [4.2 错误处理与容错](#42-错误处理与容错)
  - [4.3 性能优化](#43-性能优化)


## 1 MCP 配置与缓存

### 1.1 配置文件结构 (`config_prod.yaml`)

基于实际的生产配置文件，MCP 服务器配置包含以下关键字段：

```yaml
mcp_servers:
  vm_coredump:
    protocol: streamable_http
    base_url: https://ecs-mcp.alibaba-inc.com
    path: /vm_coredump/mcp/
    token: ************************************************
    auth: token
    enabled_tools:
      - query_vm_coredump

  diagnose:
    protocol: streamable_http
    base_url: http://pre-xmca-cloudbot.aliyun-inc.com
    timeout: 20  # 超时配置（秒）
    path: /mcp/mcp/
    token: M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU=
    auth: bearer
    enabled_tools:
      - runLiveMigrationCheck
      - runColdMigrationCheck
      - listLiveMigrationRecords
      # ... 更多工具

  antv:
    protocol: stdio
    args:
      - -y
      - "@antv/mcp-server-chart"
    enabled_tools:
      - generate_pie_chart
```

**配置字段说明**：
- `protocol`: 传输协议，支持 `streamable_http` 和 `stdio`
- `base_url`: 服务器基础URL（HTTP协议）
- `path`: API路径（HTTP协议）
- `timeout`: 连接超时时间（秒）
- `auth`: 认证类型（`token`、`bearer`）
- `token`: 认证令牌
- `args`: 命令行参数（stdio协议）
- `enabled_tools`: 启用的工具列表

### 1.2 Redis 缓存结构

系统将处理后的配置和工具信息缓存到 Redis，键名格式：`{env}_deep_diagnose_mcp_tools`

**实际Redis缓存示例**（基于生产配置）：

```json
{
  "vm_coredump": {
    "mcp_server": {
      "name": "vm_coredump",
      "transport": "streamable_http",
      "timeout": 10,
      "enabled_tools": ["query_vm_coredump"],
      "add_to_agents": ["researcher"],
      "url": "https://ecs-mcp.alibaba-inc.com/vm_coredump/mcp/?token=************************************************"
    },
    "tools": [
      {
        "name": "query_vm_coredump",
        "description": "获取虚拟机核心转储文件信息",
        "args_schema": {
          "type": "object",
          "properties": {
            "instance_id": {
              "type": "string",
              "description": "ECS实例ID"
            }
          },
          "required": ["instance_id"]
        }
      }
    ]
  },
  "cloudbot": {
    "mcp_server": {
      "name": "cloudbot",
      "transport": "streamable_http",
      "timeout": 20,
      "enabled_tools": ["runLiveMigrationCheck", "runColdMigrationCheck"],
      "add_to_agents": ["researcher"],
      "url": "http://pre-xmca-cloudbot.aliyun-inc.com/mcp/mcp/",
      "headers": {
        "Content-Type": "application/json",
        "Authorization": "Bearer M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU="
      }
    },
    "tools": [
      {
        "name": "runLiveMigrationCheck",
        "description": "执行在线迁移检查",
        "args_schema": {
          "type": "object",
          "properties": {
            "instance_id": {"type": "string"},
            "target_host": {"type": "string"}
          },
          "required": ["instance_id"]
        }
      }
    ]
  },
  "antv": {
    "mcp_server": {
      "name": "antv",
      "transport": "stdio",
      "timeout": 10,
      "enabled_tools": ["generate_pie_chart"],
      "add_to_agents": ["researcher"],
      "command": "npx",
      "args": ["-y", "@antv/mcp-server-chart"]
    },
    "tools": [
      {
        "name": "generate_pie_chart",
        "description": "生成饼图",
        "args_schema": {
          "type": "object",
          "properties": {
            "data": {"type": "array"},
            "title": {"type": "string"}
          }
        }
      }
    ]
  }
}
```

**关键处理逻辑**：

1. **名称规范化**: `MCPToolConfig.SERVER_NAME_MAPPING` 将 `diagnose` 映射为 `cloudbot`
2. **URL构建**: `_build_url()` 方法拼接 `base_url + path`，并根据 `auth=token` 添加查询参数
3. **Headers构建**: `_build_headers()` 方法根据 `auth=bearer` 生成 Authorization 头
4. **协议适配**: 支持 HTTP (`streamable_http`) 和 stdio 两种传输协议
5. **超时配置**: **✅ 已生效 - 配置文件中的 `timeout: 20` 字段已正确读取、存储到Redis缓存并在MCP连接中生效**
6. **工具过滤**: 仅缓存 `enabled_tools` 列表中指定的工具

## 2 详细流程图

下图详细描绘了从配置加载到工具调用的完整生命周期，包括配置处理、Redis缓存、MCP连接建立和工具执行的全过程。

![Detailed MCP Config Loading Flow](../../assets/png/16_mcp_config_loading_flow_detailed.png)

## 3 完整流程详解

### 3.1 配置加载与处理阶段

**步骤1-2: 配置读取与规范化**
- `MCPToolConfig.get_server_configs()` 从 `config_prod.yaml` 读取 `mcp_servers` 配置
- 执行服务器名称规范化：`diagnose` → `cloudbot`（通过 `SERVER_NAME_MAPPING`）
- 调用 `_build_server_config()` 构建完整的服务器配置对象

**步骤3: URL和认证信息构建**
- `_build_url()`: 拼接 `base_url + path`，根据 `auth=token` 添加查询参数
- `_build_headers()`: 根据 `auth=bearer` 生成 Authorization 头
- 支持多种传输协议：`streamable_http`（HTTP）和 `stdio`（命令行）

**配置转换示例**：
```yaml
# 原始配置
diagnose:
  base_url: http://pre-xmca-cloudbot.aliyun-inc.com
  path: /mcp/mcp/
  auth: bearer
  token: M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU=

# 转换后的mcp_server对象
{
  "name": "cloudbot",
  "transport": "streamable_http",
  "url": "http://pre-xmca-cloudbot.aliyun-inc.com/mcp/mcp/",
  "headers": {
    "Content-Type": "application/json",
    "Authorization": "Bearer M93nQpklj0QDInS79QHRB+LkZPTVsSXThSnzKYyEkhU="
  }
}
```

### 3.2 工具获取与缓存阶段

**步骤4-5: 并发工具获取**
- `MCPToolClient.get_all_tools()` 使用 `asyncio.gather()` 并发请求所有MCP服务器
- 为每个服务器创建 `MultiServerMCPClient` 实例
- 发送 MCP `list_tools` 请求获取工具定义

**步骤6-7: 工具过滤与序列化**
- 根据 `enabled_tools` 列表过滤工具（仅保留配置中启用的工具）
- 使用 `structured_tool_2_dict()` 将 `BaseTool` 对象序列化为JSON
- 构建包含 `mcp_server` 配置和 `tools` 列表的缓存数据结构

**步骤8: Redis缓存写入**
- 缓存键名：`{env}_deep_diagnose_mcp_tools`（如：`prod_deep_diagnose_mcp_tools`）
- TTL设置：`refresh_time` 配置值（默认1800秒）
- 缓存内容包含完整的服务器配置和工具定义

### 3.3 缓存命中路径

**快速响应机制**：
- `MCPCacheManager.get_tools()` 首先检查Redis缓存
- 缓存命中时，使用 `dict_2_structured_tool()` 反序列化工具对象
- 直接返回可用的 `BaseTool` 列表，避免重复的网络请求

### 3.4 MCP连接建立与工具调用

**步骤9-11: 实时工具执行**
1. **工具调用触发**: Agent调用 `tool.invoke(params)`
2. **MCP会话建立**: 
   - 使用 `create_session(mcp_server)` 建立MCP连接
   - 传入缓存中的服务器配置（URL、headers等）
3. **工具执行请求**: 
   - 调用 `call_tool(name, arguments)` 发送MCP请求
   - 请求体包含工具名称和参数
4. **结果处理**: 
   - MCP服务器执行工具逻辑并返回结果
   - 使用 `_convert_call_tool_result()` 转换结果格式
   - 将结果返回给调用的Agent

### 3.5 后台定时刷新机制

**Celery定时任务**：
- `Celery Beat` 调度器定期触发 `refresh_mcp_cache_task`
- 强制执行完整的配置加载和工具获取流程
- 确保缓存数据与MCP服务器保持同步

## 4 关键技术细节


### 4.1 协议支持
- **HTTP协议** (`streamable_http`): 支持URL、认证头、查询参数
- **Stdio协议** (`stdio`): 支持命令行参数、进程通信

### 4.2 错误处理与容错
- 并发请求中的异常会被捕获和记录
- 单个服务器失败不影响其他服务器的工具获取
- 缓存失效时自动触发刷新机制

### 4.3 性能优化
- **并发获取**: 使用 `asyncio.gather()` 同时请求多个MCP服务器
- **智能缓存**: Redis缓存减少重复的网络请求
- **工具过滤**: 仅缓存和返回启用的工具，减少内存占用