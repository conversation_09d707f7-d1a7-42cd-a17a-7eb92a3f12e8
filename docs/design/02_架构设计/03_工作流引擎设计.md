# 工作流引擎设计

## 1. 概述

ECS 深度诊断系统基于 LangGraph 构建工作流引擎，通过状态图管理多智能体的协同执行。系统采用 `StateGraph` 模式，支持条件分支、并行处理和状态持久化。

## 2. 核心组件

### 2.1 状态图架构

#### 📊 StateGraph 基础
```python
from langgraph.graph import StateGraph, START, END
from langgraph.checkpoint.memory import MemorySaver

def _build_base_graph():
    """构建基础状态图"""
    builder = StateGraph(ReasoningState)
    
    # 定义执行起点
    builder.add_edge(START, "coordinator")
    
    # 添加智能体节点
    builder.add_node("coordinator", coordinator_node)
    builder.add_node("background_investigator", background_investigation_node)
    builder.add_node("planner", planner_node)
    builder.add_node("research_team", research_team_node)
    builder.add_node("researcher", researcher_node)
    builder.add_node("coder", coder_node)
    builder.add_node("human_feedback", human_feedback_node)
    builder.add_node("reporter", reporter_node)
    
    # 并发处理节点
    builder.add_node("parallel_html_generator", parallel_html_generator_node)
    builder.add_node("html_report_merger", html_report_merger_node)
    
    # 定义固定路径
    builder.add_edge("reporter", "parallel_html_generator")
    builder.add_edge("parallel_html_generator", "html_report_merger")
    builder.add_edge("html_report_merger", END)
    
    return builder
```

#### 🔄 条件路由机制
工作流中的智能体通过返回 `Command` 对象来实现动态路由：

```python
async def coordinator_node(state: ReasoningState, config: RunnableConfig) -> Command:
    """协调器节点 - 决定执行路径"""
    
    # 分析问题并决定下一步
    goto = "background_investigator" if state.get("enable_background_investigation") else "planner"
    
    return Command(goto=goto)

async def research_team_node(state: ReasoningState, config: RunnableConfig) -> Command:
    """研究团队节点 - 任务分发"""
    
    current_plan = state.get("current_plan")
    
    # 检查执行条件
    if not current_plan or not current_plan.steps:
        return Command(goto="planner")
    
    # 检查是否所有步骤完成
    if all(step.execution_res for step in current_plan.steps):
        return Command(goto="planner")
    
    # 根据步骤类型路由
    for step in current_plan.steps:
        if not step.execution_res:
            if step.step_type == StepType.PROCESSING:
                return Command(goto="coder")
            else:
                return Command(goto="researcher")
    
    return Command(goto="planner")
```

### 2.2 状态管理

#### 📋 ReasoningState 定义
```python
class ReasoningState(MessagesState):
    """推理状态 - 工作流的核心状态容器"""
    
    # 身份标识
    request_id: str = ""                    # 请求唯一标识符
    
    # 执行状态
    observations: List[str] = []            # 执行观察记录
    plan_iterations: int = 0                # 计划迭代次数
    current_plan: Plan | str = None         # 当前执行计划
    final_report: str = ""                  # 最终报告内容
    
    # 执行控制
    auto_accepted_plan: bool = False        # 是否自动接受计划
    enable_background_investigation: bool = True  # 是否启用背景调查
    
    # 数据存储
    background_investigation_results: str = None  # 背景调查结果
    mcp_servers_description: str = ""       # MCP服务器描述
    sop_name: str = ""                     # 使用的SOP名称
    
    # HTML报告相关
    problem_description_html: str = ""      # 问题描述HTML片段
    diagnosis_info_html: str = ""          # 诊断信息HTML片段
    key_findings_html: str = ""            # 关键发现HTML片段
    evidence_chain_html: str = ""          # 证据链HTML片段
    summary_conclusion_html: str = ""      # 总结结论HTML片段
    merged_html_report: str = ""           # 合并后的完整HTML报告
```

#### 🔄 状态更新机制
```python
# 智能体通过Command更新状态
return Command(
    update={
        "current_plan": new_plan,
        "plan_iterations": plan_iterations + 1,
        "sop_name": selected_sop_name
    },
    goto="human_feedback"
)

# 状态更新是原子性的，确保数据一致性
```

### 2.3 内存管理

#### 💾 检查点机制
```python
def build_graph_with_memory():
    """构建带内存持久化的工作流图"""
    
    # 使用LangGraph内置内存存储
    memory = MemorySaver()
    
    # 构建状态图
    builder = _build_base_graph()
    
    # 编译图并添加检查点支持
    return builder.compile(checkpointer=memory)

def build_graph():
    """构建无内存的工作流图（用于无状态执行）"""
    builder = _build_base_graph()
    return builder.compile()
```

#### 🔄 状态恢复
```python
# 工作流支持从任意检查点恢复执行
async def resume_workflow(thread_id: str, checkpoint_id: str):
    """从检查点恢复工作流"""
    
    graph = build_graph_with_memory()
    
    # 从指定检查点恢复
    config = {"configurable": {"thread_id": thread_id}}
    
    # 继续执行
    async for event in graph.astream(None, config=config):
        yield event
```

## 3. 执行流程

### 3.1 标准执行流

#### 🚀 启动流程
```mermaid
graph TD
    A[START] --> B[coordinator]
    B --> C{enable_background_investigation?}
    C -->|true| D[background_investigator]
    C -->|false| E[planner]
    D --> E
    E --> F[human_feedback]
    F --> G{plan_accepted?}
    G -->|accepted| H[research_team]
    G -->|edit| E
    H --> I{step_type?}
    I -->|PROCESSING| J[coder]
    I -->|RESEARCH| K[researcher]
    J --> L{more_steps?}
    K --> L
    L -->|yes| H
    L -->|no| M[reporter]
    M --> N[parallel_html_generator]
    N --> O[html_report_merger]
    O --> P[END]
```

#### 📋 执行阶段说明

1. **协调阶段**：
   - 分析用户问题
   - 匹配SOP能力
   - 决定执行路径

2. **背景调查阶段**（可选）：
   - 网络搜索相关信息
   - 收集背景资料

3. **规划阶段**：
   - 选择合适的SOP
   - 生成详细执行计划
   - 等待人工确认

4. **执行阶段**：
   - 分发任务到专业智能体
   - 执行诊断步骤
   - 收集执行结果

5. **报告阶段**：
   - 生成诊断报告
   - 并行生成HTML片段
   - 智能合并最终报告

### 3.2 节点实现模式

#### 🔧 标准节点模式
```python
async def agent_node(state: ReasoningState, config: RunnableConfig) -> Command:
    """标准智能体节点实现模式"""
    
    # 1. 创建智能体实例
    configurable = Configuration.from_runnable_config(config)
    agent = AgentClass(configurable)
    
    # 2. 执行智能体逻辑
    result = await agent.execute(state, config)
    
    # 3. 返回命令
    return result

# 实际应用示例
async def planner_node(state: ReasoningState, config: RunnableConfig) -> Command:
    configurable = Configuration.from_runnable_config(config)
    _planner = PlannerAgent(configurable)
    return await _planner.execute(state, config)
```

#### 🔀 条件分支节点
```python
async def conditional_node(state: ReasoningState, config: RunnableConfig) -> Command:
    """条件分支节点"""
    
    # 根据状态决定路径
    if condition_a(state):
        return Command(goto="path_a")
    elif condition_b(state):
        return Command(goto="path_b")
    else:
        return Command(goto="default_path")
```

## 4. 并行处理

### 4.1 并行HTML生成

#### 🚀 RunnableParallel 实现
```python
from langchain_core.runnables import RunnableParallel

async def parallel_html_generator_node(state: ReasoningState, config: RunnableConfig) -> Command:
    """并行生成HTML片段"""
    
    # 创建并行任务
    parallel_chains = RunnableParallel({
        "problem_description": create_problem_description_chain(),
        "diagnosis_info": create_diagnosis_info_chain(),
        "key_findings": create_key_findings_chain(),
        "evidence_chain": create_evidence_chain_chain(),
        "summary_conclusion": create_summary_conclusion_chain()
    })
    
    # 并行执行
    results = await parallel_chains.ainvoke(state, config=config)
    
    # 更新状态
    return Command(
        update={
            "problem_description_html": results["problem_description"],
            "diagnosis_info_html": results["diagnosis_info"],
            "key_findings_html": results["key_findings"],
            "evidence_chain_html": results["evidence_chain"],
            "summary_conclusion_html": results["summary_conclusion"]
        },
        goto="html_report_merger"
    )
```

#### ⚡ 性能优化
```python
# 使用asyncio.gather进行并发优化
async def optimized_parallel_execution(state: ReasoningState, config: RunnableConfig):
    """优化的并行执行"""
    
    # 创建并发任务
    tasks = [
        create_problem_description_task(state, config),
        create_diagnosis_info_task(state, config),
        create_key_findings_task(state, config),
        create_evidence_chain_task(state, config),
        create_summary_conclusion_task(state, config)
    ]
    
    # 并发执行，减少LangChain框架开销
    results = await asyncio.gather(*tasks)
    
    return results
```

### 4.2 智能合并

#### 🔗 HTML片段合并
```python
async def html_report_merger_node(state: ReasoningState, config: RunnableConfig) -> Command:
    """智能合并HTML片段"""
    
    # 收集HTML片段
    html_sections = {
        "problem_description": state.get("problem_description_html", ""),
        "diagnosis_info": state.get("diagnosis_info_html", ""),
        "key_findings": state.get("key_findings_html", ""),
        "evidence_chain": state.get("evidence_chain_html", ""),
        "summary_conclusion": state.get("summary_conclusion_html", "")
    }
    
    # 应用合并模板
    merge_prompt = apply_prompt_template("html_report_merger", {
        **state,
        **html_sections
    })
    
    # 执行智能合并
    llm = get_llm_by_type("reporter")
    merged_html = await llm.ainvoke(merge_prompt)
    
    return Command(
        update={"merged_html_report": merged_html.content},
        goto="__end__"
    )
```

## 5. 错误处理

### 5.1 异常传播

#### 🛡️ 统一异常处理
```python
class BaseAgent(ABC):
    """智能体基类中的错误处理"""
    
    async def execute(self, state: ReasoningState, config: RunnableConfig) -> Command:
        try:
            self.logger.info(f"{self.name} starting")
            result = await self._do_execute(state, config)
            self.logger.info(f"{self.name} completed")
            return result
        except Exception as e:
            self.logger.error(f"{self.name} error: {e}")
            return self._handle_error(e, state)
    
    def _handle_error(self, error: Exception, state: ReasoningState) -> Command:
        """默认错误处理 - 子类可重写"""
        return Command(goto="__end__")
```

#### 🔄 智能体特定错误处理
```python
class ResearcherAgent(BaseAgent):
    """研究员智能体的特定错误处理"""
    
    def _handle_error(self, error: Exception, state: ReasoningState) -> Command:
        """研究员特定的错误处理"""
        self.logger.error(f"Research execution failed: {error}")
        
        # 研究失败时返回研究团队重新分配
        return Command(goto="research_team")

class CoderAgent(BaseAgent):
    """编码员智能体的特定错误处理"""
    
    def _handle_error(self, error: Exception, state: ReasoningState) -> Command:
        """编码员特定的错误处理"""
        self.logger.error(f"Code processing failed: {error}")
        
        # 代码处理失败时返回研究团队
        return Command(goto="research_team")
```

### 5.2 重试机制

#### 🔄 自动重试
```python
async def execute_with_retry(agent_func, state: ReasoningState, config: RunnableConfig, max_retries: int = 3):
    """带重试的执行函数"""
    
    for attempt in range(max_retries):
        try:
            return await agent_func(state, config)
        except Exception as e:
            if attempt == max_retries - 1:
                raise e
            
            logger.warning(f"Attempt {attempt + 1} failed: {e}, retrying...")
            await asyncio.sleep(2 ** attempt)  # 指数退避
```

## 6. 监控与调试

### 6.1 执行跟踪

#### 🔍 事件流监控
```python
async def monitor_workflow_execution(graph, input_data, config):
    """监控工作流执行"""
    
    async for agent, action, event_data in graph.astream(
        input_data,
        config=config,
        stream_mode=["messages", "updates"],
        subgraphs=True
    ):
        # 记录执行事件
        logger.info(f"Agent: {agent}, Action: {action}, Data: {event_data}")
        
        # 性能监控
        if agent in CRITICAL_AGENTS:
            monitor_agent_performance(agent, event_data)
```

#### 📊 性能指标
```python
class WorkflowMetrics:
    """工作流性能指标"""
    
    def __init__(self):
        self.execution_times = {}
        self.state_sizes = {}
        self.error_counts = {}
    
    def record_agent_execution(self, agent_name: str, duration: float):
        """记录智能体执行时间"""
        if agent_name not in self.execution_times:
            self.execution_times[agent_name] = []
        self.execution_times[agent_name].append(duration)
    
    def record_state_size(self, state_size: int):
        """记录状态大小"""
        self.state_sizes[datetime.now()] = state_size
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        return {
            "avg_execution_times": {
                agent: sum(times) / len(times)
                for agent, times in self.execution_times.items()
            },
            "total_errors": sum(self.error_counts.values()),
            "avg_state_size": sum(self.state_sizes.values()) / len(self.state_sizes)
        }
```

### 6.2 调试工具

#### 🐛 状态检查器
```python
def debug_state(state: ReasoningState, step: str):
    """调试状态检查器"""
    
    logger.debug(f"=== State Debug at {step} ===")
    logger.debug(f"Request ID: {state.get('request_id')}")
    logger.debug(f"Plan Iterations: {state.get('plan_iterations')}")
    logger.debug(f"Current Plan: {state.get('current_plan')}")
    logger.debug(f"Observations: {len(state.get('observations', []))}")
    logger.debug("=" * 40)
```

#### 📋 执行历史
```python
class ExecutionHistory:
    """执行历史记录器"""
    
    def __init__(self):
        self.history = []
    
    def record_step(self, agent: str, action: str, timestamp: datetime, state_snapshot: dict):
        """记录执行步骤"""
        self.history.append({
            "agent": agent,
            "action": action,
            "timestamp": timestamp,
            "state_snapshot": state_snapshot
        })
    
    def get_execution_path(self) -> List[str]:
        """获取执行路径"""
        return [step["agent"] for step in self.history]
    
    def export_debug_info(self) -> dict:
        """导出调试信息"""
        return {
            "execution_path": self.get_execution_path(),
            "total_steps": len(self.history),
            "execution_time": self.history[-1]["timestamp"] - self.history[0]["timestamp"],
            "detailed_history": self.history
        }
```

## 7. 扩展能力

### 7.1 动态节点添加

#### 🔌 运行时扩展
```python
def extend_graph_with_custom_agent(builder: StateGraph, agent_name: str, agent_func):
    """动态添加自定义智能体"""
    
    # 添加节点
    builder.add_node(agent_name, agent_func)
    
    # 添加路由逻辑
    def custom_router(state: ReasoningState) -> str:
        # 自定义路由逻辑
        if should_use_custom_agent(state):
            return agent_name
        return "default_agent"
    
    # 添加条件边
    builder.add_conditional_edges("some_agent", custom_router)
```

### 7.2 插件化工具

#### 🛠️ 工具插件机制
```python
class ToolPlugin:
    """工具插件基类"""
    
    @abstractmethod
    async def get_tools(self) -> List[Tool]:
        """获取插件提供的工具"""
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """获取插件描述"""
        pass

class MCPToolPlugin(ToolPlugin):
    """MCP工具插件"""
    
    async def get_tools(self) -> List[Tool]:
        manager = MCPToolManager()
        await manager.connect_all()
        return await manager.get_all_tools()
```

## 8. 最佳实践

### 8.1 性能优化
1. **状态最小化**：只在状态中保存必要数据
2. **并发处理**：利用并行处理提升效率
3. **资源复用**：复用连接和实例
4. **缓存策略**：缓存计算结果和中间状态

### 8.2 可维护性
1. **模块化设计**：每个智能体独立文件
2. **统一接口**：使用BaseAgent统一接口
3. **配置驱动**：通过配置控制行为
4. **文档完善**：详细的代码文档和注释

### 8.3 可观测性
1. **结构化日志**：使用结构化日志记录
2. **性能监控**：关键指标实时监控
3. **错误追踪**：完整的错误堆栈跟踪
4. **执行链路**：完整的执行链路可视化