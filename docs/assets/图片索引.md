# ECS 深度诊断系统 - 图片索引

本文档提供了系统文档中所有图片的完整索引，包括图片编号、文件名、描述和使用位置。

## 图片编号规范

### 系统架构相关 (图1-1 至 图4-1)
- **图1-1**: `01_system_architecture.png/svg` - 系统总体架构图
  - 使用位置: `docs/design/02_架构设计.md`
  - 描述: 展示系统的四层架构：展现与接入层、核心业务层、工具与服务层、基础设施层

- **图2-1**: `02_core_workflow.png/svg` - 核心工作流图
  - 使用位置: `docs/design/03_核心工作流.md`
  - 描述: 详细展示智能体之间的协作流程和决策路径

- **图3-1**: `03_api_async_task_flow.png/svg` - API异步任务处理流程图
  - 使用位置: `docs/design/04_API接口规范.md`
  - 描述: 展示客户端提交异步任务后，系统内部的处理流程

- **图4-1**: `04_config_loading_flow.png/svg` - 配置系统加载流程图
  - 使用位置: `docs/design/05_配置与安全.md`, `docs/Developer_QA/01_config_module_guide.md`
  - 描述: 展示配置系统的加载和合并流程

### 研究员智能体相关 (图5-1 至 图5-3)
- **图5-1**: `05_researcher_agent_detailed_architecture.png/svg` - 研究员智能体详细架构图
  - 使用位置: `docs/design/08_研究员智能体详细设计与核心执行机制.md`
  - 描述: 展示完整的组件架构和核心方法，包括关键设计优点和组件关系

- **图5-2**: `06_researcher_mcp_integration.png/svg` - MCP工具集成架构图
  - 使用位置: `docs/design/08_研究员智能体详细设计与核心执行机制.md`
  - 描述: 展示MCP工具集成的四层架构

- **图5-3**: `07_researcher_execution_flow.png/svg` - 研究员智能体执行流程图
  - 使用位置: `docs/design/08_研究员智能体详细设计与核心执行机制.md`
  - 描述: 展示优化布局的完整执行流程

### 配置和安全相关 (图6-1 至 图6-3)
- **图6-1**: `08_security_keycenter.png` - KeyCenter密钥定义图
  - 使用位置: `docs/Developer_QA/01_config_module_guide.md`
  - 描述: 展示在KeyCenter中定义密钥的界面和操作

- **图6-2**: `09_security_yaml_usage.png` - YAML中使用!decrypt标签图
  - 使用位置: `docs/Developer_QA/01_config_module_guide.md`
  - 描述: 展示在YAML配置文件中使用!decrypt标签引用加密数据的方法

- **图6-3**: `10_mcp_servers_config.png` - MCP服务器配置示例图
  - 使用位置: `docs/Developer_QA/02_mcp_module_guide.md`
  - 描述: 展示MCP服务器在配置文件中的具体配置位置和格式

## 文件格式说明

- **SVG格式**: 矢量图形，适用于高质量打印和缩放
- **PNG格式**: 位图格式，适用于网页显示和一般文档

## 图片文件位置

- SVG文件: `docs/assets/svg/`
- PNG文件: `docs/assets/png/`

## 更新记录

- 2024年: 初始版本，建立图片编号规范
- 重新编号所有图片文件，统一图片引用格式
- 为所有图片添加了标准化的图注格式

## 使用规范

1. 新增图片时，请按照编号规范进行命名
2. 在markdown文档中引用图片时，请使用标准格式：
   ```markdown
   ![图片描述](../assets/png/文件名.png)
   *图X-Y: 图片标题*
   ```
3. 更新图片时，请同步更新本索引文档