<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1200" viewBox="0 0 1400 1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 现代渐变色彩定义 -->
    <linearGradient id="startGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#22c55e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#16a34a;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="processGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2563eb;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="decisionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="errorGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ef4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="toolGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="endGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#64748b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#475569;stop-opacity:1" />
    </linearGradient>
    <!-- 柔和阴影滤镜 -->
    <filter id="softShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
    <!-- 箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#64748b"/>
    </marker>
    <marker id="successArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#22c55e"/>
    </marker>
    <marker id="errorArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#ef4444"/>
    </marker>
  </defs>

  <!-- 背景 -->
  <rect width="1400" height="1200" fill="#f8fafc"/>
  
  <!-- 标题 -->
  <text x="700" y="40" text-anchor="middle" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="#1e293b">
    研究员智能体执行流程 (优化布局)
  </text>
  
  <!-- 主容器 -->
  <g id="main-flow" transform="translate(450, 0)">
    <!-- 开始节点 -->
    <ellipse cx="250" cy="120" rx="60" ry="25" fill="url(#startGradient)" stroke="#16a34a" stroke-width="2" filter="url(#softShadow)"/>
    <text x="250" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">开始执行</text>
    
    <!-- 验证执行条件 -->
    <rect x="150" y="190" width="200" height="60" rx="15" fill="url(#decisionGradient)" stroke="#d97706" stroke-width="2" filter="url(#softShadow)"/>
    <text x="250" y="215" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">_validate_execution_conditions()</text>
    <text x="250" y="235" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">验证执行条件</text>
    
    <!-- StepExecutor入口 -->
    <rect x="150" y="300" width="200" height="60" rx="15" fill="url(#processGradient)" stroke="#2563eb" stroke-width="2" filter="url(#softShadow)"/>
    <text x="250" y="325" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">StepExecutor.execute_current_step()</text>
    <text x="250" y="345" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">执行当前步骤</text>

    <!-- 找到当前步骤 -->
    <rect x="150" y="410" width="200" height="60" rx="15" fill="url(#processGradient)" stroke="#2563eb" stroke-width="2" filter="url(#softShadow)"/>
    <text x="250" y="435" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">_find_current_step()</text>
    <text x="250" y="455" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">找到未执行的步骤</text>

    <!-- 步骤判断 -->
    <rect x="150" y="520" width="200" height="60" rx="15" fill="url(#decisionGradient)" stroke="#d97706" stroke-width="2" filter="url(#softShadow)"/>
    <text x="250" y="545" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">是否找到步骤？</text>
    <text x="250" y="565" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">检查步骤状态</text>

    <!-- 准备工具 -->
    <rect x="150" y="630" width="200" height="60" rx="15" fill="url(#toolGradient)" stroke="#7c3aed" stroke-width="2" filter="url(#softShadow)"/>
    <text x="250" y="655" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">_prepare_tools()</text>
    <text x="250" y="675" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">设置MCP工具</text>

    <!-- 构建输入上下文 -->
    <rect x="150" y="740" width="200" height="60" rx="15" fill="url(#toolGradient)" stroke="#7c3aed" stroke-width="2" filter="url(#softShadow)"/>
    <text x="250" y="765" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">_prepare_input()</text>
    <text x="250" y="785" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">构建执行上下文</text>

    <!-- 创建Agent执行器 -->
    <rect x="150" y="850" width="200" height="60" rx="15" fill="url(#processGradient)" stroke="#2563eb" stroke-width="2" filter="url(#softShadow)"/>
    <text x="250" y="875" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">create_agent_with_tools()</text>
    <text x="250" y="895" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">创建带工具的Agent执行器</text>

    <!-- 执行并获取结果 -->
    <rect x="150" y="960" width="200" height="60" rx="15" fill="url(#processGradient)" stroke="#2563eb" stroke-width="2" filter="url(#softShadow)"/>
    <text x="250" y="985" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">executor.ainvoke()</text>
    <text x="250" y="1005" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">执行并获取结果</text>

    <!-- 更新状态并返回 -->
    <rect x="125" y="1070" width="250" height="60" rx="15" fill="url(#endGradient)" stroke="#475569" stroke-width="2" filter="url(#softShadow)"/>
    <text x="250" y="1095" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">更新State并返回Command</text>
    <text x="250" y="1115" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">goto="research_team"</text>

    <!-- 主流程连接线 (垂直) -->
    <path d="M 250 145 V 190" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
    <path d="M 250 250 V 300" stroke="#22c55e" stroke-width="2" marker-end="url(#successArrow)" fill="none"/>
    <text x="275" y="275" font-family="Arial, sans-serif" font-size="10" fill="#22c55e">条件满足</text>
    <path d="M 250 360 V 410" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
    <path d="M 250 470 V 520" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
    <path d="M 250 580 V 630" stroke="#22c55e" stroke-width="2" marker-end="url(#successArrow)" fill="none"/>
    <text x="275" y="605" font-family="Arial, sans-serif" font-size="10" fill="#22c55e">找到步骤</text>
    <path d="M 250 690 V 740" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
    <path d="M 250 800 V 850" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
    <path d="M 250 910 V 960" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
    <path d="M 250 1020 V 1070" stroke="#64748b" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
  </g>

  <!-- 错误处理流程 -->
  <g id="error-flow">
    <!-- 错误处理节点 -->
    <rect x="950" y="1070" width="200" height="60" rx="15" fill="url(#errorGradient)" stroke="#dc2626" stroke-width="2" filter="url(#softShadow)"/>
    <text x="1050" y="1100" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">返回 research_team</text>

    <!-- 错误路径总线 -->
    <path d="M 850 220 V 1095 H 950" stroke="#ef4444" stroke-width="2" marker-end="url(#errorArrow)" fill="none"/>

    <!-- 条件不满足分支 -->
    <path d="M 800 220 H 850" stroke="#ef4444" stroke-width="2" marker-end="url(#arrowhead)" fill="none"/>
    <text x="825" y="215" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#ef4444">条件不满足</text>

    <!-- 未找到步骤分支 -->
    <path d="M 800 550 H 850 M 850 550 V 230 a 10 10 0 0 0 -10 -10 H 850" stroke="#ef4444" stroke-width="2" fill="none"/>
    <text x="825" y="545" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#ef4444">未找到步骤</text>
  </g>
  
  <!-- StepContextBuilder详细流程 -->
  <g id="context-builder-detail">
    <rect x="100" y="650" width="400" height="200" rx="15" fill="url(#toolGradient)" opacity="0.1" stroke="#7c3aed" stroke-width="1.5" stroke-dasharray="4"/>
    <text x="300" y="675" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#7c3aed">StepContextBuilder 详细流程</text>
    
    <rect x="120" y="700" width="160" height="40" rx="8" fill="white" stroke="#8b5cf6" stroke-width="1"/>
    <text x="200" y="725" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#7c3aed">_build_history_context()</text>
    
    <rect x="300" y="700" width="160" height="40" rx="8" fill="white" stroke="#8b5cf6" stroke-width="1"/>
    <text x="380" y="725" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#7c3aed">_build_current_task()</text>
    
    <rect x="120" y="760" width="160" height="40" rx="8" fill="white" stroke="#8b5cf6" stroke-width="1"/>
    <text x="200" y="785" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#7c3aed">_build_agent_instruction()</text>
    
    <rect x="300" y="760" width="160" height="40" rx="8" fill="white" stroke="#8b5cf6" stroke-width="1"/>
    <text x="380" y="785" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#7c3aed">build_input()</text>
    
    <rect x="210" y="815" width="180" height="25" rx="8" fill="white" stroke="#8b5cf6" stroke-width="1"/>
    <text x="300" y="832" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#7c3aed">返回完整的messages</text>
    
    <!-- StepContextBuilder内部连接 -->
    <path d="M 200 740 V 760" stroke="#8b5cf6" stroke-width="1" marker-end="url(#arrowhead)" fill="none"/>
    <path d="M 380 740 V 760" stroke="#8b5cf6" stroke-width="1" marker-end="url(#arrowhead)" fill="none"/>
    <path d="M 200 800 L 290 815" stroke="#8b5cf6" stroke-width="1" marker-end="url(#arrowhead)" fill="none"/>
    <path d="M 380 800 L 300 815" stroke="#8b5cf6" stroke-width="1" marker-end="url(#arrowhead)" fill="none"/>
    
    <!-- 连接到主流程 -->
    <path d="M 520 770 H 500" stroke="#8b5cf6" stroke-width="2" stroke-dasharray="5,5" marker-end="url(#arrowhead)" fill="none"/>
    <text x="510" y="760" font-family="Arial, sans-serif" font-size="10" fill="#7c3aed">详情</text>
  </g>
  
  <!-- 标注区域 -->
  <g id="annotations">
    <rect x="80" y="80" width="280" height="200" rx="10" fill="#fef3c7" stroke="#f59e0b" stroke-width="1"/>
    <text x="220" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#92400e">关键验证条件</text>
    <text x="90" y="130" font-family="Arial, sans-serif" font-size="11" fill="#92400e">1. 检查是否有有效计划 (current_plan)</text>
    <text x="90" y="150" font-family="Arial, sans-serif" font-size="11" fill="#92400e">2. 检查是否有待执行步骤 (steps存在)</text>
    <text x="90" y="170" font-family="Arial, sans-serif" font-size="11" fill="#92400e">3. 检查是否有未完成的研究步骤</text>
    <text x="90" y="200" font-family="Arial, sans-serif" font-size="11" fill="#92400e" font-weight="bold">验证失败时：</text>
    <text x="90" y="220" font-family="Arial, sans-serif" font-size="11" fill="#92400e">• 记录警告日志</text>
    <text x="90" y="240" font-family="Arial, sans-serif" font-size="11" fill="#92400e">• 返回 research_team 节点重新分配</text>

    <rect x="80" y="300" width="280" height="200" rx="10" fill="#dbeafe" stroke="#3b82f6" stroke-width="1"/>
    <text x="220" y="325" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#1d4ed8">执行特点</text>
    <text x="90" y="350" font-family="Arial, sans-serif" font-size="11" fill="#1d4ed8">• 支持并发工具调用</text>
    <text x="90" y="370" font-family="Arial, sans-serif" font-size="11" fill="#1d4ed8">• 动态MCP工具集成</text>
    <text x="90" y="390" font-family="Arial, sans-serif" font-size="11" fill="#1d4ed8">• 智能上下文构建</text>
    <text x="90" y="410" font-family="Arial, sans-serif" font-size="11" fill="#1d4ed8">• 完整的错误处理机制</text>
    <text x="90" y="430" font-family="Arial, sans-serif" font-size="11" fill="#1d4ed8">• 状态一致性维护</text>
    <text x="90" y="450" font-family="Arial, sans-serif" font-size="11" fill="#1d4ed8">• 结构化的观察结果更新</text>
    
    <rect x="980" y="80" width="280" height="200" rx="10" fill="#f0fdf4" stroke="#22c55e" stroke-width="1"/>
    <text x="1120" y="105" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#15803d">性能优化</text>
    <text x="990" y="130" font-family="Arial, sans-serif" font-size="11" fill="#15803d">• 工具缓存和复用机制</text>
    <text x="990" y="150" font-family="Arial, sans-serif" font-size="11" fill="#15803d">• 智能上下文构建避免重复计算</text>
    <text x="990" y="170" font-family="Arial, sans-serif" font-size="11" fill="#15803d">• 并发工具调用提高效率</text>
    <text x="990" y="190" font-family="Arial, sans-serif" font-size="11" fill="#15803d">• 递归限制防止无限循环</text>
    <text x="990" y="210" font-family="Arial, sans-serif" font-size="11" fill="#15803d">• 结构化日志与内存友好管理</text>
    
    <rect x="980" y="300" width="280" height="200" rx="10" fill="#fdf2f8" stroke="#ec4899" stroke-width="1"/>
    <text x="1120" y="325" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#be185d">数据流转</text>
    <text x="990" y="350" font-family="Arial, sans-serif" font-size="11" fill="#be185d">State → 验证 → StepExecutor</text>
    <text x="990" y="370" font-family="Arial, sans-serif" font-size="11" fill="#be185d">Step → 工具准备 → 上下文构建</text>
    <text x="990" y="390" font-family="Arial, sans-serif" font-size="11" fill="#be185d">Messages → Agent执行 → 结果</text>
    <text x="990" y="410" font-family="Arial, sans-serif" font-size="11" fill="#be185d">Response → 状态更新 → Command</text>
    <text x="990" y="430" font-family="Arial, sans-serif" font-size="11" fill="#be185d">Observations → 历史记录 → 下次执行</text>
    <text x="990" y="450" font-family="Arial, sans-serif" font-size="11" fill="#be185d">错误 → 日志记录 → 重新分配</text>
  </g>
</svg>