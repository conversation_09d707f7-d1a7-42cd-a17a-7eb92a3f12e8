<svg width="1800" height="950" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stop-color="#f8fafc" />
            <stop offset="100%" stop-color="#f1f5f9" />
        </linearGradient>
        <filter id="card-shadow" x="-10%" y="-10%" width="120%" height="120%">
            <feDropShadow dx="0" dy="5" stdDeviation="8" flood-color="#000000" flood-opacity="0.05" />
        </filter>
        <marker id="arrow-head" viewBox="0 0 10 10" refX="8" refY="5" markerWidth="6" markerHeight="6" orient="auto-start-reverse">
            <path d="M 0 0 L 10 5 L 0 10 z" fill="#64748b" />
        </marker>
        <style>
            .font-sans { font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif; }
            .title { font-size: 24px; font-weight: 700; fill: #1e293b; }
            .subtitle { font-size: 14px; font-weight: 400; fill: #64748b; }
            .stage-title { font-size: 16px; font-weight: 600; }
            .component-title { font-size: 14px; font-weight: 600; }
            .component-desc { font-size: 12px; fill: #475569; }
            .annotation { font-size: 11px; font-style: italic; fill: #475569; }
            .flow-line { stroke: #94a3b8; stroke-width: 2; marker-end: url(#arrow-head); fill: none; }
            .highlight-title { font-size: 14px; font-weight: 600; }
            .highlight-desc { font-size: 12px; fill: #475569; }
        </style>
    </defs>

    <rect width="100%" height="100%" fill="url(#bg-gradient)" />

    <!-- Header -->
    <text x="900" y="60" text-anchor="middle" class="font-sans title">并发HTML报告生成与智能合并架构</text>
    <text x="900" y="85" text-anchor="middle" class="font-sans subtitle">高性能、高可靠的自动化报告流水线</text>

    <!-- Key Highlights -->
    <g transform="translate(290, 120)" class="font-sans">
        <g transform="translate(0, 0)">
            <rect x="0" y="0" width="380" height="80" rx="12" fill="#ffffff" filter="url(#card-shadow)"/>
            <path d="M15 4.5a.75.75 0 00-1.5 0v6a.75.75 0 001.5 0v-6zM10.5 6a.75.75 0 00-1.5 0v3a.75.75 0 001.5 0v-3zM6 7.5a.75.75 0 00-1.5 0v.75a.75.75 0 001.5 0v-.75z" fill="#3b82f6" transform="translate(20, 25) scale(1.5)"/>
            <text x="60" y="25" class="highlight-title" fill="#1e40af">高性能并发</text>
            <text x="60" y="45" class="highlight-desc">`RunnableParallel` 并发执行5个章节</text>
            <text x="60" y="60" class="highlight-desc">速度提升约5倍，耗时取决于最慢的链</text>
        </g>
        <g transform="translate(420, 0)">
            <rect x="0" y="0" width="380" height="80" rx="12" fill="#ffffff" filter="url(#card-shadow)"/>
            <path d="M14.25 7.5a4.5 4.5 0 00-8.61-1.973.75.75 0 01-1.292-.752 6 6 0 0111.204 2.725.75.75 0 01-1.292-.752A4.5 4.5 0 0014.25 7.5z M3.75 10.5a4.5 4.5 0 008.61 1.973.75.75 0 011.292.752 6 6 0 01-11.204-2.725.75.75 0 011.292.752A4.5 4.5 0 003.75 10.5z" fill="#8b5cf6" transform="translate(20, 25) scale(1.5)"/>
            <text x="60" y="25" class="highlight-title" fill="#5b21b6">智能合并策略</text>
            <text x="60" y="45" class="highlight-desc">在LLM与规则间自适应选择</text>
            <text x="60" y="60" class="highlight-desc">失败时自动降级，确保交付不中断</text>
        </g>
        <g transform="translate(840, 0)">
            <rect x="0" y="0" width="380" height="80" rx="12" fill="#ffffff" filter="url(#card-shadow)"/>
            <path d="M9.25 2.25a.75.75 0 000 1.5h.5a.75.75 0 000-1.5h-.5zM6.75 6a.75.75 0 01.75-.75h4.5a.75.75 0 010 1.5h-4.5a.75.75 0 01-.75-.75zM6 10.5a.75.75 0 01.75-.75h7.5a.75.75 0 010 1.5h-7.5a.75.75 0 01-.75-.75zM3 2.25a.75.75 0 01.75-.75h1.5a.75.75 0 010 1.5h-1.5a.75.75 0 01-.75-.75z" fill="#10b981" transform="translate(20, 25) scale(1.5)"/>
            <text x="60" y="25" class="highlight-title" fill="#047857">健壮性设计</text>
            <text x="60" y="45" class="highlight-desc">`HtmlChainFactory` 统一创建与配置</text>
            <text x="60" y="60" class="highlight-desc">多层Fallback机制，确保稳定交付</text>
        </g>
    </g>

    <!-- Main Flow -->
    <g class="font-sans">
        <!-- Input -->
        <g transform="translate(725, 240)">
            <rect x="0" y="0" width="350" height="60" rx="8" fill="#ffffff" filter="url(#card-shadow)" />
            <text x="175" y="25" text-anchor="middle" class="component-title" fill="#1e293b">Input: ReasoningState</text>
            <text x="175" y="45" text-anchor="middle" class="component-desc">包含完整诊断上下文 (final_report, observations)</text>
        </g>
        <path d="M900 300 V 340" class="flow-line" />

        <!-- Stage 1: Concurrent Generation -->
        <g transform="translate(100, 340)">
            <rect x="0" y="0" width="1600" height="240" rx="16" fill="#eff6ff" />
            <text x="800" y="40" text-anchor="middle" class="stage-title" fill="#1d4ed8">① 并发生成层 (Concurrent Generation)</text>
            <g transform="translate(600, 70)">
                <rect x="0" y="0" width="400" height="40" rx="8" fill="#dbeafe" />
                <text x="200" y="25" text-anchor="middle" class="component-title" fill="#1e3a8a">ParallelHtmlGeneratorAgent</text>
            </g>
            <text x="800" y="125" text-anchor="middle" class="annotation">`RunnableParallel` 扇出 (Fan-out) 5个并行的生成链</text>

            <!-- 5 Parallel Chains with new curved connectors -->
            <path d="M800,110 C 450,110 450,150 225,150" class="flow-line" stroke="#93c5fd"/>
            <path d="M800,110 C 600,110 600,150 475,150" class="flow-line" stroke="#93c5fd"/>
            <path d="M800,110 C 800,110 800,150 725,150" class="flow-line" stroke="#93c5fd"/>
            <path d="M800,110 C 1000,110 1000,150 975,150" class="flow-line" stroke="#93c5fd"/>
            <path d="M800,110 C 1150,110 1150,150 1225,150" class="flow-line" stroke="#93c5fd"/>

            <g transform="translate(125, 150)">
                <rect x="0" y="0" width="200" height="60" rx="8" fill="#ffffff" filter="url(#card-shadow)"/>
                <text x="100" y="25" text-anchor="middle" class="component-title" fill="#374151">问题描述</text>
                <text x="100" y="45" text-anchor="middle" class="component-desc">Template | LLM | Parser</text>
            </g>
            <g transform="translate(375, 150)">
                <rect x="0" y="0" width="200" height="60" rx="8" fill="#ffffff" filter="url(#card-shadow)"/>
                <text x="100" y="25" text-anchor="middle" class="component-title" fill="#374151">诊断分析</text>
                <text x="100" y="45" text-anchor="middle" class="component-desc">Template | LLM | Parser</text>
            </g>
            <g transform="translate(625, 150)">
                <rect x="0" y="0" width="200" height="60" rx="8" fill="#ffffff" filter="url(#card-shadow)"/>
                <text x="100" y="25" text-anchor="middle" class="component-title" fill="#374151">关键发现</text>
                <text x="100" y="45" text-anchor="middle" class="component-desc">Template | LLM | Parser</text>
            </g>
            <g transform="translate(875, 150)">
                <rect x="0" y="0" width="200" height="60" rx="8" fill="#ffffff" filter="url(#card-shadow)"/>
                <text x="100" y="25" text-anchor="middle" class="component-title" fill="#374151">支撑证据</text>
                <text x="100" y="45" text-anchor="middle" class="component-desc">Template | LLM | Parser</text>
            </g>
            <g transform="translate(1125, 150)">
                <rect x="0" y="0" width="200" height="60" rx="8" fill="#ffffff" filter="url(#card-shadow)"/>
                <text x="100" y="25" text-anchor="middle" class="component-title" fill="#374151">总结建议</text>
                <text x="100" y="45" text-anchor="middle" class="component-desc">Template | LLM | Parser</text>
            </g>
        </g>
        <path d="M800 580 V 620" class="flow-line" />
        <text x="810" y="605" class="annotation">Output: 5个独立的HTML片段</text>

        <!-- Stage 2: Intelligent Merging -->
        <g transform="translate(100, 620)">
            <rect x="0" y="0" width="1600" height="220" rx="16" fill="#f5f3ff" />
            <text x="800" y="40" text-anchor="middle" class="stage-title" fill="#5b21b6">② 智能合并层 (Intelligent Merging)</text>
            <g transform="translate(600, 70)">
                <rect x="0" y="0" width="400" height="120" rx="8" fill="#ffffff" filter="url(#card-shadow)" />
                <text x="200" y="20" text-anchor="middle" class="component-title" fill="#5b21b6">SmartHtmlMergeStrategy</text>
                <path d="M 100 40 L 300 40 L 200 60 Z" fill="#ede9fe" stroke="#ddd6fe"/>
                <text x="200" y="53" text-anchor="middle" class="component-desc">内容是否复杂? (阈值判断)</text>
                <path d="M125 60 V 80" class="flow-line" />
                <text x="115" y="75" text-anchor="end" class="component-desc">否</text>
                <text x="125" y="95" text-anchor="middle" class="component-desc">规则化合并</text>
                <path d="M275 60 V 80" class="flow-line" />
                <text x="285" y="75" text-anchor="start" class="component-desc">是</text>
                <text x="275" y="95" text-anchor="middle" class="component-desc">LLM智能合并</text>
            </g>
            <text x="1050" y="135" class="annotation">LLM失败时自动降级 (Fallback)</text>
        </g>
    </g>
</svg>