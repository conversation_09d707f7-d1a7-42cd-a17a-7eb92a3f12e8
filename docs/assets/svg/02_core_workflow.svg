<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="900" viewBox="0 0 1000 900" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 温和的渐变色彩定义 -->
    <linearGradient id="startGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#22c55e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#16a34a;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="processGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2563eb;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="agentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="decisionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="endGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0891b2;stop-opacity:1" />
    </linearGradient>
    
    <!-- 柔和阴影滤镜 -->
    <filter id="softShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
    
    <!-- 现代箭头标记 -->
    <marker id="arrowhead" markerWidth="8" markerHeight="6" 
            refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#6b7280" />
    </marker>
    
    <!-- 虚线箭头 -->
    <marker id="dashedArrow" markerWidth="8" markerHeight="6" 
            refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#94a3b8" />
    </marker>
  </defs>
  
  <!-- 清爽背景 -->
  <rect width="1000" height="900" fill="#f8fafc"/>
  
  <!-- 标题区域 -->
  <rect x="50" y="30" width="900" height="70" rx="16" fill="white" 
        stroke="#e2e8f0" stroke-width="1" filter="url(#softShadow)"/>
  <text x="500" y="55" text-anchor="middle" font-family="Inter, -apple-system, sans-serif" 
        font-size="26" font-weight="700" fill="#1e293b">核心工作流程</text>
  <text x="500" y="80" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="14" font-weight="400" fill="#64748b">智能诊断多智能体协作流程</text>
  
  <!-- 第一阶段：输入与协调 -->
  <rect x="100" y="140" width="800" height="100" rx="12" fill="#f1f5f9" 
        stroke="#cbd5e1" stroke-width="1" opacity="0.5"/>
  <text x="120" y="165" font-family="Inter, sans-serif" font-size="14" font-weight="600" fill="#475569">
    阶段一：输入与协调</text>
  
  <!-- 用户输入 -->
  <circle cx="200" cy="190" r="25" fill="url(#startGradient)" filter="url(#softShadow)"/>
  <text x="200" y="195" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="11" font-weight="600" fill="white">用户输入</text>
  
  <!-- 协调器 -->
  <rect x="300" y="165" width="120" height="50" rx="10" fill="url(#processGradient)" filter="url(#softShadow)"/>
  <text x="360" y="185" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="12" font-weight="600" fill="white">协调器</text>
  <text x="360" y="200" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="9" font-weight="400" fill="white">Coordinator</text>
  
  <!-- 决策点 -->
  <path d="M 500 190 L 530 165 L 560 190 L 530 215 Z" fill="url(#decisionGradient)" filter="url(#softShadow)"/>
  <text x="530" y="185" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="9" font-weight="500" fill="white">需要深度</text>
  <text x="530" y="195" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="9" font-weight="500" fill="white">诊断?</text>
  
  <!-- 直接回复 -->
  <rect x="650" y="165" width="100" height="50" rx="10" fill="url(#endGradient)" filter="url(#softShadow)"/>
  <text x="700" y="185" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="11" font-weight="600" fill="white">直接回复</text>
  <text x="700" y="200" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="9" font-weight="400" fill="white">简单问题</text>
  
  <!-- 第二阶段：背景调查与规划 -->
  <rect x="100" y="280" width="800" height="100" rx="12" fill="#fef7f0" 
        stroke="#fed7aa" stroke-width="1" opacity="0.5"/>
  <text x="120" y="305" font-family="Inter, sans-serif" font-size="14" font-weight="600" fill="#c2410c">
    阶段二：背景调查与规划</text>
  
  <!-- 背景调查员 -->
  <rect x="200" y="320" width="120" height="50" rx="10" fill="url(#agentGradient)" filter="url(#softShadow)"/>
  <text x="260" y="340" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="11" font-weight="600" fill="white">背景调查员</text>
  <text x="260" y="355" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="9" font-weight="400" fill="white">Background Investigator</text>
  
  <!-- 规划器 -->
  <rect x="380" y="320" width="120" height="50" rx="10" fill="url(#processGradient)" filter="url(#softShadow)"/>
  <text x="440" y="340" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="12" font-weight="600" fill="white">规划器</text>
  <text x="440" y="355" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="9" font-weight="400" fill="white">Planner</text>
  
  <!-- 人工反馈 -->
  <rect x="560" y="320" width="120" height="50" rx="10" fill="url(#decisionGradient)" filter="url(#softShadow)"/>
  <text x="620" y="340" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="11" font-weight="600" fill="white">人工反馈</text>
  <text x="620" y="355" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="9" font-weight="400" fill="white">Human Feedback</text>
  
  <!-- 第三阶段：执行与研究 -->
  <rect x="100" y="420" width="800" height="120" rx="12" fill="#f0f9ff" 
        stroke="#bae6fd" stroke-width="1" opacity="0.5"/>
  <text x="120" y="445" font-family="Inter, sans-serif" font-size="14" font-weight="600" fill="#0369a1">
    阶段三：执行与研究</text>
  
  <!-- 研究团队 -->
  <rect x="200" y="470" width="120" height="50" rx="10" fill="url(#startGradient)" filter="url(#softShadow)"/>
  <text x="260" y="490" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="11" font-weight="600" fill="white">研究团队</text>
  <text x="260" y="505" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="9" font-weight="400" fill="white">Research Team</text>
  
  <!-- 研究员 -->
  <rect x="380" y="470" width="100" height="50" rx="10" fill="url(#agentGradient)" filter="url(#softShadow)"/>
  <text x="430" y="490" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="11" font-weight="600" fill="white">研究员</text>
  <text x="430" y="505" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="9" font-weight="400" fill="white">Researcher</text>
  
  <!-- 编程员 -->
  <rect x="520" y="470" width="100" height="50" rx="10" fill="url(#processGradient)" filter="url(#softShadow)"/>
  <text x="570" y="490" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="11" font-weight="600" fill="white">编程员</text>
  <text x="570" y="505" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="9" font-weight="400" fill="white">Coder</text>
  
  <!-- 第四阶段：报告生成 -->
  <rect x="100" y="580" width="800" height="100" rx="12" fill="#f0fdf4" 
        stroke="#bbf7d0" stroke-width="1" opacity="0.5"/>
  <text x="120" y="605" font-family="Inter, sans-serif" font-size="14" font-weight="600" fill="#15803d">
    阶段四：报告生成</text>
  
  <!-- 报告员 -->
  <rect x="300" y="620" width="120" height="50" rx="10" fill="url(#endGradient)" filter="url(#softShadow)"/>
  <text x="360" y="640" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="12" font-weight="600" fill="white">报告员</text>
  <text x="360" y="655" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="9" font-weight="400" fill="white">Reporter</text>
  
  <!-- 最终报告 -->
  <circle cx="580" cy="645" r="25" fill="url(#startGradient)" filter="url(#softShadow)"/>
  <text x="580" y="640" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="10" font-weight="600" fill="white">最终</text>
  <text x="580" y="652" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="10" font-weight="600" fill="white">报告</text>
  
  <!-- 流程箭头 -->
  <!-- 主流程 -->
  <path d="M 225 190 L 290 190" stroke="#6b7280" stroke-width="2" 
        marker-end="url(#arrowhead)" fill="none"/>
  <path d="M 420 190 L 490 190" stroke="#6b7280" stroke-width="2" 
        marker-end="url(#arrowhead)" fill="none"/>
  
  <!-- 分支：直接回复 -->
  <path d="M 560 180 L 640 180" stroke="#94a3b8" stroke-width="2" stroke-dasharray="5,5"
        marker-end="url(#dashedArrow)" fill="none"/>
  <text x="600" y="175" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="10" fill="#64748b">否</text>
  
  <!-- 深度诊断流程 -->
  <path d="M 530 215 L 530 250 L 260 250 L 260 310" stroke="#6b7280" stroke-width="2" 
        marker-end="url(#arrowhead)" fill="none"/>
  <text x="540" y="235" font-family="Inter, sans-serif" font-size="10" fill="#22c55e">是</text>
  
  <path d="M 320 345 L 370 345" stroke="#6b7280" stroke-width="2" 
        marker-end="url(#arrowhead)" fill="none"/>
  <path d="M 500 345 L 550 345" stroke="#6b7280" stroke-width="2" 
        marker-end="url(#arrowhead)" fill="none"/>
  
  <!-- 反馈循环 -->
  <path d="M 620 320 L 620 300 L 440 300 L 440 320" stroke="#94a3b8" stroke-width="2" stroke-dasharray="3,3"
        marker-end="url(#dashedArrow)" fill="none"/>
  <text x="530" y="295" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="9" fill="#64748b">修改计划</text>
  
  <!-- 执行阶段 -->
  <path d="M 260 370 L 260 460" stroke="#6b7280" stroke-width="2" 
        marker-end="url(#arrowhead)" fill="none"/>
  <path d="M 320 495 L 370 495" stroke="#6b7280" stroke-width="2" 
        marker-end="url(#arrowhead)" fill="none"/>
  <path d="M 480 495 L 510 495" stroke="#6b7280" stroke-width="2" 
        marker-end="url(#arrowhead)" fill="none"/>
  
  <!-- 报告生成 -->
  <path d="M 430 520 L 430 560 L 360 560 L 360 610" stroke="#6b7280" stroke-width="2" 
        marker-end="url(#arrowhead)" fill="none"/>
  <path d="M 420 645 L 550 645" stroke="#6b7280" stroke-width="2" 
        marker-end="url(#arrowhead)" fill="none"/>
  
  <!-- 图例 -->
  <rect x="50" y="720" width="900" height="120" rx="12" fill="white" 
        stroke="#e2e8f0" stroke-width="1" filter="url(#softShadow)"/>
  <text x="80" y="745" font-family="Inter, sans-serif" font-size="16" font-weight="600" fill="#1e293b">
    流程说明</text>
  
  <!-- 图例项目 -->
  <rect x="80" y="760" width="15" height="15" rx="3" fill="url(#startGradient)"/>
  <text x="105" y="772" font-family="Inter, sans-serif" font-size="12" fill="#475569">
    开始/结束节点</text>
  
  <rect x="220" y="760" width="15" height="15" rx="3" fill="url(#processGradient)"/>
  <text x="245" y="772" font-family="Inter, sans-serif" font-size="12" fill="#475569">
    处理节点</text>
  
  <rect x="340" y="760" width="15" height="15" rx="3" fill="url(#agentGradient)"/>
  <text x="365" y="772" font-family="Inter, sans-serif" font-size="12" fill="#475569">
    智能体节点</text>
  
  <path d="M 480 767 L 520 767" stroke="#6b7280" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="530" y="772" font-family="Inter, sans-serif" font-size="12" fill="#475569">
    主流程</text>
  
  <path d="M 620 767 L 660 767" stroke="#94a3b8" stroke-width="2" stroke-dasharray="3,3" marker-end="url(#dashedArrow)"/>
  <text x="670" y="772" font-family="Inter, sans-serif" font-size="12" fill="#475569">
    分支/反馈</text>
  
  <text x="80" y="800" font-family="Inter, sans-serif" font-size="11" fill="#64748b">
    • 系统根据问题复杂度自动选择处理路径</text>
  <text x="80" y="815" font-family="Inter, sans-serif" font-size="11" fill="#64748b">
    • 支持人工反馈和计划调整，确保诊断准确性</text>
  
</svg>