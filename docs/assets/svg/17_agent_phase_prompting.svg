 <svg xmlns="http://www.w3.org/2000/svg" width="1400" height="1050" viewBox="0 0 1400 1050">
<rect x="50" y="50" width="1300" height="150" fill="#FFFBEB" stroke="#FDBA74" stroke-width="2" rx="10"/>
<text x="70" y="90" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="#333333">ReAct Agent：观察-思考-行动循环</text>
<text x="70" y="130" font-family="Arial, sans-serif" font-size="18" fill="#555555">ReAct (Reasoning and Acting) 代理通过交替进行推理（思考）和行动（工具使用）来解决复杂任务。它模拟人类解决问题的过程，通过观察工具结果来更新内部状态并规划下一步行动。</text>
  <defs>
    <!-- Gradients -->
    <linearGradient id="decisionGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" stop-color="#3b82f6"/><stop offset="100%" stop-color="#1d4ed8"/></linearGradient>
    <linearGradient id="selectGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" stop-color="#10b981"/><stop offset="100%" stop-color="#059669"/></linearGradient>
    <linearGradient id="finalGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" stop-color="#8b5cf6"/><stop offset="100%" stop-color="#7c3aed"/></linearGradient>
    <linearGradient id="toolGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" stop-color="#f97316"/><stop offset="100%" stop-color="#ea580c"/></linearGradient>
    <linearGradient id="outputGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" stop-color="#64748b"/><stop offset="100%" stop-color="#475569"/></linearGradient>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="0%" y2="100%"><stop offset="0%" stop-color="#f8fafc"/><stop offset="100%" stop-color="#f1f5f9"/></linearGradient>

    <!-- Styles -->
    <style>
      .bg { fill: url(#bgGradient); }
      .title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 32px; font-weight: 700; fill: #000000; text-anchor: middle; }
      .box { fill: white; stroke: #e2e8f0; stroke-width: 1.5; rx: 12; filter: url(#subtleShadow); }
      .group-title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 18px; font-weight: 600; fill: white; }
      .group-subtitle { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 14px; fill: white; opacity: 0.9; }
      .text-content { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 14px; fill: #000000; }
      .text-content-bold { font-weight: 600; }
      .text-content-light { fill: #000000; font-size: 14px; font-weight: 500; }
      .phase-label { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 16px; font-weight: 700; text-anchor: middle; }
      .arrow { stroke: #334155; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .feedback-arrow { stroke: #c2185b; stroke-width: 3.5; fill: none; marker-end: url(#arrowhead-feedback); stroke-dasharray: 8, 4; }
    </style>

    <!-- Markers & Filters -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto"><polygon points="0 0, 10 3.5, 0 7" fill="#334155"/></marker>
    <marker id="arrowhead-feedback" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto"><polygon points="0 0, 10 3.5, 0 7" fill="#c2185b"/></marker>
    <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%"><feDropShadow dx="0" dy="5" stdDeviation="10" flood-color="#1e293b" flood-opacity="0.1"/></filter>
    <filter id="subtleShadow" x="-20%" y="-20%" width="140%" height="140%"><feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#1e293b" flood-opacity="0.06"/></filter>
  </defs>

  <!-- Background -->
  <rect width="100%" height="100%" class="bg"/>

  <!-- Title -->
  <text class="title" x="700" y="60">ReAct Agent 阶段化提示设计</text>

  <!-- Nodes -->
  <g id="nodes">
    <!-- Top: Input -->
    <g transform="translate(525, 120)">
      <rect class="box" width="350" height="120"/>
      <text class="group-title" x="25" y="35" fill="#000000" font-weight="600">AgentState (输入/状态)</text>
      <text class="text-content" x="25" y="65">messages: 完整消息历史</text>
      <text class="text-content" x="25" y="90">configurable: 其他SOP、地区配置</text>
    </g>

    <!-- Center: Decision -->
    <g transform="translate(475, 300)">
      <rect width="450" height="170" rx="12" fill="url(#decisionGradient)" filter="url(#dropShadow)"/>
      <text class="group-title" x="30" y="40">阶段判定算法 (核心决策)</text>
      <text class="group-subtitle" x="30" y="65">根据最新工具消息 (ToolMessage) 决定下一阶段</text>
      <text class="text-content" fill="white" x="30" y="95">• 无或全空工具消息 → <tspan class="text-content-bold">SELECT</tspan> 阶段</text>
      <text class="text-content" fill="white" x="30" y="120" xml:space="preserve">  (当返回全为空时，注入空状态)</text>
      <text class="text-content" fill="white" x="30" y="145">• 工具返回非空数据 → <tspan class="text-content-bold">FINAL</tspan> 阶段</text>
    </g>

    <!-- Left Branch: Tool Selection & Execution -->
    <g transform="translate(100, 530)">
      <rect width="500" height="220" rx="12" fill="url(#selectGradient)" filter="url(#dropShadow)"/>
      <text class="group-title" x="30" y="40">SELECT (工具选择) 阶段</text>
      <text class="group-subtitle" x="30" y="65">提示模板: {agent}_select.md</text>
      <text class="text-content" fill="white" x="30" y="100"><tspan class="text-content-bold">注入变量:</tspan></text>
      <text class="text-content" fill="white" x="45" y="125">• 可用工具 (mcp_servers_description)</text>
      <text class="text-content" fill="white" x="45" y="150">• 上轮是否空返回 (last_tool_output_empty)</text>
      <text class="text-content" fill="white" x="45" y="175">• 连续空返回次数 (last_tool_empty_count)</text>
      <text class="group-subtitle" x="30" y="205" style="font-weight:600;">输出: 并发的工具调用 (tool_calls)</text>
    </g>
    <g transform="translate(100, 800)">
      <rect width="500" height="120" rx="12" fill="url(#toolGradient)" filter="url(#dropShadow)"/>
      <text class="group-title" x="30" y="40">工具执行层 (MCP Tools)</text>
      <text class="group-subtitle" x="30" y="65">执行工具调用，并对结果进行规范化处理</text>
      <text class="text-content" fill="white" x="30" y="95">• 统一空数据格式，确保一致性</text>
    </g>

    <!-- Right Branch: Final Generation -->
    <g transform="translate(800, 530)">
      <rect width="500" height="220" rx="12" fill="url(#finalGradient)" filter="url(#dropShadow)"/>
      <text class="group-title" x="30" y="40">FINAL (最终生成) 阶段</text>
      <text class="group-subtitle" x="30" y="65">提示模板: {agent}_final.md</text>
      <text class="text-content" fill="white" x="30" y="100"><tspan class="text-content-bold">注入变量:</tspan></text>
      <text class="text-content" fill="white" x="45" y="125">• 近期工具证据 (recent_tool_evidence)</text>
      <text class="text-content" fill="white" x="45" y="150">• 是否曾有空输出 (has_empty_tool_output)</text>
      <text class="text-content" fill="white" x="45" y="175">• 总计空输出次数 (empty_tool_output_count)</text>
      <text class="group-subtitle" x="30" y="205" style="font-weight:600;">输出: 结构化结论 (禁止调用工具)</text>
    </g>
    <g transform="translate(800, 800)">
      <rect width="500" height="120" rx="12" fill="url(#outputGradient)" filter="url(#dropShadow)"/>
      <text class="group-title" x="30" y="40">流程结束</text>
      <text class="group-subtitle" x="30" y="65">输出最终的结构化JSON结论</text>
    </g>
  </g>

  <!-- Arrows -->
  <g id="arrows">
    <!-- Main Flow -->
    <path class="arrow" d="M 700 240 V 300"/>
    <text class="text-content-light" x="735" y="275">输入状态</text>
    
    <path class="arrow" d="M 700 470 C 700 480 650 500 600 530" />
    <text class="phase-label" fill="#059669" x="500" y="505">工具选择</text>
    
    <path class="arrow" d="M 700 470 C 700 480 750 500 800 530"/>
    <text class="phase-label" fill="#7c3aed" x="900" y="505">最终生成</text>

    <path class="arrow" d="M 350 750 V 800"/>
    <text class="text-content-light" x="385" y="780">执行</text>
    <path class="arrow" d="M 1050 750 V 800"/>
    <text class="text-content-light" x="1085" y="780">输出</text>

    <!-- Feedback Loop -->
    <path class="feedback-arrow" d="M 100 860 C -50 860, -50 180, 525 180"/>
    <text class="text-content-light" x="200" y="480" text-anchor="middle">反馈循环：工具结果更新AgentState，开始新一轮判定</text>
  </g>
</svg>