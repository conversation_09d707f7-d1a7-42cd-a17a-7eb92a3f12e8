<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="1350" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 优化后的配色方案 -->
    <linearGradient id="grad-header" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#3b82f6;"/><stop offset="100%" style="stop-color:#1d4ed8;"/></linearGradient>
    <linearGradient id="grad-roof" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#3b82f6;"/><stop offset="100%" style="stop-color:#1d4ed8;"/></linearGradient>
    <linearGradient id="grad-interface" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#10b981;"/><stop offset="100%" style="stop-color:#059669;"/></linearGradient>
    <linearGradient id="grad-agent" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#8b5cf6;"/><stop offset="100%" style="stop-color:#7c3aed;"/></linearGradient>
    <linearGradient id="grad-foundation" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#64748b;"/><stop offset="100%" style="stop-color:#475569;"/></linearGradient>
    <radialGradient id="grad-sky" cx="50%" cy="20%" r="80%"><stop offset="0%" style="stop-color:#f8fafc;"/><stop offset="100%" style="stop-color:#eef2ff;"/></radialGradient>
    <linearGradient id="grad-highlight" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#f97316;"/><stop offset="100%" style="stop-color:#f59e0b;"/></linearGradient>
    <linearGradient id="grad-foundation-group" x1="0%" y1="0%" x2="0%" y2="100%"><stop offset="0%" style="stop-color:rgba(255,255,255,0.08)" /><stop offset="100%" style="stop-color:rgba(255,255,255,0.02)" /></linearGradient>

    <!-- 更细腻的阴影效果 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="3" dy="3" stdDeviation="5" flood-color="#1e2d3b" flood-opacity="0.1"/>
    </filter>
    <filter id="strong-shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="5" dy="5" stdDeviation="8" flood-color="#1e2d3b" flood-opacity="0.15"/>
    </filter>

    <!-- 定义箭头样式 -->
    <marker id="arrow" viewBox="0 0 10 10" refX="8" refY="5" markerWidth="6" markerHeight="6" orient="auto-start-reverse">
        <path d="M 0 0 L 10 5 L 0 10 z" fill="#6d28d9" />
    </marker>
     <marker id="arrow-blue" viewBox="0 0 10 10" refX="8" refY="5" markerWidth="6" markerHeight="6" orient="auto-start-reverse">
        <path d="M 0 0 L 10 5 L 0 10 z" fill="#3b82f6" />
    </marker>
  </defs>

  <!-- 整体容器与背景 -->
  <g>
    <rect x="0" y="0" width="1800" height="1350" fill="url(#grad-sky)"/>

    <!-- 装饰元素 -->
    <g opacity="0.6">
        <ellipse cx="200" cy="150" rx="60" ry="35" fill="white"/>
        <ellipse cx="230" cy="140" rx="45" ry="28" fill="white"/>
        <ellipse cx="1600" cy="170" rx="70" ry="40" fill="white"/>
        <ellipse cx="1635" cy="160" rx="50" ry="30" fill="white"/>
        <circle cx="1550" cy="120" r="40" fill="#fde047" opacity="0.3"/>
        <circle cx="1550" cy="120" r="30" fill="#fde047" opacity="0.4"/>
    </g>

    <!-- 标题栏 -->
    <rect x="0" y="0" width="1800" height="70" fill="url(#grad-header)" filter="url(#strong-shadow)"/>
    <text x="900" y="42" text-anchor="middle" fill="white" font-size="28" font-weight="bold" letter-spacing="1" font-family="'Inter', 'Segoe UI', sans-serif">CloudBot 智能体技术架构</text>

    <!-- 主体架构容器 -->
    <g transform="translate(100, -10)">

        <!-- 业务层 (屋顶) -->
        <g id="business-roof">
          <polygon points="50,380 350,110 1250,110 1550,380" fill="url(#grad-roof)" filter="url(#strong-shadow)"/>
          <text x="800" y="150" text-anchor="middle" fill="white" font-size="26" font-weight="bold" font-family="'Inter', 'Segoe UI', sans-serif">业务层 - 核心业务目标</text>

          <rect x="420" y="180" width="760" height="30" fill="rgba(255,255,255,0.9)" rx="15" stroke="#93c5fd" stroke-width="1"/>
          <text x="800" y="200" text-anchor="middle" fill="#1e40af" font-size="14" font-weight="bold" font-family="'Inter', 'Segoe UI', sans-serif">🎯 总体目标: 通过AI智能体实现运维服务无人化、诊断服务规模化、测试流程智能化</text>

          <g transform="translate(0, 10)">
            <!-- Box 1: 工单自动化 -->
            <g transform="translate(-15, 0)">
              <rect x="250" y="240" width="350" height="120" fill="white" rx="12" stroke="#3b82f6" stroke-width="2" filter="url(#shadow)"/>
              <text x="425" y="265" text-anchor="middle" fill="#1d4ed8" font-size="18" font-weight="bold">工单自动化</text>
              <line x1="270" y1="280" x2="580" y2="280" stroke="#e2e8f0" stroke-width="1.5"/>
              <text x="265" y="298" fill="#334155" font-size="12" font-weight="bold">技术指标:</text>
              <text x="275" y="315" fill="#475569" font-size="11">• 诊断准确率: <tspan style="fill:#1d4ed8; font-weight:bold;">40% → 60%</tspan></text>
              <text x="435" y="315" fill="#475569" font-size="11">• 根因覆盖率: <tspan style="fill:#1d4ed8; font-weight:bold;">>80%</tspan></text>
              <text x="275" y="332" fill="#475569" font-size="11">• 分类/任务拆解准确率: <tspan style="fill:#1d4ed8; font-weight:bold;">>X%</tspan></text>
              <text x="275" y="349" fill="#475569" font-size="11">• 知识库/SOP覆盖率: <tspan style="fill:#1d4ed8; font-weight:bold;">>X%</tspan></text>
            </g>

            <!-- Box 2: 智能诊断助手 -->
            <g transform="translate(0, 0)">
              <rect x="625" y="240" width="350" height="120" fill="white" rx="12" stroke="#3b82f6" stroke-width="2" filter="url(#shadow)"/>
              <text x="800" y="265" text-anchor="middle" fill="#1d4ed8" font-size="18" font-weight="bold">智能诊断助手</text>
              <line x1="645" y1="280" x2="955" y2="280" stroke="#e2e8f0" stroke-width="1.5"/>
              <text x="640" y="298" fill="#334155" font-size="12" font-weight="bold">关键指标: • 日均UV <tspan style="fill:#1d4ed8; font-weight:bold;">10倍+ (500+)</tspan></text>
              <text x="640" y="315" fill="#334155" font-size="12" font-weight="bold">关键能力 (Milestones):</text>
              <text x="650" y="332" fill="#475569" font-size="11">• M1/M4: DingOps &amp; 交互式诊断</text>
              <text x="650" y="349" fill="#475569" font-size="11">• M2/M3: CIPU &amp; 长推理/报告</text>
            </g>

            <!-- Box 3: 测试智能化 -->
            <g transform="translate(15, 0)">
              <rect x="1000" y="240" width="350" height="120" fill="white" rx="12" stroke="#3b82f6" stroke-width="2" filter="url(#shadow)"/>
              <text x="1175" y="265" text-anchor="middle" fill="#1d4ed8" font-size="18" font-weight="bold">测试智能化</text>
              <line x1="1020" y1="280" x2="1330" y2="280" stroke="#e2e8f0" stroke-width="1.5"/>
              <text x="1015" y="300" fill="#b45309" font-size="12" font-weight="bold">价值: • 赋能测试, AI智能生成</text>
              <text x="1015" y="320" fill="#334155" font-size="12" font-weight="bold">关键指标:</text>
              <text x="1025" y="340" fill="#475569" font-size="12">• 自动化覆盖率: <tspan style="fill:#1d4ed8; font-weight:bold;">+30%</tspan></text>
              <text x="1185" y="340" fill="#475569" font-size="12">• 测试构建效率: <tspan style="fill:#1d4ed8; font-weight:bold;">+50%</tspan></text>
            </g>
          </g>
        </g>

        <path d="M 800 380 V 395" stroke="#9ca3af" stroke-width="2" stroke-dasharray="4 4"/>

        <!-- 接口层 (上层) -->
        <g id="interface-floor">
          <rect x="50" y="395" width="1500" height="265" fill="url(#grad-interface)" rx="15" filter="url(#strong-shadow)"/>
          <text x="800" y="425" text-anchor="middle" fill="white" font-size="24" font-weight="bold" font-family="'Inter', 'Segoe UI', sans-serif">接口层 - 智能融入各大平台</text>

          <g>
              <!-- Box 1: 钉钉DingOps -->
              <g transform="translate(-74, 0)">
                <rect x="200" y="445" width="280" height="200" fill="white" rx="10" stroke="#10b981" stroke-width="2" filter="url(#shadow)"/>
                <text x="340" y="470" text-anchor="middle" fill="#059669" font-size="16" font-weight="bold">钉钉DingOps</text>
                <text x="215" y="490" fill="#b45309" font-size="11">价值: • 移动端接入，扩大用户群体</text>
                <text x="215" y="510" fill="#334155" font-size="12" font-weight="bold">关键指标:</text>
                <text x="225" y="527" fill="#475569" font-size="11">• First Token 延迟: &lt;10s</text>
                <text x="225" y="542" fill="#475569" font-size="11">• 日均使用量 > 50</text>
                <rect x="215" y="560" width="250" height="25" fill="#f0fdf4" rx="5" stroke="#a7f3d0" stroke-width="1"/><text x="340" y="576" text-anchor="middle" fill="#065f46" font-size="10" font-weight="bold">故障、异常诊断场景接入</text>
                <rect x="215" y="590" width="250" height="25" fill="#f0fdf4" rx="5" stroke="#a7f3d0" stroke-width="1"/><text x="340" y="606" text-anchor="middle" fill="#065f46" font-size="10" font-weight="bold">CloudBot诊断用户群聊</text>
                <rect x="215" y="620" width="250" height="25" fill="#f0fdf4" rx="5" stroke="#a7f3d0" stroke-width="1"/><text x="340" y="636" text-anchor="middle" fill="#065f46" font-size="10" font-weight="bold">工单和线上问题通知闭环能力</text>
              </g>

              <!-- Box 2: Aone平台集成 -->
              <g transform="translate(-18, 0)">
                <rect x="500" y="445" width="280" height="200" fill="white" rx="10" stroke="#10b981" stroke-width="2" filter="url(#shadow)"/>
                <text x="640" y="470" text-anchor="middle" fill="#059669" font-size="16" font-weight="bold">Aone平台集成</text>
                <text x="515" y="490" fill="#b45309" font-size="11">价值: • 实时工单信息推送能力</text>
                <text x="515" y="510" fill="#334155" font-size="12" font-weight="bold">关键指标:</text>
                <text x="525" y="527" fill="#475569" font-size="11">• Aone 推送场景：&gt; 3个</text>
                <rect x="515" y="575" width="250" height="28" fill="#f0fdf4" rx="5" stroke="#a7f3d0" stroke-width="1"/><text x="640" y="592" text-anchor="middle" fill="#065f46" font-size="10" font-weight="bold">Aone 自动Comment 能力</text>
                <rect x="515" y="610" width="250" height="28" fill="#f0fdf4" rx="5" stroke="#a7f3d0" stroke-width="1"/><text x="640" y="627" text-anchor="middle" fill="#065f46" font-size="10" font-weight="bold">Aone @CloudBot自动回复能力</text>
              </g>

              <!-- Box 3: AES客户端智能诊断 -->
              <g transform="translate(38, 0)">
                <rect x="800" y="445" width="280" height="200" fill="white" rx="10" stroke="#10b981" stroke-width="2" filter="url(#shadow)"/>
                <text x="940" y="470" text-anchor="middle" fill="#059669" font-size="16" font-weight="bold">AES客户端智能诊断</text>
                <text x="815" y="490" fill="#b45309" font-size="11">价值: • 一键诊断能力</text>
                <text x="815" y="510" fill="#334155" font-size="12" font-weight="bold">关键指标:</text>
                <text x="825" y="527" fill="#475569" font-size="11">• 覆盖率: +60%</text>
                <text x="825" y="542" fill="#475569" font-size="11">• 采纳率: +60%</text>
                <rect x="815" y="575" width="250" height="28" fill="#f0fdf4" rx="5" stroke="#a7f3d0" stroke-width="1"/><text x="940" y="592" text-anchor="middle" fill="#065f46" font-size="10" font-weight="bold">用户触发诊断能力</text>
                <rect x="815" y="610" width="250" height="28" fill="#f0fdf4" rx="5" stroke="#a7f3d0" stroke-width="1"/><text x="940" y="627" text-anchor="middle" fill="#065f46" font-size="10" font-weight="bold">自主通知诊断报告</text>
              </g>

              <!-- Box 4: CloudBot UI诊断页面 -->
              <g transform="translate(94, 0)">
                <rect x="1100" y="445" width="280" height="200" fill="white" rx="10" stroke="#10b981" stroke-width="2" filter="url(#shadow)"/>
                <text x="1240" y="470" text-anchor="middle" fill="#059669" font-size="16" font-weight="bold">CloudBot UI诊断页面</text>
                <text x="1115" y="490" fill="#b45309" font-size="11">价值: • CloudBot平台智能化</text>
                <text x="1115" y="510" fill="#334155" font-size="12" font-weight="bold">关键指标:</text>
                <text x="1125" y="527" fill="#475569" font-size="11">• 用户使用量: >300 /日</text>
                <rect x="1115" y="560" width="250" height="25" fill="#f0fdf4" rx="5" stroke="#a7f3d0" stroke-width="1"/><text x="1240" y="576" text-anchor="middle" fill="#065f46" font-size="10" font-weight="bold">多视角(研发侧/客户侧)智能诊断</text>
                <rect x="1115" y="590" width="250" height="25" fill="#f0fdf4" rx="5" stroke="#a7f3d0" stroke-width="1"/><text x="1240" y="606" text-anchor="middle" fill="#065f46" font-size="10" font-weight="bold">Clientops客户诊断</text>
                <rect x="1115" y="620" width="250" height="25" fill="#f0fdf4" rx="5" stroke="#a7f3d0" stroke-width="1"/><text x="1240" y="636" text-anchor="middle" fill="#065f46" font-size="10" font-weight="bold">深度探索诊断能力</text>
              </g>
          </g>
        </g>

        <path d="M 800 660 V 675" stroke="#9ca3af" stroke-width="2" stroke-dasharray="4 4"/>

        <!-- Agent层 (主层) -->
        <g id="agent-floor">
          <rect x="50" y="675" width="1500" height="265" fill="url(#grad-agent)" rx="15" filter="url(#strong-shadow)"/>
          <text x="800" y="705" text-anchor="middle" fill="white" font-size="24" font-weight="bold" font-family="'Inter', 'Segoe UI', sans-serif">Agent层 - 多Agent能力</text>

          <g>
              <!-- Box 1 -->
              <g transform="translate(-74, 0)">
                <rect x="200" y="725" width="280" height="200" fill="white" rx="10" stroke="#a78bfa" stroke-width="2" filter="url(#shadow)"/>
                <text x="340" y="750" text-anchor="middle" fill="#7c3aed" font-size="16" font-weight="bold">Multi Agent Routing</text>
                <text x="215" y="770" fill="#b45309" font-size="11">价值: • 智能分发，提升系统效率</text>
                <text x="215" y="790" fill="#334155" font-size="12" font-weight="bold">关键指标:</text>
                <text x="225" y="807" fill="#475569" font-size="11">• 路由准确率: 95%+</text>
                <text x="225" y="822" fill="#475569" font-size="11">• 响应时延: &lt;2s</text>
                <rect x="215" y="840" width="250" height="25" fill="#f5f3ff" rx="5" stroke="#c4b5fd" stroke-width="1"/><text x="340" y="856" text-anchor="middle" fill="#6d28d9" font-size="10" font-weight="bold">用户需求路由不同智能体</text>
                <rect x="215" y="870" width="250" height="25" fill="#f5f3ff" rx="5" stroke="#c4b5fd" stroke-width="1"/><text x="340" y="886" text-anchor="middle" fill="#6d28d9" font-size="10" font-weight="bold">用户需求改写</text>
                <rect x="215" y="900" width="250" height="25" fill="#f5f3ff" rx="5" stroke="#c4b5fd" stroke-width="1"/><text x="340" y="916" text-anchor="middle" fill="#6d28d9" font-size="10" font-weight="bold">用户需求工具推荐</text>
              </g>

              <!-- Box 2 -->
              <g transform="translate(-18, 0)">
                <rect x="500" y="725" width="280" height="200" fill="white" rx="10" stroke="#a78bfa" stroke-width="2" filter="url(#shadow)"/>
                <text x="640" y="750" text-anchor="middle" fill="#7c3aed" font-size="16" font-weight="bold">交互诊断</text>
                <text x="515" y="770" fill="#b45309" font-size="11">价值: • 赋能产研/TAM，提供专家级对话</text>
                <text x="515" y="790" fill="#334155" font-size="12" font-weight="bold">关键指标:</text>
                <text x="525" y="807" fill="#475569" font-size="11">• 诊断准确率: >60%</text>
                <text x="525" y="822" fill="#475569" font-size="11">• 覆盖场景数: >10</text>
                <rect x="515" y="840" width="250" height="25" fill="#f5f3ff" rx="5" stroke="#c4b5fd" stroke-width="1"/><text x="640" y="856" text-anchor="middle" fill="#6d28d9" font-size="10" font-weight="bold">多轮对话支持</text>
                <rect x="515" y="870" width="250" height="25" fill="#f5f3ff" rx="5" stroke="#c4b5fd" stroke-width="1"/><text x="640" y="886" text-anchor="middle" fill="#6d28d9" font-size="10" font-weight="bold">复杂任务Plan &amp; Solve的诊断能力</text>
                <rect x="515" y="900" width="250" height="25" fill="#f5f3ff" rx="5" stroke="#c4b5fd" stroke-width="1"/><text x="640" y="916" text-anchor="middle" fill="#6d28d9" font-size="10" font-weight="bold">知识库RAG能力</text>
              </g>

              <!-- Box 3 -->
              <g transform="translate(38, 0)">
                <rect x="800" y="725" width="280" height="200" fill="white" rx="10" stroke="#a78bfa" stroke-width="2" filter="url(#shadow)"/>
                <text x="940" y="750" text-anchor="middle" fill="#7c3aed" font-size="16" font-weight="bold">推理诊断</text>
                <text x="815" y="770" fill="#b45309" font-size="11">价值: • 赋能产研，提供实例级诊断报告</text>
                <text x="815" y="790" fill="#334155" font-size="12" font-weight="bold">关键指标:</text>
                <text x="825" y="807" fill="#475569" font-size="11">• 诊断准确率: >60%</text>
                <text x="825" y="822" fill="#475569" font-size="11">• 覆盖场景数: >10</text>
                <rect x="815" y="840" width="250" height="25" fill="#f5f3ff" rx="5" stroke="#c4b5fd" stroke-width="1"/><text x="940" y="856" text-anchor="middle" fill="#6d28d9" font-size="9" font-weight="bold">支持多实例、多时间点诊断任务</text>
                <rect x="815" y="870" width="250" height="25" fill="#f5f3ff" rx="5" stroke="#c4b5fd" stroke-width="1"/><text x="940" y="886" text-anchor="middle" fill="#6d28d9" font-size="9" font-weight="bold">支持多场景复杂任务拆解</text>
                <rect x="815" y="900" width="250" height="25" fill="#f5f3ff" rx="5" stroke="#c4b5fd" stroke-width="1"/><text x="940" y="916" text-anchor="middle" fill="#6d28d9" font-size="9" font-weight="bold">数据分析和处理能力</text>
              </g>

              <!-- Box 4 -->
              <g transform="translate(94, 0)">
                <rect x="1100" y="725" width="280" height="200" fill="white" rx="10" stroke="#a78bfa" stroke-width="2" filter="url(#shadow)"/>
                <text x="1240" y="750" text-anchor="middle" fill="#7c3aed" font-size="16" font-weight="bold">测试智能体</text>
                <text x="1115" y="770" fill="#b45309" font-size="11">价值: • 赋能测试, 提升软件质量与效率</text>
                <text x="1115" y="790" fill="#334155" font-size="12" font-weight="bold">关键指标:</text>
                <text x="1125" y="807" fill="#475569" font-size="11">• 自动化覆盖率: +30%</text>
                <text x="1125" y="822" fill="#475569" font-size="11">• 测试构建效率: +50%</text>
                <rect x="1115" y="855" width="250" height="28" fill="#f5f3ff" rx="5" stroke="#c4b5fd" stroke-width="1"/><text x="1240" y="872" text-anchor="middle" fill="#6d28d9" font-size="10" font-weight="bold">智能用例生成</text>
                <rect x="1115" y="890" width="250" height="28" fill="#f5f3ff" rx="5" stroke="#c4b5fd" stroke-width="1"/><text x="1240" y="907" text-anchor="middle" fill="#6d28d9" font-size="10" font-weight="bold">自动化编排</text>
              </g>
          </g>
        </g>

        <path d="M 800 940 V 955" stroke="#9ca3af" stroke-width="2" stroke-dasharray="4 4"/>

        <!-- 技术底座层 (地基) -->
        <g id="foundation">
          <rect x="25" y="955" width="1550" height="335" fill="url(#grad-foundation)" rx="15" filter="url(#strong-shadow)"/>
          
          <!-- Title Banner -->
          <rect x="550" y="972" width="500" height="32" rx="16" fill="rgba(0,0,0,0.1)"/>
          <text x="800" y="995" text-anchor="middle" fill="white" font-size="24" font-weight="bold" font-family="'Inter', 'Segoe UI', sans-serif">技术底座层 - 夯实基础技术</text>

          <style>
            .foundation-group-title {
              font-family: 'Inter', 'Segoe UI', sans-serif;
              font-size: 16px;
              font-weight: bold;
              fill: white;
              text-anchor: middle;
            }
            .foundation-card {
              fill: #f8fafc;
              rx: 10;
              stroke-width: 1.5;
              filter: url(#shadow);
            }
            .foundation-card-title {
              font-family: 'Inter', 'Segoe UI', sans-serif;
              font-size: 14px;
              font-weight: bold;
              text-anchor: middle;
            }
            .foundation-item-box {
              fill: #ffffff;
              rx: 5;
              stroke: #e2e8f0;
              stroke-width: 1;
            }
            .foundation-item-text {
              font-family: 'Segoe UI', sans-serif;
              font-size: 11px;
              font-weight: bold;
              fill: #334155;
              text-anchor: middle;
            }
            .foundation-item-subtext {
              font-family: 'Segoe UI', sans-serif;
              font-size: 9px;
              fill: #64748b;
              text-anchor: middle;
            }
          </style>

          <g transform="translate(0, 10)">
              <!-- Grouping Rects and Labels -->
              <rect x="45" y="1015" width="750" height="265" rx="10" fill="url(#grad-foundation-group)" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
              <text x="420" y="1008" text-anchor="middle" fill="white" font-size="16" font-weight="bold"> Agent架构底座 </text>

              <rect x="805" y="1015" width="490" height="265" rx="10" fill="url(#grad-foundation-group)" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
              <text x="1050" y="1008" text-anchor="middle" fill="white" font-size="16" font-weight="bold"> 工程化 </text>

              <rect x="1305" y="1015" width="265" height="265" rx="10" fill="url(#grad-foundation-group)" stroke="rgba(255,255,255,0.2)" stroke-width="1"/>
              <text x="1437" y="1008" text-anchor="middle" fill="white" font-size="16" font-weight="bold"> 对外合作 </text>

              <!-- Agent大脑: 规划与推理 -->
              <g id="agent-brain-refined">
                <rect x="60" y="1030" width="240" height="240" class="foundation-card" stroke="#93c5fd"/>
                <text x="180" y="1050" class="foundation-card-title" fill="#1e40af">Agent大脑 (规划 &amp; 推理)</text>
                <style>
                    .brain-box-refined { fill: #eff6ff; stroke: #bfdbfe; rx: 4; stroke-width: 1; }
                    .brain-text-refined { font-family: 'Segoe UI', sans-serif; font-size: 10px; font-weight: bold; fill: #1e3a8a; text-anchor: middle; }
                    .brain-arrow-refined { stroke: #3b82f6; stroke-width: 1.5; fill: none; marker-end: url(#arrow-blue); }
                </style>
                <g transform="translate(25, 15)">
                    <rect x="75" y="1072" width="100" height="25" class="brain-box-refined"/>
                    <text x="125" y="1088" class="brain-text-refined">意图理解</text>
                    <path d="M 125 1097 V 1107" class="brain-arrow-refined"/>
                    <rect x="75" y="1107" width="100" height="25" class="brain-box-refined"/>
                    <text x="125" y="1123" class="brain-text-refined">任务拆解</text>
                    <path d="M 125 1132 V 1142" class="brain-arrow-refined"/>
                    <rect x="50" y="1142" width="150" height="40" class="brain-box-refined" style="fill:#e0f2fe;"/>
                    <text x="125" y="1158" class="brain-text-refined">计划执行 &amp; 工具编排</text>
                    <path d="M 125 1182 V 1192" class="brain-arrow-refined"/>
                    <g>
                        <rect x="85" y="1192" width="80" height="25" class="brain-box-refined"/>
                        <text x="125" y="1208" class="brain-text-refined">执行反馈</text>
                    </g>
                    <path d="M 125 1217 C 125 1240, 65 1240, 65 1210 V 1125" class="brain-arrow-refined" style="stroke-dasharray: 3 3;"/>
                </g>
              </g>

              <!-- 知识与记忆: 信息与上下文 -->
              <g>
                <rect x="310" y="1030" width="240" height="240" class="foundation-card" stroke="#a7f3d0"/>
                <text x="430" y="1050" class="foundation-card-title" fill="#065f46">知识与记忆 (信息 &amp; 上下文)</text>
                <g transform="translate(325, 1080)">
                    <rect width="210" height="50" class="foundation-item-box" style="fill:#f0fdf4;"/>
                    <text x="105" y="22" class="foundation-item-text">Agent Memory</text>
                    <text x="105" y="38" class="foundation-item-subtext">长/短期记忆</text>
                </g>
                <g transform="translate(325, 1145)">
                    <rect width="210" height="50" class="foundation-item-box" style="fill:#f0fdf4;"/>
                    <text x="105" y="22" class="foundation-item-text">Knowledge Graph</text>
                    <text x="105" y="38" class="foundation-item-subtext">知识图谱</text>
                </g>
                <g transform="translate(325, 1210)">
                    <rect width="210" height="50" class="foundation-item-box" style="fill:#f0fdf4;"/>
                    <text x="105" y="22" class="foundation-item-text">SOP System</text>
                    <text x="105" y="38" class="foundation-item-subtext">SOP 体系</text>
                </g>
              </g>

              <!-- 工具与执行 -->
              <g>
                <rect x="560" y="1030" width="225" height="240" class="foundation-card" stroke="#c4b5fd"/>
                <text x="672" y="1050" class="foundation-card-title" fill="#6d28d9">工具与执行</text>
                <g transform="translate(570, 1080)">
                    <rect width="140" height="50" class="foundation-item-box" style="fill:#f5f3ff;"/>
                    <text x="70" y="28" class="foundation-item-text">MCP 标准工具集</text>
                </g>
                <g transform="translate(570, 1145)">
                    <rect width="140" height="50" class="foundation-item-box" style="fill:#f5f3ff;"/>
                    <text x="70" y="22" class="foundation-item-text">代码智能体</text>
                    <text x="70" y="38" class="foundation-item-subtext">(生成 &amp; 规划)</text>
                </g>
                <g transform="translate(570, 1210)">
                    <rect width="140" height="50" class="foundation-item-box" style="fill:#f5f3ff;"/>
                    <text x="70" y="22" class="foundation-item-text">安全沙箱</text>
                    <text x="70" y="38" class="foundation-item-subtext">(FC SandBox)</text>
                </g>
                <g transform="translate(720, 1145)">
                    <rect width="50" height="50" class="foundation-item-box" style="fill:#f5f3ff;"/>
                    <text x="25" y="22" class="foundation-item-text">轨迹强化</text>
                    <text x="25" y="38" class="foundation-item-subtext">(RL)</text>
                </g>
                <path d="M 710 1170 H 720" stroke="#8b5cf6" stroke-width="1.5" marker-end="url(#arrow)"/>
              </g>

              <!-- 基础工程化能力 -->
              <g>
                <rect x="820" y="1030" width="230" height="240" class="foundation-card" stroke="#fde68a"/>
                <text x="935" y="1050" class="foundation-card-title" fill="#b45309">基础工程化能力</text>
                <g transform="translate(830, 1100)">
                    <rect width="210" height="60" class="foundation-item-box" style="fill:#fffbeb;"/>
                    <text x="105" y="25" class="foundation-item-text">OpenAPI → MCP 工具</text>
                    <text x="105" y="45" class="foundation-item-subtext">(自动生成 &amp; 跟踪)</text>
                </g>
                <g transform="translate(830, 1185)">
                    <rect width="210" height="50" class="foundation-item-box" style="fill:#fffbeb;"/>
                    <text x="105" y="22" class="foundation-item-text">服务可观测性</text>
                    <text x="105" y="38" class="foundation-item-subtext">(Logging, Tracing, Metrics)</text>
                </g>
              </g>

              <!-- 工单数据处理 -->
              <g>
                <rect x="1060" y="1030" width="230" height="240" class="foundation-card" stroke="#fca5a5"/>
                <text x="1175" y="1050" class="foundation-card-title" fill="#b91c1c">工单数据处理</text>
                <g transform="translate(1070, 1080)">
                    <rect width="210" height="50" class="foundation-item-box" style="fill:#fef2f2;"/>
                    <text x="105" y="28" class="foundation-item-text">工单采集 / 分类</text>
                </g>
                <g transform="translate(1070, 1145)">
                    <rect width="210" height="50" class="foundation-item-box" style="fill:#fef2f2;"/>
                    <text x="105" y="28" class="foundation-item-text">关单 SOP 知识库构建</text>
                </g>
                 <g transform="translate(1070, 1210)">
                    <rect width="210" height="50" class="foundation-item-box" style="fill:#fef2f2;"/>
                    <text x="105" y="28" class="foundation-item-text">数据分析与洞察</text>
                </g>
              </g>

              <!-- 团队协作 -->
              <g>
                <rect x="1320" y="1030" width="240" height="240" class="foundation-card" stroke="#d1d5db"/>
                <text x="1440" y="1050" class="foundation-card-title" fill="#4b5563">团队协作生态</text>
                <g transform="translate(1335, 1080)">
                    <rect width="210" height="50" class="foundation-item-box" style="fill:#f3f4f6;"/>
                    <text x="105" y="28" class="foundation-item-text">热迁移 MCP 复用</text>
                </g>
                <g transform="translate(1335, 1145)">
                    <rect width="210" height="50" class="foundation-item-box" style="fill:#f3f4f6;"/>
                    <text x="105" y="28" class="foundation-item-text">ChatBI 能力复用</text>
                </g>
                <g transform="translate(1335, 1210)">
                    <rect width="210" height="50" class="foundation-item-box" style="fill:#f3f4f6;"/>
                    <text x="105" y="28" class="foundation-item-text">知识库复用</text>
                </g>
              </g>
          </g>
        </g>
    </g>
  </g>
</svg>