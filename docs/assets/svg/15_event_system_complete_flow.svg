<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1550" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Modern Gradients -->
    <linearGradient id="processGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#4A90E2;"/><stop offset="100%" style="stop-color:#3B73D1;"/></linearGradient>
    <linearGradient id="decisionGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#F5A623;"/><stop offset="100%" style="stop-color:#E8900A;"/></linearGradient>
    <linearGradient id="storageGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#50E3C2;"/><stop offset="100%" style="stop-color:#29B89A;"/></linearGradient>
    <linearGradient id="processorGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#B8E986;"/><stop offset="100%" style="stop-color:#7ED321;"/></linearGradient>
    <linearGradient id="outputGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#BD10E0;"/><stop offset="100%" style="stop-color:#9013FE;"/></linearGradient>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="0%" y2="100%"><stop offset="0%" style="stop-color:#f8fafc;"/><stop offset="100%" style="stop-color:#f1f5f9;"/></linearGradient>
    
    <!-- Styles -->
    <style>
      /* Fonts */
      .title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 30px; font-weight: 700; fill: #1e293b; }
      
      /* --- NEW HIGHLIGHT STYLES --- */
      .highlight-bg { 
        fill: #f8fafc; /* A very light gray, almost white background */
        rx: 25; 
        stroke: #e2e8f0; /* A light border */
        stroke-width: 1; 
        filter: url(#highlightShadow); 
      }
      .highlight-box { 
        rx: 18; 
        stroke: #cbd5e1; /* A slightly darker border for the cards */
        stroke-width: 1; 
        filter: url(#subtleShadow);
        fill: #ffffff; /* Solid white fill for the cards */
      }
      .highlight-box-1, .highlight-box-2, .highlight-box-3 { 
        /* These classes are kept for structure but share the same style from .highlight-box */
      }
      .highlight-title { 
        font-family: 'Inter', sans-serif; 
        font-size: 18px; 
        font-weight: 700; 
        fill: #1e3a8a; /* A deep, professional blue */
        text-anchor: middle;
      }
      .highlight-desc { 
        font-family: 'Inter', sans-serif; 
        font-size: 13px; 
        font-weight: 500; 
        fill: #334155; /* A dark slate gray for good readability */
        opacity: 0.9; 
        text-anchor: middle;
      }
      
      .section-title { font-family: 'Inter', sans-serif; font-size: 20px; font-weight: 600; fill: #334155; }
      
      .box-text { font-family: 'Inter', sans-serif; font-size: 14px; fill: white; }
      .box-text-bold { font-weight: 600; font-size: 15px; }
      .box-text-small { font-size: 12px; opacity: 0.9; }
      
      /* Shapes &amp; Arrows */
      .box { rx: 12; filter: url(#dropShadow); }
      .arrow { stroke: #64748b; stroke-width: 2.5; fill: none; marker-end: url(#arrowhead); }
      .decision-arrow { stroke: #F5A623; stroke-width: 2.5; fill: none; marker-end: url(#arrowhead-decision); }
      .success-arrow { stroke: #29B89A; stroke-width: 2.5; fill: none; marker-end: url(#arrowhead-success); }
      .label-text { font-family: 'Inter', sans-serif; font-size: 12px; fill: #475569; font-weight: 500; }
    </style>
    
    <!-- Markers & Filters -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto"><polygon points="0 0, 10 3.5, 0 7" fill="#64748b" /></marker>
    <marker id="arrowhead-decision" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto"><polygon points="0 0, 10 3.5, 0 7" fill="#F5A623" /></marker>
    <marker id="arrowhead-success" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto"><polygon points="0 0, 10 3.5, 0 7" fill="#29B89A" /></marker>
    <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%"><feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#1e293b" flood-opacity="0.1"/></filter>
    <filter id="subtleShadow" x="-20%" y="-20%" width="140%" height="140%"><feDropShadow dx="0" dy="3" stdDeviation="6" flood-color="#1e293b" flood-opacity="0.05"/></filter>
    <filter id="highlightShadow" x="-20%" y="-20%" width="140%" height="140%"><feDropShadow dx="0" dy="6" stdDeviation="12" flood-color="#1e293b" flood-opacity="0.15"/></filter>
  </defs>

  <!-- Faint Background -->
  <rect width="100%" height="100%" fill="url(#bgGradient)"/>

  <!-- Main Title & Highlights -->
  <text x="800" y="60" text-anchor="middle" class="title">LangGraph 消息处理流程</text>
  
  <!-- Highlight Background -->
  <rect x="120" y="85" width="1400" height="100" class="highlight-bg"/>
  
  <g id="highlights" transform="translate(150, 100)">
    <g transform="translate(0, 0)">
      <rect width="420" height="70" class="highlight-box highlight-box-1"/>
      <text x="210" y="28" class="highlight-title">🧩 增量消息聚合</text>
      <text x="210" y="48" class="highlight-desc">AIMessageChunk流式拼接</text>
      <text x="210" y="62" class="highlight-desc">工具调用状态实时追踪</text>
    </g>
    <g transform="translate(470, 0)">
      <rect width="420" height="70" class="highlight-box highlight-box-2"/>
      <text x="210" y="28" class="highlight-title">⚡ 易定义Agent输出格式</text>
      <text x="210" y="48" class="highlight-desc">Planner输出思考JSON</text>
      <text x="210" y="62" class="highlight-desc">Researcher输出工具执行 + Reporter输出结果文本</text>
    </g>
    <g transform="translate(940, 0)">
      <rect width="420" height="70" class="highlight-box highlight-box-3"/>
      <text x="210" y="28" class="highlight-title">🔄 实时状态同步</text>
      <text x="210" y="48" class="highlight-desc">MessageRepository统一存储</text>
      <text x="210" y="62" class="highlight-desc">业务字段动态更新映射</text>
    </g>
  </g>

  <!-- Part 1: Message Handling and Storage -->
  <rect x="50" y="210" width="1500" height="580" fill="rgba(255,255,255,0.6)" rx="16" stroke="#e2e8f0" />
  <text x="80" y="250" class="section-title">第一部分：消息处理与存储</text>
  
  <g class="box-text">
    <rect x="700" y="280" width="200" height="50" class="box" fill="url(#processGradient)"/><text x="800" y="310" text-anchor="middle" class="box-text-bold">开始处理消息</text>
    <path class="arrow" d="M 800 330 V 360"/>
    <rect x="700" y="360" width="200" height="55" class="box" fill="url(#processGradient)"/><text x="800" y="383" text-anchor="middle" class="box-text-bold">handle_message()</text><text x="800" y="403" text-anchor="middle" class="box-text-small">接收原始消息</text>
    <path class="arrow" d="M 800 415 V 445"/>
    <rect x="700" y="445" width="200" height="55" class="box" fill="url(#processGradient)"/><text x="800" y="468" text-anchor="middle" class="box-text-bold">MessageCollector</text><text x="800" y="488" text-anchor="middle" class="box-text-small">collect_message()</text>
    <path class="arrow" d="M 800 500 V 530"/>
    <path d="M750 530 L850 530 L875 565 L850 600 L750 600 L725 565 Z" class="box" fill="url(#decisionGradient)"/><text x="800" y="560" text-anchor="middle" class="box-text-bold">消息类型?</text><text x="800" y="580" text-anchor="middle" class="box-text-small">AIMessage / Tool</text>

    <!-- AI Branch -->
    <path d="M 725 565 H 450 V 630" class="decision-arrow" /><text x="580" y="595" class="label-text">AIMessage</text>
    <rect x="350" y="630" width="200" height="55" class="box" fill="url(#processGradient)"/><text x="450" y="653" text-anchor="middle" class="box-text-bold">_handle_ai_message</text><text x="450" y="673" text-anchor="middle" class="box-text-small">处理AI消息</text>
    <path d="M 450 685 V 710" class="arrow" />
    <rect x="350" y="710" width="200" height="50" class="box" fill="url(#storageGradient)"/><text x="450" y="740" text-anchor="middle" class="box-text">存储消息片段</text>
    
    <!-- Tool Branch -->
    <path d="M 875 565 H 1150 V 630" class="decision-arrow" /><text x="1000" y="595" class="label-text">ToolMessage</text>
    <rect x="1050" y="630" width="200" height="55" class="box" fill="url(#processGradient)"/><text x="1150" y="653" text-anchor="middle" class="box-text-bold">_handle_tool_message</text><text x="1150" y="673" text-anchor="middle" class="box-text-small">处理工具消息</text>
    <path d="M 1150 685 V 710" class="arrow" />
    <rect x="1050" y="710" width="200" height="50" class="box" fill="url(#storageGradient)"/><text x="1150" y="740" text-anchor="middle" class="box-text">更新工具状态</text>
    
    <!-- Merge -->
    <path d="M 450 760 V 800 H 700" class="success-arrow" />
    <path d="M 1150 760 V 800 H 900" class="success-arrow" />
    <rect x="700" y="800" width="200" height="55" class="box" fill="url(#storageGradient)"/><text x="800" y="823" text-anchor="middle" class="box-text-bold">MessageRepository</text><text x="800" y="843" text-anchor="middle" class="box-text-small">存储/更新消息</text>
  </g>

  <!-- Part 2: Business Processing -->
  <rect x="50" y="880" width="1500" height="640" fill="rgba(255,255,255,0.6)" rx="16" stroke="#e2e8f0" />
  <text x="80" y="920" class="section-title">第二部分：业务处理与聚合</text>
  <path class="arrow" d="M 800 855 V 910"/>
  
  <g class="box-text">
    <rect x="700" y="910" width="220" height="55" class="box" fill="url(#processGradient)"/><text x="810" y="933" text-anchor="middle" class="box-text-bold">BusinessMessageProcessor</text><text x="810" y="953" text-anchor="middle" class="box-text-small">process_business_messages()</text>
    <path class="arrow" d="M 810 965 V 995"/>
    <path d="M760 995 L860 995 L885 1030 L860 1065 L760 1065 L735 1030 Z" class="box" fill="url(#decisionGradient)"/><text x="810" y="1025" text-anchor="middle" class="box-text-bold">Agent类型?</text><text x="810" y="1045" text-anchor="middle" class="box-text-small">选择处理器</text>

    <!-- Processor Lanes -->
    <path d="M 735 1030 H 350 V 1095" class="decision-arrow" /><text x="550" y="1055" class="label-text">Planner</text>
    <path d="M 810 1065 V 1095" class="decision-arrow" /><text x="820" y="1085" class="label-text">Researcher</text>
    <path d="M 885 1030 H 1250 V 1095" class="decision-arrow" /><text x="1050" y="1055" class="label-text">Reporter</text>
    
    <!-- Planner -->
    <rect x="250" y="1095" width="200" height="55" class="box" fill="url(#processorGradient)"/><text x="350" y="1118" text-anchor="middle" class="box-text-bold" fill="#333">PlannerProcessor</text><text x="350" y="1138" text-anchor="middle" class="box-text-small" fill="#333">解析思考与计划</text>
    <path d="M 350 1150 V 1175" class="success-arrow" />
    <rect x="250" y="1175" width="200" height="50" class="box" fill="url(#outputGradient)"/><text x="350" y="1205" text-anchor="middle" class="box-text">输出: thought, plan_steps</text>
    
    <!-- Researcher -->
    <rect x="710" y="1095" width="200" height="55" class="box" fill="url(#processorGradient)"/><text x="810" y="1118" text-anchor="middle" class="box-text-bold" fill="#333">ResearcherProcessor</text><text x="810" y="1138" text-anchor="middle" class="box-text-small" fill="#333">处理工具调用</text>
    <path d="M 810 1150 V 1175" class="success-arrow" />
    <rect x="710" y="1175" width="200" height="50" class="box" fill="url(#outputGradient)"/><text x="810" y="1205" text-anchor="middle" class="box-text">输出: executions[]</text>
    
    <!-- Reporter -->
    <rect x="1150" y="1095" width="200" height="55" class="box" fill="url(#processorGradient)"/><text x="1250" y="1118" text-anchor="middle" class="box-text-bold" fill="#333">ReporterProcessor</text><text x="1250" y="1138" text-anchor="middle" class="box-text-small" fill="#333">收集最终结果</text>
    <path d="M 1250 1150 V 1175" class="success-arrow" />
    <rect x="1150" y="1175" width="200" height="50" class="box" fill="url(#outputGradient)"/><text x="1250" y="1205" text-anchor="middle" class="box-text">输出: final_result</text>
    
    <!-- Merge and Finalize -->
    <path d="M 350 1225 V 1275 H 710" class="success-arrow" />
    <path d="M 810 1225 V 1275" class="success-arrow" />
    <path d="M 1250 1225 V 1275 H 910" class="success-arrow" />
    <rect x="710" y="1275" width="200" height="55" class="box" fill="url(#storageGradient)"/><text x="810" y="1298" text-anchor="middle" class="box-text-bold">数据聚合</text><text x="810" y="1318" text-anchor="middle" class="box-text-small">合并处理结果</text>
    <path class="arrow" d="M 810 1330 V 1360"/>
    <rect x="710" y="1360" width="200" height="55" class="box" fill="url(#processGradient)"/><text x="810" y="1383" text-anchor="middle" class="box-text-bold">_apply_event_data()</text><text x="810" y="1403" text-anchor="middle" class="box-text-small">更新业务字段</text>
    <path class="arrow" d="M 810 1415 V 1445"/>
    <rect x="710" y="1445" width="200" height="50" class="box" fill="url(#storageGradient)"/><text x="810" y="1475" text-anchor="middle" class="box-text-bold">处理完成</text>
  </g>

</svg>
