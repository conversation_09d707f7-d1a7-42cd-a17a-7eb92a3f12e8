<svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <!-- 现代渐变色彩定义 -->
        <linearGradient id="apiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
        </linearGradient>
        
        <linearGradient id="businessGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
        </linearGradient>
        
        <linearGradient id="toolsGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
        </linearGradient>
        
        <linearGradient id="infraGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
        </linearGradient>
        
        <linearGradient id="componentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9" />
            <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.7" />
        </linearGradient>
        
        <!-- 现代阴影滤镜 -->
        <filter id="modernShadow" x="-50%" y="-50%" width="200%" height="200%">
            <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.08"/>
            <feDropShadow dx="0" dy="1" stdDeviation="2" flood-color="#000000" flood-opacity="0.06"/>
        </filter>
        
        <!-- 发光效果 -->
        <filter id="glow" x="-20%" y="-20%" width="140%" height="140%">
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feMerge> 
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
            </feMerge>
        </filter>
        
        <!-- 现代箭头标记 -->
        <marker id="arrowhead" viewBox="0 0 10 10" refX="8" refY="5" markerWidth="8" markerHeight="8" orient="auto-start-reverse">
            <path d="M 0 0 L 10 5 L 0 10 z" fill="#64748b" />
        </marker>
        
        <!-- 网格图案 -->
        <pattern id="modernGrid" width="30" height="30" patternUnits="userSpaceOnUse">
            <path d="M 30 0 L 0 0 0 30" fill="none" stroke="#e5e7eb" stroke-width="0.5" opacity="0.4"/>
        </pattern>
    </defs>
    
    <!-- 现代背景 -->
    <rect width="800" height="600" fill="#fafbfc"/>
    <rect width="800" height="600" fill="url(#modernGrid)"/>

    <!-- 标题区域 -->
    <rect x="50" y="20" width="700" height="60" rx="16" fill="white" 
          stroke="#e5e7eb" stroke-width="1" filter="url(#modernShadow)"/>
    <text x="400" y="45" text-anchor="middle" font-family="Inter, -apple-system, sans-serif" 
          font-size="24" font-weight="700" fill="#111827">深度诊断系统架构</text>
    <text x="400" y="65" text-anchor="middle" font-family="Inter, sans-serif" 
          font-size="12" font-weight="400" fill="#6b7280">智能化 ECS 诊断系统</text>
    
    <!-- 展现与接入层 -->
    <rect x="50" y="100" width="700" height="100" rx="12" fill="url(#apiGradient)" 
          filter="url(#modernShadow)"/>
    <text x="400" y="130" text-anchor="middle" font-family="Inter, sans-serif" 
          font-size="18" font-weight="600" fill="white">展现与接入层 (API Layer)</text>
    
    <!-- 核心业务层 -->
    <rect x="50" y="220" width="700" height="140" rx="12" fill="url(#businessGradient)" 
          filter="url(#modernShadow)"/>
    <text x="400" y="250" text-anchor="middle" font-family="Inter, sans-serif" 
          font-size="18" font-weight="600" fill="white">核心业务层 (Core Business Layer)</text>
    
    <!-- 工具与服务层 -->
    <rect x="50" y="380" width="700" height="80" rx="12" fill="url(#toolsGradient)" 
          filter="url(#modernShadow)"/>
    <text x="400" y="410" text-anchor="middle" font-family="Inter, sans-serif" 
          font-size="18" font-weight="600" fill="white">工具与服务层 (Tools &amp; Services Layer)</text>
    
    <!-- 基础设施层 -->
    <rect x="50" y="480" width="700" height="80" rx="12" fill="url(#infraGradient)" 
          filter="url(#modernShadow)"/>
    <text x="400" y="510" text-anchor="middle" font-family="Inter, sans-serif" 
          font-size="18" font-weight="600" fill="white">基础设施层 (Infrastructure Layer)</text>

    <!-- API 层组件 -->
    <rect x="100" y="150" width="160" height="40" rx="8" fill="url(#componentGradient)" 
          stroke="rgba(255,255,255,0.3)" stroke-width="1" filter="url(#modernShadow)"/>
    <text x="180" y="175" text-anchor="middle" font-family="Inter, sans-serif" 
          font-size="12" font-weight="500" fill="#1d4ed8">FastAPI 应用</text>
    
    <rect x="280" y="150" width="160" height="40" rx="8" fill="url(#componentGradient)" 
          stroke="rgba(255,255,255,0.3)" stroke-width="1" filter="url(#modernShadow)"/>
    <text x="360" y="175" text-anchor="middle" font-family="Inter, sans-serif" 
          font-size="12" font-weight="500" fill="#1d4ed8">认证与中间件</text>
    
    <rect x="460" y="150" width="160" height="40" rx="8" fill="url(#componentGradient)" 
          stroke="rgba(255,255,255,0.3)" stroke-width="1" filter="url(#modernShadow)"/>
    <text x="540" y="175" text-anchor="middle" font-family="Inter, sans-serif" 
          font-size="12" font-weight="500" fill="#1d4ed8">API 路由</text>

    <!-- 核心业务层组件 -->
    <rect x="80" y="270" width="280" height="70" rx="8" fill="url(#componentGradient)" 
          stroke="rgba(255,255,255,0.3)" stroke-width="1" filter="url(#modernShadow)"/>
    <text x="220" y="290" text-anchor="middle" font-family="Inter, sans-serif" 
          font-size="12" font-weight="600" fill="#059669">多智能体工作流 (LangGraph)</text>
    <text x="220" y="310" text-anchor="middle" font-family="Inter, sans-serif" 
          font-size="10" font-weight="400" fill="#059669">协调器 • 规划器 • 研究员 • 编程员 • 报告员</text>
    <text x="220" y="325" text-anchor="middle" font-family="Inter, sans-serif" 
          font-size="10" font-weight="400" fill="#059669">人工反馈</text>
    
    <rect x="380" y="270" width="280" height="70" rx="8" fill="url(#componentGradient)" 
          stroke="rgba(255,255,255,0.3)" stroke-width="1" filter="url(#modernShadow)"/>
    <text x="520" y="290" text-anchor="middle" font-family="Inter, sans-serif" 
          font-size="12" font-weight="600" fill="#059669">核心服务</text>
    <text x="520" y="310" text-anchor="middle" font-family="Inter, sans-serif" 
          font-size="10" font-weight="400" fill="#059669">任务服务 • 报告生成器</text>
    <text x="520" y="325" text-anchor="middle" font-family="Inter, sans-serif" 
          font-size="10" font-weight="400" fill="#059669">SSE 解析器</text>

    <!-- 工具与服务层组件 -->
    <rect x="100" y="420" width="160" height="30" rx="8" fill="url(#componentGradient)" 
          stroke="rgba(255,255,255,0.3)" stroke-width="1" filter="url(#modernShadow)"/>
    <text x="180" y="440" text-anchor="middle" font-family="Inter, sans-serif" 
          font-size="11" font-weight="500" fill="#7c3aed">MCP 工具集</text>
    
    <rect x="280" y="420" width="160" height="30" rx="8" fill="url(#componentGradient)" 
          stroke="rgba(255,255,255,0.3)" stroke-width="1" filter="url(#modernShadow)"/>
    <text x="360" y="440" text-anchor="middle" font-family="Inter, sans-serif" 
          font-size="11" font-weight="500" fill="#7c3aed">搜索引擎</text>
    
    <rect x="460" y="420" width="160" height="30" rx="8" fill="url(#componentGradient)" 
          stroke="rgba(255,255,255,0.3)" stroke-width="1" filter="url(#modernShadow)"/>
    <text x="540" y="440" text-anchor="middle" font-family="Inter, sans-serif" 
          font-size="11" font-weight="500" fill="#7c3aed">代码执行器 (REPL)</text>

    <!-- 基础设施层组件 -->
    <rect x="120" y="520" width="140" height="30" rx="8" fill="url(#componentGradient)" 
          stroke="rgba(255,255,255,0.3)" stroke-width="1" filter="url(#modernShadow)"/>
    <text x="190" y="540" text-anchor="middle" font-family="Inter, sans-serif" 
          font-size="11" font-weight="500" fill="#d97706">Redis 缓存</text>
    
    <rect x="280" y="520" width="140" height="30" rx="8" fill="url(#componentGradient)" 
          stroke="rgba(255,255,255,0.3)" stroke-width="1" filter="url(#modernShadow)"/>
    <text x="350" y="540" text-anchor="middle" font-family="Inter, sans-serif" 
          font-size="11" font-weight="500" fill="#d97706">LLM 服务</text>
    
    <rect x="440" y="520" width="140" height="30" rx="8" fill="url(#componentGradient)" 
          stroke="rgba(255,255,255,0.3)" stroke-width="1" filter="url(#modernShadow)"/>
    <text x="510" y="540" text-anchor="middle" font-family="Inter, sans-serif" 
          font-size="11" font-weight="500" fill="#d97706">对象存储 (OSS)</text>

    <!-- 现代化箭头连接 -->
    <path d="M 400 200 L 400 210" stroke="#64748b" stroke-width="2" 
          marker-end="url(#arrowhead)" fill="none" opacity="0.7"/>
    <path d="M 220 340 L 220 370" stroke="#64748b" stroke-width="2" 
          marker-end="url(#arrowhead)" fill="none" opacity="0.7"/>
    <path d="M 520 340 L 520 370" stroke="#64748b" stroke-width="2" 
          marker-end="url(#arrowhead)" fill="none" opacity="0.7"/>
    <path d="M 400 460 L 400 470" stroke="#64748b" stroke-width="2" 
          marker-end="url(#arrowhead)" fill="none" opacity="0.7"/>
</svg>