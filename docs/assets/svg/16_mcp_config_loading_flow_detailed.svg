<svg width="1400" height="960" viewBox="0 0 1400 960" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <!-- Gradients -->
        <linearGradient id="layer4Gradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" /><stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" /></linearGradient>
        <linearGradient id="layer3Gradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#10b981;stop-opacity:1" /><stop offset="100%" style="stop-color:#047857;stop-opacity:1" /></linearGradient>
        <linearGradient id="layer2Gradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" /><stop offset="100%" style="stop-color:#d97706;stop-opacity:1" /></linearGradient>
        <linearGradient id="layer1Gradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" /><stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" /></linearGradient>
        <linearGradient id="componentGradient" x1="0%" y1="0%" x2="0%" y2="100%"><stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" /><stop offset="100%" style="stop-color:#f8fafc;stop-opacity:1" /></linearGradient>
        <linearGradient id="backgroundGradient" x1="0%" y1="0%" x2="0%" y2="100%"><stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" /><stop offset="100%" style="stop-color:#f1f5f9;stop-opacity:1" /></linearGradient>
        <linearGradient id="summaryGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" /><stop offset="100%" style="stop-color:#3730a3;stop-opacity:1" /></linearGradient>
        
        <!-- Filters and Markers -->
        <filter id="dropShadow" x="-50%" y="-50%" width="200%" height="200%">
            <feGaussianBlur in="SourceAlpha" stdDeviation="3"/><feOffset dx="0" dy="2" result="offset"/>
            <feComponentTransfer><feFuncA type="linear" slope="0.2"/></feComponentTransfer>
            <feMerge><feMergeNode/><feMergeNode in="SourceGraphic"/></feMerge>
        </filter>
        <marker id="arrowMarker" viewBox="0 0 10 10" refX="8" refY="3" markerWidth="6" markerHeight="6" orient="auto" markerUnits="strokeWidth"><path d="M0,0 L0,6 L9,3 z" fill="#64748b"/></marker>
    </defs>

    <style>
        .main-title { font-family: Arial, sans-serif; font-size: 32px; font-weight: 700; text-anchor: middle; fill: #1e293b; }
        .sub-title { font-family: Arial, sans-serif; font-size: 16px; font-weight: 400; text-anchor: middle; fill: #475569; }
        .layer-title { font-family: Arial, sans-serif; font-size: 20px; font-weight: 600; text-anchor: start; fill: #ffffff; }
        .component-title { font-family: Arial, sans-serif; font-size: 16px; font-weight: 600; text-anchor: middle; fill: #1e293b; }
        .description-text { font-family: Arial, sans-serif; font-size: 13px; font-weight: 400; text-anchor: start; fill: #475569; }
        .code-text { font-family: monospace; font-size: 12px; font-weight: 500; fill: #c026d3; }
        .highlight-text { font-family: Arial, sans-serif; font-size: 13px; font-weight: 600; text-anchor: start; fill: #059669; }
        .flow-arrow { stroke: #94a3b8; stroke-width: 2.5; fill: none; marker-end: url(#arrowMarker); }
        .summary-title { font-family: Arial, sans-serif; font-size: 18px; font-weight: 600; text-anchor: middle; }
        .summary-text { font-family: Arial, sans-serif; font-size: 14px; }
    </style>

    <rect width="1400" height="960" fill="url(#backgroundGradient)"/>
    
    <!-- Main Title -->
    <text x="700" y="50" class="main-title">MCP 工具系统分层架构</text>
    <text x="700" y="80" class="sub-title">从工具调用到配置加载的完整数据流转</text>

    <!-- Key Data Flow Summary -->
    <g transform="translate(60, 110)">
        <rect width="1280" height="90" rx="16" fill="url(#summaryGradient)" stroke="#3b82f6" stroke-width="2" filter="url(#dropShadow)"/>
        <text x="640" y="30" class="summary-title" style="fill: #ffffff;">核心数据流</text>
        
        <text x="40" y="55" class="summary-text" style="font-weight: 600; fill: #ffffff;">[快速] 缓存命中路径:</text>
        <text x="40" y="75" class="summary-text" style="fill: #ffffff;">
            <tspan style="font-weight:500;">Agent调用</tspan>
            <tspan style="fill: #60a5fa; font-weight: bold;"> → </tspan>
            <tspan style="font-weight:500;">缓存读取</tspan>
            <tspan style="fill: #60a5fa; font-weight: bold;"> → </tspan>
            <tspan style="font-weight:500;">工具执行</tspan>
            <tspan style="fill: #60a5fa; font-weight: bold;"> → </tspan>
            <tspan style="font-weight:500;">返回结果</tspan>
        </text>

        <line x1="640" y1="45" x2="640" y2="85" style="stroke:#ffffff; stroke-width:2; opacity:0.3"/>

        <text x="680" y="55" class="summary-text" style="font-weight: 600; fill: #ffffff;">[完整] 缓存未命中路径:</text>
        <text x="680" y="75" class="summary-text" style="fill: #ffffff;">
            <tspan style="font-weight:500;">缓存失效</tspan>
            <tspan style="fill: #fbbf24; font-weight: bold;"> → </tspan>
            <tspan style="font-weight:500;">连接MCP</tspan>
            <tspan style="fill: #fbbf24; font-weight: bold;"> → </tspan>
            <tspan style="font-weight:500;">加载配置</tspan>
            <tspan style="fill: #fbbf24; font-weight: bold;"> → </tspan>
            <tspan style="font-weight:500;">更新缓存</tspan>
        </text>
    </g>

    <!-- Layer Groups -->
    <g transform="translate(0, 220)">
        <rect x="60" y="0" width="1280" height="160" rx="16" fill="url(#layer4Gradient)" filter="url(#dropShadow)"/>
        <text x="80" y="30" class="layer-title">工具调用执行层</text>
        <text x="80" y="50" class="description-text" fill="rgba(255,255,255,0.8)">职责：Agent调用工具入口，获取工具列表并执行具体工具</text>
        <g transform="translate(90, 70)">
            <rect width="360" height="75" rx="12" fill="url(#componentGradient)"/><text x="180" y="20" class="component-title">Agent → MCPToolManager</text>
            <text x="10" y="45" class="description-text">调用方法: <tspan class="code-text">get_enabled_mcp_tools()</tspan></text>
            <text x="10" y="65" class="highlight-text">能力：提供统一的工具管理入口</text>
        </g>
        <g transform="translate(470, 70)">
            <rect width="360" height="75" rx="12" fill="url(#componentGradient)"/><text x="180" y="20" class="component-title">StructuredTool 调用</text>
            <text x="10" y="45" class="description-text">核心机制: <tspan class="code-text">coroutine=call_tool</tspan></text>
            <text x="10" y="65" class="highlight-text">能力：异步执行具体工具调用</text>
        </g>
        <g transform="translate(850, 70)">
            <rect width="360" height="75" rx="12" fill="url(#componentGradient)"/><text x="180" y="20" class="component-title">MCP 会话与执行</text>
            <text x="10" y="45" class="description-text">执行流程: <tspan class="code-text">create_session → call_tool</tspan></text>
            <text x="10" y="65" class="highlight-text">能力：直接与MCP服务器通信</text>
        </g>
    </g>

    <g transform="translate(0, 400)">
        <rect x="60" y="0" width="1280" height="160" rx="16" fill="url(#layer3Gradient)" filter="url(#dropShadow)"/>
        <text x="80" y="30" class="layer-title">缓存管理层</text>
        <text x="80" y="50" class="description-text" fill="rgba(255,255,255,0.8)">职责：检查Redis缓存，命中则返回，未命中则触发下层获取</text>
        <g transform="translate(90, 70)">
            <rect width="360" height="75" rx="12" fill="url(#componentGradient)"/><text x="180" y="20" class="component-title">MCPCacheManager</text>
            <text x="10" y="45" class="description-text">核心方法: <tspan class="code-text">get_tools() → _load_from_cache()</tspan></text>
            <text x="10" y="65" class="highlight-text">能力：智能缓存路由决策</text>
        </g>
        <g transform="translate(470, 70)">
            <rect width="360" height="75" rx="12" fill="url(#componentGradient)"/><text x="180" y="20" class="component-title">工具反序列化</text>
            <text x="10" y="45" class="description-text">重建方法: <tspan class="code-text">dict_2_structured_tool</tspan></text>
            <text x="10" y="65" class="highlight-text">能力：恢复可执行的工具对象</text>
        </g>
        <g transform="translate(850, 70)">
            <rect width="360" height="75" rx="12" fill="url(#componentGradient)"/><text x="180" y="20" class="component-title">缓存写入更新</text>
            <text x="10" y="45" class="description-text">存储方法: <tspan class="code-text">_save_to_cache()</tspan></text>
            <text x="10" y="65" class="highlight-text">能力：持久化工具配置(TTL: 1800s)</text>
        </g>
    </g>

    <g transform="translate(0, 580)">
        <rect x="60" y="0" width="1280" height="160" rx="16" fill="url(#layer2Gradient)" filter="url(#dropShadow)"/>
        <text x="80" y="30" class="layer-title">MCP连接获取层</text>
        <text x="80" y="50" class="description-text" fill="rgba(255,255,255,0.8)">职责：缓存未命中时，并发连接多个MCP服务器获取工具定义</text>
        <g transform="translate(90, 70)">
            <rect width="360" height="75" rx="12" fill="url(#componentGradient)"/><text x="180" y="20" class="component-title">MCPToolClient</text>
            <text x="10" y="45" class="description-text">并发策略: <tspan class="code-text">asyncio.gather(*tasks)</tspan></text>
            <text x="10" y="65" class="highlight-text">能力：高效的多服务器并发获取</text>
        </g>
        <g transform="translate(470, 70)">
            <rect width="360" height="75" rx="12" fill="url(#componentGradient)"/><text x="180" y="20" class="component-title">MultiServerMCPClient</text>
            <text x="10" y="45" class="description-text">协议调用: <tspan class="code-text">client.get_tools(server)</tspan></text>
            <text x="10" y="65" class="highlight-text">能力：标准MCP协议通信</text>
        </g>
        <g transform="translate(850, 70)">
            <rect width="360" height="75" rx="12" fill="url(#componentGradient)"/><text x="180" y="20" class="component-title">工具过滤与序列化</text>
            <text x="10" y="45" class="description-text">过滤机制: <tspan class="code-text">enabled_tools 白名单</tspan></text>
            <text x="10" y="65" class="highlight-text">能力：安全过滤并准备缓存数据</text>
        </g>
    </g>

    <g transform="translate(0, 760)">
        <rect x="60" y="0" width="1280" height="160" rx="16" fill="url(#layer1Gradient)" filter="url(#dropShadow)"/>
        <text x="80" y="30" class="layer-title">配置加载层</text>
        <text x="80" y="50" class="description-text" fill="rgba(255,255,255,0.8)">职责：读取YAML配置，构建标准MCP服务器配置和工具白名单</text>
        <g transform="translate(90, 70)">
            <rect width="360" height="75" rx="12" fill="url(#componentGradient)"/><text x="180" y="20" class="component-title">MCPToolConfig</text>
            <text x="10" y="45" class="description-text">配置入口: <tspan class="code-text">get_server_configs()</tspan></text>
            <text x="10" y="65" class="highlight-text">能力：解析YAML配置文件</text>
        </g>
        <g transform="translate(470, 70)">
            <rect width="360" height="75" rx="12" fill="url(#componentGradient)"/><text x="180" y="20" class="component-title">服务器配置构建</text>
            <text x="10" y="45" class="description-text">构建方法: <tspan class="code-text">_build_server_config()</tspan></text>
            <text x="10" y="65" class="highlight-text">能力：标准化配置并构建连接URL</text>
        </g>
        <g transform="translate(850, 70)">
            <rect width="360" height="75" rx="12" fill="url(#componentGradient)"/><text x="180" y="20" class="component-title">启用工具映射</text>
            <text x="10" y="45" class="description-text">映射方法: <tspan class="code-text">get_enabled_tools_map()</tspan></text>
            <text x="10" y="65" class="highlight-text">能力：生成服务器工具白名单映射</text>
        </g>
    </g>
</svg>