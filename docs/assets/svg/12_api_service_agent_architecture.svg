<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1100" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Gradients -->
    <linearGradient id="apiGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#3b82f6;"/><stop offset="100%" style="stop-color:#1d4ed8;"/></linearGradient>
    <linearGradient id="serviceGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#10b981;"/><stop offset="100%" style="stop-color:#059669;"/></linearGradient>
    <linearGradient id="agentGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#8b5cf6;"/><stop offset="100%" style="stop-color:#7c3aed;"/></linearGradient>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="0%" y2="100%"><stop offset="0%" style="stop-color:#f8fafc;"/><stop offset="100%" style="stop-color:#f1f5f9;"/></linearGradient>
    <linearGradient id="obsGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#64748b;"/><stop offset="100%" style="stop-color:#475569;"/></linearGradient>

    <!-- Styles -->
    <style>
      /* Fonts */
      .title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 32px; font-weight: 700; fill: #1e293b; }
      .subtitle { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 18px; fill: #64748b; font-weight: 500; letter-spacing: 0.5px; }
      .layer-title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 22px; font-weight: 600; fill: white; }
      .layer-desc { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 14px; fill: white; opacity: 0.9; }
      .component-title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 16px; font-weight: 600; fill: #1e293b; }
      .component-desc { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 12px; fill: #475569; }
      .flow-text { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 14px; fill: #334155; font-weight: 500; }
      .tech-title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 20px; font-weight: 600; fill: #1e293b; }
      .tech-subtitle { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 15px; font-weight: 600; fill: #f97316; }
      .tech-desc { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 13px; fill: #475569; }
      .tech-desc-bold { font-weight: 600; fill: #334155; }

      /* Shapes &amp; Arrows */
      .layer-box { rx: 16; filter: url(#dropShadow); }
      .component-box { rx: 10; stroke-width: 1.5; }
      .legend-box { fill: white; rx: 16; stroke: #e2e8f0; stroke-width: 1; filter: url(#subtleShadow); }
      .flow-arrow { stroke: #475569; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .data-arrow { stroke: #059669; stroke-width: 2.5; fill: none; marker-end: url(#arrowhead-data); stroke-dasharray: 6,4; }
      
      /* Marker Style */
      .marker-circle { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 14px; font-weight: 700; stroke: rgba(255,255,255,0.7); stroke-width: 2px; }
    </style>

    <!-- Markers &amp; Filters -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto"><polygon points="0 0, 10 3.5, 0 7" fill="#475569" /></marker>
    <marker id="arrowhead-data" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto"><polygon points="0 0, 8 3, 0 6" fill="#059669" /></marker>
    <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%"><feDropShadow dx="0" dy="5" stdDeviation="10" flood-color="#1e293b" flood-opacity="0.1"/></filter>
    <filter id="subtleShadow" x="-20%" y="-20%" width="140%" height="140%"><feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#1e293b" flood-opacity="0.06"/></filter>
  </defs>

  <!-- === BACKGROUND === -->
  <rect width="100%" height="100%" fill="url(#bgGradient)"/>

  <!-- === MAIN TITLE === -->
  <text x="800" y="60" text-anchor="middle" class="title">智能诊断系统架构</text>
  <text x="800" y="90" text-anchor="middle" class="subtitle">清晰分层 · 灵活扩展 · 全面可观测</text>

  <!-- === ARCHITECTURE DIAGRAM === -->
  <g id="main-architecture">
    <!-- API Layer -->
    <g id="api-layer"><rect x="250" y="150" width="700" height="100" fill="url(#apiGradient)" class="layer-box"/><text x="280" y="198" class="layer-title">🔗 API 层：统一入口</text></g>
    
    <!-- Service Layer -->
    <g id="service-layer"><rect x="250" y="300" width="700" height="300" fill="url(#serviceGradient)" class="layer-box"/><text x="280" y="348" class="layer-title">⚙️ Service 层：智能路由与编排</text><rect x="400" y="380" width="400" height="50" fill="rgba(255,255,255,0.3)" rx="8"/><text x="600" y="410" text-anchor="middle" style="fill:white; font-size:16px; font-weight:500;">ChatService: 核心编排器</text><path class="flow-arrow" d="M 500 430 C 500 450, 450 460, 450 480 V 520"/><text x="400" y="460" class="flow-text" style="fill:white;">实时处理</text><path class="flow-arrow" d="M 700 430 C 700 450, 750 460, 750 480 V 520"/><text x="760" y="460" class="flow-text" style="fill:white;">长任务处理</text><rect x="350" y="520" width="200" height="60" fill="white" class="component-box" stroke="#10b981"/><text x="450" y="545" text-anchor="middle" class="component-title">⚡️ 实时 Agent</text><text x="450" y="565" text-anchor="middle" class="component-desc">同步调用，快速响应</text><rect x="650" y="520" width="200" height="60" fill="white" class="component-box" stroke="#10b981"/><text x="750" y="545" text-anchor="middle" class="component-title">🕒 长时 Agent</text><text x="750" y="565" text-anchor="middle" class="component-desc">异步任务，后台执行</text></g>

    <!-- Agent Layer -->
    <g id="agent-layer"><rect x="250" y="640" width="700" height="220" fill="url(#agentGradient)" class="layer-box"/><text x="280" y="688" class="layer-title">🤖 Agent 层：能力工厂</text><rect x="300" y="730" width="220" height="100" fill="rgba(255,255,255,0.3)" rx="8"/><text x="410" y="760" text-anchor="middle" style="fill:white; font-size:16px; font-weight:500;">LangGraph 核心</text><text x="410" y="785" text-anchor="middle" style="fill:white; font-size:12px; opacity:0.8;">(State/MessageGraph)</text><rect x="550" y="730" width="160" height="100" fill="rgba(255,255,255,0.3)" rx="8"/><text x="630" y="760" text-anchor="middle" style="fill:white; font-size:16px; font-weight:500;">🧩 通用工具库</text><text x="630" y="785" text-anchor="middle" style="fill:white; font-size:12px; opacity:0.8;">(MCP Tools)</text><rect x="740" y="730" width="160" height="100" fill="rgba(255,255,255,0.3)" rx="8"/><text x="820" y="760" text-anchor="middle" style="fill:white; font-size:16px; font-weight:500;">具体 Agent</text><text x="820" y="785" text-anchor="middle" style="fill:white; font-size:12px; opacity:0.8;">(推理/诊断...)</text></g>
    
    <!-- Data &amp; Control Flow Arrows -->
    <path class="flow-arrow" d="M 600 250 V 300"/><text x="610" y="280" class="flow-text">Request</text>
    <path class="data-arrow" d="M 270 300 v -40 c 0,-15 -15,-15 -15,-15 h -80"/>
    <path class="flow-arrow" d="M 450 580 V 640"/><path class="flow-arrow" d="M 750 580 V 640"/><text x="610" y="615" class="flow-text">Agent Invocation</text>
  </g>

  <!-- === OBSERVABILITY SIDEBAR === -->
  <g id="observability-sidebar"><rect x="50" y="150" width="150" height="710" fill="url(#obsGradient)" class="layer-box"/><text x="125" y="520" text-anchor="middle" transform="rotate(-90 125 520)" class="layer-title" style="font-size:24px;">统一可观测性<tspan x="125" dy="1.4em" class="layer-desc" style="font-size:14px;">(日志 · 追踪 · 监控)</tspan></text></g>

  <!-- === LEGEND / DESIGN PRINCIPLES === -->
  <g id="tech-points">
    <rect x="1000" y="120" width="500" height="480" class="legend-box"/>
    <g transform="translate(1020, 150)">
        <text x="20" y="20" class="tech-title">核心设计原则</text>
        <g transform="translate(0, 80)"><text x="45" y="0" class="tech-subtitle">简化开发：通用化与分层</text><text x="45" y="25" class="tech-desc"><tspan class="tech-desc-bold">分层解耦：</tspan><tspan>三层职责清晰，独立演进。</tspan><tspan x="45" dy="1.4em" class="tech-desc-bold">通用API/Service：</tspan><tspan>一套服务支持多种Agent。</tspan></text></g>
        <g transform="translate(0, 185)"><text x="45" y="0" class="tech-subtitle">支持不同Agent运行形态</text><text x="45" y="25" class="tech-desc"><tspan>通过在Service层路由，无缝支持</tspan><tspan x="45" dy="1.4em"><tspan class="tech-desc-bold">实时Agent (同步)</tspan><tspan>和</tspan><tspan class="tech-desc-bold">长时Agent (异步)</tspan><tspan>。</tspan></tspan></text></g>
        <g transform="translate(0, 290)"><text x="45" y="0" class="tech-subtitle">统一的可观测能力</text><text x="45" y="25" class="tech-desc"><tspan>集成<tspan class="tech-desc-bold">Langfuse</tspan>等工具，实现所有层级、</tspan><tspan x="45" dy="1.4em">所有链路的端到端追踪、监控与日志记录。</tspan></text></g>
        <g transform="translate(0, 395)"><text x="45" y="0" class="tech-subtitle">快速构建复杂Agent</text><text x="45" y="25" class="tech-desc"><tspan>基于<tspan class="tech-desc-bold">LangGraph</tspan>将复杂流程定义为状态图，</tspan><tspan x="45" dy="1.4em">易于构建、调试和扩展多步骤的复杂Agent。</tspan></text></g>
    </g>
  </g>

  <!-- === MARKERS === -->
  <g id="markers" class="marker-circle">
    <!-- Markers for Legend List -->
    <g transform="translate(0, 80) translate(1020, 150)"><circle cx="20" cy="-5" r="12" fill="#f97316"/><text x="20" y="0" text-anchor="middle" fill="white">1</text></g>
    <g transform="translate(0, 185) translate(1020, 150)"><circle cx="20" cy="-5" r="12" fill="#f97316"/><text x="20" y="0" text-anchor="middle" fill="white">2</text></g>
    <g transform="translate(0, 290) translate(1020, 150)"><circle cx="20" cy="-5" r="12" fill="#f97316"/><text x="20" y="0" text-anchor="middle" fill="white">3</text></g>
    <g transform="translate(0, 395) translate(1020, 150)"><circle cx="20" cy="-5" r="12" fill="#f97316"/><text x="20" y="0" text-anchor="middle" fill="white">4</text></g>
    
    <!-- Pointers on the Diagram -->
    <g transform="translate(225, 505)"><circle cx="0" cy="0" r="12" fill="#f97316"/><text x="0" y="5" text-anchor="middle" fill="white">1</text></g>
    <g transform="translate(600, 480)"><circle cx="0" cy="0" r="12" fill="#f97316"/><text x="0" y="5" text-anchor="middle" fill="white">2</text></g>
    <g transform="translate(125, 175)"><circle cx="0" cy="0" r="12" fill="#f97316"/><text x="0" y="5" text-anchor="middle" fill="white">3</text></g>
    <g transform="translate(410, 840)"><circle cx="0" cy="0" r="12" fill="#f97316"/><text x="0" y="5" text-anchor="middle" fill="white">4</text></g>
  </g>
</svg>