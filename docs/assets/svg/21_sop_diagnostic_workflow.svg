<?xml version="1.0" encoding="UTF-8"?>
<svg width="1800" height="950" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Color scheme from 12_api_service_agent_architecture.svg -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="0%" y2="100%"><stop offset="0%" stop-color="#f8fafc"/><stop offset="100%" stop-color="#f1f5f9"/></linearGradient>
    <linearGradient id="step1Gradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#3b82f6;"/><stop offset="100%" style="stop-color:#1d4ed8;"/></linearGradient>
    <linearGradient id="step2Gradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#10b981;"/><stop offset="100%" style="stop-color:#059669;"/></linearGradient>
    <linearGradient id="step3Gradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#8b5cf6;"/><stop offset="100%" style="stop-color:#7c3aed;"/></linearGradient>
    <linearGradient id="step4Gradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#64748b;"/><stop offset="100%" style="stop-color:#475569;"/></linearGradient>

    <style>
      .title { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; font-size: 36px; font-weight: 700; fill: #1e293b; text-anchor: middle; }
      .subtitle { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; font-size: 18px; fill: #64748b; font-weight: 400; text-anchor: middle; }
      .stage-box { rx: 16; filter: url(#dropShadow); }
      .stage-title { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; font-size: 20px; font-weight: 600; fill: white; text-anchor: middle; }
      .stage-desc { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; font-size: 14px; fill: rgba(255,255,255,0.8); text-anchor: middle; }
      .tech-box { fill: rgba(255,255,255,0.8); backdrop-filter: blur(10px); rx: 12; stroke: rgba(0,0,0,0.05); stroke-width: 1; }
      .tech-title { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; font-size: 16px; font-weight: 600; fill: #1e293b; text-anchor: middle; }
      .tech-desc { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; font-size: 13px; fill: #475569; text-anchor: middle; }
      .tech-list-item { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif; font-size: 13px; fill: #334155; text-anchor: start; }
      .flow-arrow { stroke: #64748b; stroke-width: 3; fill: none; marker-end: url(#arrowhead); }
      .loop-arrow { stroke: #8b5cf6; stroke-width: 2.5; fill: none; marker-end: url(#arrowhead-loop); }
    </style>

    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9.5" refY="3.5" orient="auto"><polygon points="0 0, 10 3.5, 0 7" fill="#64748b" /></marker>
    <marker id="arrowhead-loop" markerWidth="8" markerHeight="6" refX="7.5" refY="3" orient="auto"><polygon points="0 0, 8 3, 0 6" fill="#8b5cf6" /></marker>
    <marker id="arrowhead-white" markerWidth="10" markerHeight="7" refX="9.5" refY="3.5" orient="auto"><polygon points="0 0, 10 3.5, 0 7" fill="white" /></marker>
    <filter id="dropShadow" x="-10%" y="-10%" width="120%" height="120%"><feDropShadow dx="0" dy="8" stdDeviation="12" flood-color="#1e293b" flood-opacity="0.08"/></filter>
  </defs>

  <rect width="100%" height="100%" fill="url(#bgGradient)"/>

  <text x="900" y="70" class="title">21. 诊断流程核心工作流 (SOP-Driven Diagnostic Workflow)</text>
  <text x="900" y="105" class="subtitle">基于SOP模板与LangGraph的自动化诊断引擎</text>

  <g id="workflow" transform="translate(50, 150)">
    <!-- Stage 1: SOP Routing -->
    <g id="stage1">
      <rect x="0" y="0" width="350" height="600" fill="url(#step1Gradient)" class="stage-box"/>
      <text x="175" y="50" class="stage-title">Step 1: SOP 路由</text>
      <text x="175" y="75" class="stage-desc">根据用户意图选择诊断方案</text>
      <g transform="translate(25, 120)">
        <rect x="0" y="0" width="300" height="120" class="tech-box"/>
        <text x="150" y="40" class="tech-title">用户输入 (User Query)</text>
        <text x="150" y="65" class="tech-desc">e.g., "我的实例i-xxx重启了"</text>
      </g>
      <path d="M 175 240 v 30" class="flow-arrow" stroke="white" marker-end="url(#arrowhead-white)"/>
      <g transform="translate(25, 270)">
        <rect x="0" y="0" width="300" height="140" class="tech-box"/>
        <text x="150" y="40" class="tech-title">匹配配置文件</text>
        <text x="150" y="65" class="tech-desc">sop_diagnosis_config.yaml</text>
        <text x="150" y="95" class="tech-desc" style="font-weight: 600; fill: #3b82f6;">识别规则 (scenario)</text>
        <text x="150" y="115" class="tech-desc" style="font-weight: 600; fill: #3b82f6;">示例问题 (example_user_queries)</text>
      </g>
    </g>
    <path d="M 350 300 h 50" class="flow-arrow"/>

    <!-- Stage 2: Planning -->
    <g id="stage2" transform="translate(400, 0)">
      <rect x="0" y="0" width="350" height="600" fill="url(#step2Gradient)" class="stage-box"/>
      <text x="175" y="50" class="stage-title">Step 2: 计划生成</text>
      <text x="175" y="75" class="stage-desc">加载SOP模板作为执行计划</text>
      <g transform="translate(25, 120)">
        <rect x="0" y="0" width="300" height="100" class="tech-box"/>
        <text x="150" y="40" class="tech-title">加载SOP模板</text>
        <text x="150" y="65" class="tech-desc">e.g., instance_restart_single.md</text>
      </g>
      <path d="M 175 220 v 30" class="flow-arrow" stroke="white" marker-end="url(#arrowhead-white)"/>
      <g transform="translate(25, 250)">
        <rect x="0" y="0" width="300" height="220" class="tech-box"/>
        <text x="150" y="30" class="tech-title">SOP Step 解剖</text>
        <text x="40" y="60" class="tech-list-item">
          <tspan x="40" dy="1.5em"><tspan font-weight="600">- 目标 (Goal):</tspan> 定义步骤目的</tspan>
          <tspan x="40" dy="1.5em"><tspan font-weight="600">- 工具 (Tool):</tspan> 指定执行工具</tspan>
          <tspan x="40" dy="1.5em"><tspan font-weight="600">- 输入参数:</tspan> 定义工具的输入</tspan>
          <tspan x="40" dy="1.5em"><tspan font-weight="600">- 分析要点:</tspan> 指导结果分析</tspan>
          <tspan x="40" dy="1.5em"><tspan font-weight="600">- 关键输出:</tspan> 定义需提取信息</tspan>
        </text>
      </g>
    </g>
    <path d="M 750 300 h 50" class="flow-arrow"/>

    <!-- Stage 3: Execution -->
    <g id="stage3" transform="translate(800, 0)">
      <rect x="0" y="0" width="500" height="600" fill="url(#step3Gradient)" class="stage-box"/>
      <text x="250" y="50" class="stage-title">Step 3: 计划执行</text>
      <text x="250" y="75" class="stage-desc">基于LangGraph的状态机循环</text>
      <g transform="translate(40, 155)">
        <rect x="0" y="0" width="420" height="290" rx="12" fill="rgba(255,255,255,0.2)"/>
        <text x="210" y="35" class="tech-title" style="fill:white;">LangGraph Execution Loop</text>
        <g transform="translate(60, 70)">
          <rect x="0" y="0" width="300" height="50" class="tech-box"/>
          <text x="150" y="30" class="tech-title">1. 调用工具 (Tool Call)</text>
        </g>
        <path d="M 210 120 v 20" class="loop-arrow"/>
        <g transform="translate(60, 140)">
          <rect x="0" y="0" width="300" height="50" class="tech-box"/>
          <text x="150" y="30" class="tech-title">2. 分析结果 (Result Analysis)</text>
        </g>
        <path d="M 210 190 v 20" class="loop-arrow"/>
        <g transform="translate(60, 210)">
          <rect x="0" y="0" width="300" height="50" class="tech-box"/>
          <text x="150" y="30" class="tech-title">3. 更新状态 (Update State)</text>
        </g>
        <path d="M 60 100 c -40 0, -40 80, 0 80 l 0 50" class="loop-arrow"/>
        <text x="20" y="165" class="tech-desc" style="fill:white;">Loop</text>
      </g>
    </g>
    <path d="M 1300 300 h 50" class="flow-arrow"/>

    <!-- Stage 4: Report Generation -->
    <g id="stage4" transform="translate(1350, 0)">
      <rect x="0" y="0" width="350" height="600" fill="url(#step4Gradient)" class="stage-box"/>
      <text x="175" y="50" class="stage-title">Step 4: 报告生成</text>
      <text x="175" y="75" class="stage-desc">汇总结果并生成报告</text>
      <g transform="translate(25, 120)">
        <rect x="0" y="0" width="300" height="350" class="tech-box"/>
        <text x="150" y="40" class="tech-title">报告结构 (Report Structure)</text>
        <text x="40" y="80" class="tech-list-item">
          <tspan x="40" dy="1.5em"><tspan font-weight="600">1. 问题摘要:</tspan> 结论先行，概述问题</tspan>
          <tspan x="40" dy="1.5em"><tspan font-weight="600">2. 诊断过程:</tspan> 按时间线展示步骤</tspan>
          <tspan x="40" dy="1.5em"><tspan font-weight="600">3. 关键发现:</tspan> 列出核心证据和数据</tspan>
          <tspan x="40" dy="1.5em"><tspan font-weight="600">4. 根本原因:</tspan> 清晰的RCA结论</tspan>
          <tspan x="40" dy="1.5em"><tspan font-weight="600">5. 优化建议:</tspan> 提供可行的解决方案</tspan>
        </text>
      </g>
    </g>
  </g>
</svg>