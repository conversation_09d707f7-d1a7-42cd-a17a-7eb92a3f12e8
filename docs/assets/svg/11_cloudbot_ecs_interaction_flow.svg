<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Modern Gradient Definitions -->
    <linearGradient id="systemGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="authGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="chatGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ef4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="queryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0891b2;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="responseGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="stepGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f1f5f9;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="containerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.7" />
    </linearGradient>

    <style>
      /* Modern Typography */
      .main-title {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 32px;
        font-weight: 700;
        fill: #1e293b;
        letter-spacing: -0.025em;
      }
      .subtitle {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 18px;
        fill: #64748b;
        font-weight: 400;
      }
      .section-title {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 20px;
        font-weight: 600;
        fill: #334155;
        letter-spacing: -0.025em;
      }
      .flow-title {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 18px;
        font-weight: 600;
        fill: #1e293b;
        letter-spacing: -0.025em;
      }
      .system-text {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 16px;
        fill: white;
        font-weight: 600;
        letter-spacing: -0.025em;
      }
      .api-text {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 13px;
        fill: white;
        font-weight: 500;
      }
      .flow-text {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 12px;
        fill: #475569;
        font-weight: 500;
      }
      .arch-point-title {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 14px;
        font-weight: 600;
        fill: #1e293b;
        letter-spacing: -0.025em;
      }
      .arch-point-text {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 13px;
        fill: #64748b;
        font-weight: 400;
      }

      /* Modern Shapes and Boxes */
      .system-box {
        fill: url(#systemGradient);
        stroke: none;
        rx: 20;
        filter: url(#modernShadow);
      }
      .api-box {
        stroke: none;
        rx: 12;
        filter: url(#softShadow);
      }
      .auth-box { fill: url(#authGradient); }
      .chat-box { fill: url(#chatGradient); }
      .query-box { fill: url(#queryGradient); }
      .response-box {
        fill: url(#responseGradient);
        stroke: none;
        rx: 8;
        filter: url(#softShadow);
      }
      .response-text {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 12px;
        fill: white;
        font-weight: 500;
      }
      .step-circle {
        fill: url(#stepGradient);
        stroke: none;
        filter: url(#modernShadow);
      }
      .step-number {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 16px;
        fill: white;
        font-weight: 700;
      }

      /* Modern Lines and Borders */
      .flow-arrow {
        stroke: #64748b;
        stroke-width: 2.5;
        fill: none;
        marker-end: url(#arrowhead);
        transition: all 0.3s ease;
      }
      .flow-arrow:hover {
        stroke: #334155;
        stroke-width: 3;
      }
      .dashed-container {
        fill: url(#containerGradient);
        stroke: #cbd5e1;
        stroke-width: 2;
        stroke-dasharray: 12,6;
        rx: 20;
        filter: url(#softShadow);
      }
    </style>

    <!-- Modern Arrowhead Marker -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#64748b" />
    </marker>

    <!-- Modern Shadow Filters -->
    <filter id="modernShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.08"/>
      <feDropShadow dx="0" dy="1" stdDeviation="2" flood-color="#000000" flood-opacity="0.06"/>
    </filter>

    <filter id="softShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>

  <!-- Background -->
  <rect width="100%" height="100%" fill="url(#bgGradient)"/>

  <!-- Header Section -->
  <g id="header">
    <text x="700" y="50" text-anchor="middle" class="main-title">CloudBot UI 与 ECS Deep Diagnose 交互架构</text>
    <text x="700" y="80" text-anchor="middle" class="subtitle">智能诊断系统前后端 API 调用流程图</text>
  </g>

  <!-- Architecture Points Section -->
  <g id="architecture-points">
    <text x="700" y="130" text-anchor="middle" class="section-title">🗝️ 核心架构要点</text>
    <rect x="60" y="150" width="1280" height="90" fill="url(#containerGradient)" stroke="#e2e8f0" stroke-width="1" rx="20" filter="url(#softShadow)"/>

    <!-- Architecture Point Cards -->
    <g transform="translate(120, 175)">
      <rect width="260" height="50" fill="rgba(139, 92, 246, 0.1)" stroke="#8b5cf6" stroke-width="1" rx="12"/>
      <text x="20" y="20" class="arch-point-title">🔐 安全认证</text>
      <text x="20" y="38" class="arch-point-text">JWT Token + 访问密钥验证</text>
    </g>

    <g transform="translate(420, 175)">
      <rect width="260" height="50" fill="rgba(6, 182, 212, 0.1)" stroke="#06b6d4" stroke-width="1" rx="12"/>
      <text x="20" y="20" class="arch-point-title">📡 实时通信</text>
      <text x="20" y="38" class="arch-point-text">Server-Sent Events (SSE) 流式响应</text>
    </g>

    <g transform="translate(720, 175)">
      <rect width="260" height="50" fill="rgba(16, 185, 129, 0.1)" stroke="#10b981" stroke-width="1" rx="12"/>
      <text x="20" y="20" class="arch-point-title">🔄 会话管理</text>
      <text x="20" y="38" class="arch-point-text">上下文保持与历史记录查询</text>
    </g>

    <g transform="translate(1020, 175)">
      <rect width="260" height="50" fill="rgba(245, 158, 11, 0.1)" stroke="#f59e0b" stroke-width="1" rx="12"/>
      <text x="20" y="20" class="arch-point-title">🔧 核心技术栈</text>
      <text x="20" y="38" class="arch-point-text">FastAPI, LangChain, Celery, Redis</text>
    </g>
  </g>

  <!-- Main System Columns -->

  <!-- CloudBot UI (Client Side) -->
  <g id="cloudbot-ui">
    <rect x="60" y="280" width="300" height="650" class="system-box"/>
    <text x="210" y="320" text-anchor="middle" class="system-text">🖥️ CloudBot UI</text>
    <text x="210" y="345" text-anchor="middle" class="system-text" style="font-size: 14px; font-weight: 400; opacity: 0.9;">智能诊断前端界面</text>

    <!-- User Action Cards -->
    <g transform="translate(85, 400)">
      <rect width="250" height="100" rx="16" fill="rgba(255,255,255,0.9)" stroke="#e2e8f0" stroke-width="1" filter="url(#softShadow)"/>
      <text x="125" y="30" text-anchor="middle" style="font-size: 16px; font-weight: 600; fill: #1e293b;">用户交互操作</text>
      <circle cx="40" cy="60" r="12" fill="#3b82f6"/>
      <text x="40" y="66" text-anchor="middle" style="font-size: 12px; fill: white; font-weight: 600;">1</text>
      <text x="70" y="66" style="font-size: 14px; fill: #475569; font-weight: 500;">💬 发起新对话</text>
    </g>

    <g transform="translate(85, 680)">
      <rect width="250" height="100" rx="16" fill="rgba(255,255,255,0.9)" stroke="#e2e8f0" stroke-width="1" filter="url(#softShadow)"/>
      <text x="125" y="30" text-anchor="middle" style="font-size: 16px; font-weight: 600; fill: #1e293b;">历史查询操作</text>
      <circle cx="40" cy="60" r="12" fill="#06b6d4"/>
      <text x="40" y="66" text-anchor="middle" style="font-size: 12px; fill: white; font-weight: 600;">2</text>
      <text x="70" y="66" style="font-size: 14px; fill: #475569; font-weight: 500;">📋 查询对话历史</text>
    </g>
  </g>

  <!-- ECS Deep Diagnose (Server Side) -->
  <g id="ecs-deep-diagnose">
    <rect x="1040" y="280" width="300" height="650" class="system-box"/>
    <text x="1190" y="320" text-anchor="middle" class="system-text">⚙️ ECS Deep Diagnose</text>
    <text x="1190" y="345" text-anchor="middle" class="system-text" style="font-size: 14px; font-weight: 400; opacity: 0.9;">智能诊断后端服务</text>

    <!-- API Endpoints Card -->
    <g transform="translate(1065, 380)">
      <rect width="250" height="220" rx="16" fill="rgba(255,255,255,0.9)" stroke="#e2e8f0" stroke-width="1" filter="url(#softShadow)"/>
      <text x="125" y="30" text-anchor="middle" style="font-size: 16px; font-weight: 600; fill: #1e293b;">API 服务端点</text>

      <g transform="translate(20, 50)">
        <rect width="210" height="35" rx="8" fill="rgba(139, 92, 246, 0.1)" stroke="#8b5cf6" stroke-width="1"/>
        <text x="15" y="23" class="api-text" style="fill:#7c3aed; font-weight: 600;">🔐 /api/token</text>
      </g>

      <g transform="translate(20, 95)">
        <rect width="210" height="35" rx="8" fill="rgba(239, 68, 68, 0.1)" stroke="#ef4444" stroke-width="1"/>
        <text x="15" y="23" class="api-text" style="fill:#dc2626; font-weight: 600;">💬 /api/v1/chat</text>
      </g>

      <g transform="translate(20, 140)">
        <rect width="210" height="35" rx="8" fill="rgba(6, 182, 212, 0.1)" stroke="#06b6d4" stroke-width="1"/>
        <text x="15" y="23" class="api-text" style="fill:#0891b2; font-weight: 600;">📋 /api/v1/chat/sessions</text>
      </g>

      <g transform="translate(20, 185)">
        <rect width="210" height="35" rx="8" fill="rgba(6, 182, 212, 0.1)" stroke="#06b6d4" stroke-width="1"/>
        <text x="15" y="23" class="api-text" style="fill:#0891b2; font-weight: 600;">📄 /.../messages</text>
      </g>
    </g>
  </g>

  <!-- Central Interaction Flow -->
  <g id="interaction-flow">
    <!-- Real-time Chat Flow -->
    <rect x="400" y="280" width="600" height="320" class="dashed-container"/>
    <text x="700" y="310" text-anchor="middle" class="flow-title">🔄 实时对话交互流程</text>

    <!-- Step 1: Authentication -->
    <g transform="translate(450, 350)">
      <circle cx="0" cy="0" r="22" class="step-circle"/>
      <text x="0" y="6" text-anchor="middle" class="step-number">1</text>
      <rect x="40" y="-20" width="200" height="40" class="api-box auth-box"/>
      <text x="140" y="5" text-anchor="middle" class="api-text">POST /api/token</text>
    </g>

    <!-- Auth Flow Arrow -->
    <path d="M 360 450 Q 400 350, 430 350" class="flow-arrow" />
    <text x="380" y="390" class="flow-text">🔐 身份认证</text>

    <!-- Auth Response -->
    <path d="M 690 350 Q 830 350, 1020 440" class="flow-arrow" />
    <g transform="translate(750, 330)">
      <rect width="140" height="32" class="response-box"/>
      <text x="70" y="20" text-anchor="middle" class="response-text">✅ JWT Token 返回</text>
    </g>

    <!-- Step 2: Chat Message -->
    <g transform="translate(450, 450)">
      <circle cx="0" cy="0" r="22" class="step-circle"/>
      <text x="0" y="6" text-anchor="middle" class="step-number">2</text>
      <rect x="40" y="-20" width="200" height="40" class="api-box chat-box"/>
      <text x="140" y="5" text-anchor="middle" class="api-text">POST /api/v1/chat</text>
    </g>

    <!-- Chat Flow Arrow -->
    <path d="M 360 450 Q 400 450, 430 450" class="flow-arrow" />
    <text x="380" y="440" class="flow-text">💬 发送诊断请求</text>

    <!-- Chat Response (Streaming) -->
    <path d="M 690 450 Q 850 450, 1040 480" class="flow-arrow" />
    <g transform="translate(750, 470)">
      <rect width="160" height="32" class="response-box"/>
      <text x="80" y="20" text-anchor="middle" class="response-text">🌊 SSE 流式响应</text>
    </g>

    <!-- History Query Flow -->
    <rect x="400" y="630" width="600" height="300" class="dashed-container"/>
    <text x="700" y="660" text-anchor="middle" class="flow-title">📋 历史查询交互流程</text>

    <!-- Step 3: Get Sessions -->
    <g transform="translate(450, 720)">
      <circle cx="0" cy="0" r="22" class="step-circle"/>
      <text x="0" y="6" text-anchor="middle" class="step-number">3</text>
      <rect x="40" y="-20" width="240" height="40" class="api-box query-box"/>
      <text x="160" y="5" text-anchor="middle" class="api-text">GET /api/v1/chat/sessions</text>
    </g>

    <!-- Sessions Flow Arrow -->
    <path d="M 360 720 Q 400 720, 430 720" class="flow-arrow" />
    <text x="360" y="710" class="flow-text">📋 获取会话列表</text>

    <!-- Sessions Response -->
    <path d="M 730 720 Q 850 720, 1040 520" class="flow-arrow" />
    <g transform="translate(770, 700)">
      <rect width="140" height="32" class="response-box"/>
      <text x="70" y="20" text-anchor="middle" class="response-text">📄 会话列表数据</text>
    </g>

    <!-- Step 4: Get Messages -->
    <g transform="translate(450, 820)">
      <circle cx="0" cy="0" r="22" class="step-circle"/>
      <text x="0" y="6" text-anchor="middle" class="step-number">4</text>
      <rect x="40" y="-20" width="280" height="40" class="api-box query-box"/>
      <text x="180" y="5" text-anchor="middle" class="api-text">GET /.../sessions/{id}/messages</text>
    </g>

    <!-- Messages Flow Arrow -->
    <path d="M 360 780 Q 400 820, 430 820" class="flow-arrow" />
    <text x="350" y="800" class="flow-text">📝 获取具体消息</text>

    <!-- Messages Response -->
    <path d="M 770 820 Q 850 820, 1040 560" class="flow-arrow" />
    <g transform="translate(810, 800)">
      <rect width="140" height="32" class="response-box"/>
      <text x="70" y="20" text-anchor="middle" class="response-text">💬 消息历史数据</text>
    </g>
  </g>

</svg>