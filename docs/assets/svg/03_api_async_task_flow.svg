<?xml version="1.0" encoding="UTF-8"?>
<svg width="1000" height="600" viewBox="0 0 1000 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 现代渐变色彩定义 -->
    <linearGradient id="clientGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="apiGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="serviceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="storageGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
    
    <!-- 现代阴影滤镜 -->
    <filter id="modernShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.08"/>
      <feDropShadow dx="0" dy="1" stdDeviation="2" flood-color="#000000" flood-opacity="0.06"/>
    </filter>
    
    <!-- 现代箭头标记 -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" 
            refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#64748b" />
    </marker>
    
    <!-- 网格图案 -->
    <pattern id="modernGrid" width="30" height="30" patternUnits="userSpaceOnUse">
      <path d="M 30 0 L 0 0 0 30" fill="none" stroke="#e5e7eb" stroke-width="0.5" opacity="0.4"/>
    </pattern>
  </defs>
  
  <!-- 现代背景 -->
  <rect width="1000" height="600" fill="#fafbfc"/>
  <rect width="1000" height="600" fill="url(#modernGrid)"/>
  
  <!-- 标题区域 -->
  <rect x="50" y="20" width="900" height="60" rx="16" fill="white" 
        stroke="#e5e7eb" stroke-width="1" filter="url(#modernShadow)"/>
  <text x="500" y="45" text-anchor="middle" font-family="Inter, -apple-system, sans-serif" 
        font-size="24" font-weight="700" fill="#111827">API 异步任务流程</text>
  <text x="500" y="65" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="12" font-weight="400" fill="#6b7280">任务创建与异步执行流程图</text>
  
  <!-- 客户端 -->
  <rect x="80" y="150" width="120" height="80" rx="12" fill="url(#clientGradient)" filter="url(#modernShadow)"/>
  <text x="140" y="180" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="14" font-weight="600" fill="white">客户端</text>
  <text x="140" y="200" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="11" font-weight="400" fill="white">(Client)</text>
  
  <!-- API 层 -->
  <rect x="280" y="120" width="180" height="140" rx="12" fill="url(#apiGradient)" filter="url(#modernShadow)"/>
  <text x="370" y="145" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="16" font-weight="600" fill="white">API 层</text>
  
  <!-- API 路由组件 -->
  <rect x="300" y="170" width="140" height="40" rx="8" fill="rgba(255,255,255,0.2)" 
        stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
  <text x="370" y="185" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="11" font-weight="500" fill="white">路由</text>
  <text x="370" y="200" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="10" font-weight="400" fill="white">/api/v1/tasks</text>
  
  <!-- 服务层 -->
  <rect x="540" y="120" width="180" height="140" rx="12" fill="url(#serviceGradient)" filter="url(#modernShadow)"/>
  <text x="630" y="145" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="16" font-weight="600" fill="white">服务层</text>
  
  <!-- TaskService 组件 -->
  <rect x="560" y="170" width="140" height="35" rx="8" fill="rgba(255,255,255,0.2)" 
        stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
  <text x="630" y="192" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="11" font-weight="500" fill="white">TaskService</text>
  
  <!-- TaskExecutor 组件 -->
  <rect x="560" y="215" width="140" height="35" rx="8" fill="rgba(255,255,255,0.2)" 
        stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
  <text x="630" y="237" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="11" font-weight="500" fill="white">TaskExecutor</text>
  
  <!-- Redis 存储 -->
  <rect x="800" y="150" width="120" height="80" rx="12" fill="url(#storageGradient)" filter="url(#modernShadow)"/>
  <text x="860" y="180" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="14" font-weight="600" fill="white">Redis</text>
  <text x="860" y="200" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="11" font-weight="400" fill="white">任务数据存储</text>
  
  <!-- 流程箭头和标签 -->
  <!-- 1. 创建任务请求 -->
  <path d="M 200 180 L 270 180" stroke="#64748b" stroke-width="2" 
        marker-end="url(#arrowhead)" fill="none"/>
  <text x="235" y="170" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="10" fill="#374151">1. POST /api/v1/tasks</text>
  
  <!-- 2. 调用服务 -->
  <path d="M 460 190 L 530 190" stroke="#64748b" stroke-width="2" 
        marker-end="url(#arrowhead)" fill="none"/>
  <text x="495" y="180" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="10" fill="#374151">2. 调用 create_task</text>
  
  <!-- 3. 存储任务 -->
  <path d="M 720 190 L 790 190" stroke="#64748b" stroke-width="2" 
        marker-end="url(#arrowhead)" fill="none"/>
  <text x="755" y="180" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="10" fill="#374151">3. 存储任务信息</text>
  
  <!-- 4. 返回任务ID -->
  <path d="M 530 210 L 460 210" stroke="#64748b" stroke-width="2" 
        marker-end="url(#arrowhead)" fill="none"/>
  <path d="M 270 210 L 200 210" stroke="#64748b" stroke-width="2" 
        marker-end="url(#arrowhead)" fill="none"/>
  <text x="350" y="225" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="10" fill="#374151">4. 返回 Task ID</text>
  
  <!-- 5. 异步执行 -->
  <path d="M 630 260 L 630 320" stroke="#64748b" stroke-width="2" 
        marker-end="url(#arrowhead)" fill="none"/>
  <text x="680" y="290" font-family="Inter, sans-serif" 
        font-size="10" fill="#374151">5. 异步触发执行</text>
  
  <!-- 异步执行区域 -->
  <rect x="480" y="340" width="300" height="80" rx="12" fill="rgba(139, 92, 246, 0.1)" 
        stroke="#8b5cf6" stroke-width="2" stroke-dasharray="5,5"/>
  <text x="630" y="365" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="14" font-weight="600" fill="#7c3aed">异步执行区域</text>
  <text x="630" y="385" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="11" fill="#7c3aed">工作流执行 • 智能体协作</text>
  <text x="630" y="405" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="11" fill="#7c3aed">状态更新 • 结果存储</text>
  
  <!-- 6. 更新状态 -->
  <path d="M 780 380 L 860 230" stroke="#64748b" stroke-width="2" 
        marker-end="url(#arrowhead)" fill="none"/>
  <text x="820" y="300" font-family="Inter, sans-serif" 
        font-size="10" fill="#374151">6. 更新任务状态</text>
  
  <!-- 时间线 -->
  <rect x="50" y="480" width="900" height="80" rx="12" fill="white" 
        stroke="#e5e7eb" stroke-width="1" filter="url(#modernShadow)"/>
  <text x="500" y="505" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="14" font-weight="600" fill="#111827">执行时间线</text>
  
  <!-- 时间线步骤 -->
  <circle cx="150" cy="530" r="8" fill="#3b82f6"/>
  <text x="150" y="550" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="9" fill="#374151">请求创建</text>
  
  <circle cx="350" cy="530" r="8" fill="#10b981"/>
  <text x="350" y="550" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="9" fill="#374151">任务入队</text>
  
  <circle cx="550" cy="530" r="8" fill="#8b5cf6"/>
  <text x="550" y="550" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="9" fill="#374151">异步执行</text>
  
  <circle cx="750" cy="530" r="8" fill="#f59e0b"/>
  <text x="750" y="550" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="9" fill="#374151">状态更新</text>
  
  <!-- 时间线连接 -->
  <path d="M 158 530 L 342 530" stroke="#e5e7eb" stroke-width="2"/>
  <path d="M 358 530 L 542 530" stroke="#e5e7eb" stroke-width="2"/>
  <path d="M 558 530 L 742 530" stroke="#e5e7eb" stroke-width="2"/>
  
</svg>