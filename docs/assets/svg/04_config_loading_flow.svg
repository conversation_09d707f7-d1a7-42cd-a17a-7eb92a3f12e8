<?xml version="1.0" encoding="UTF-8"?>
<svg width="900" height="600" viewBox="0 0 900 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 现代渐变色彩定义 -->
    <linearGradient id="sourceGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2563eb;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="processGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="resultGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="envGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>
    
    <!-- 柔和阴影滤镜 -->
    <filter id="softShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
    
    <!-- 现代箭头标记 -->
    <marker id="arrowhead" markerWidth="8" markerHeight="6" 
            refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#6b7280" />
    </marker>
  </defs>

  
  <!-- 清爽背景 -->
  <rect width="900" height="600" fill="#f8fafc"/>
  
  <!-- 标题区域 -->
  <rect x="50" y="30" width="800" height="60" rx="16" fill="white" 
        stroke="#e2e8f0" stroke-width="1" filter="url(#softShadow)"/>
  <text x="450" y="55" text-anchor="middle" font-family="Inter, -apple-system, sans-serif" 
        font-size="24" font-weight="700" fill="#1e293b">配置加载流程</text>
  <text x="450" y="75" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="12" font-weight="400" fill="#64748b">动态配置管理与加载机制</text>
  
  <!-- 配置源区域 -->
  <rect x="50" y="120" width="800" height="100" rx="12" fill="#f1f5f9" 
        stroke="#cbd5e1" stroke-width="1" opacity="0.5"/>
  <text x="70" y="145" font-family="Inter, sans-serif" font-size="14" font-weight="600" fill="#475569">
    配置源</text>
  
  <!-- YAML 配置文件 -->
  <rect x="100" y="150" width="180" height="60" rx="10" fill="url(#sourceGradient)" filter="url(#softShadow)"/>
  <text x="190" y="175" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="12" font-weight="600" fill="white">YAML 配置文件</text>
  <text x="190" y="190" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="10" font-weight="400" fill="white">config_daily.yaml</text>
  <text x="190" y="202" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="10" font-weight="400" fill="white">config_prod.yaml</text>
  
  <!-- 环境变量 -->
  <rect x="320" y="150" width="180" height="60" rx="10" fill="url(#envGradient)" filter="url(#softShadow)"/>
  <text x="410" y="175" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="12" font-weight="600" fill="white">环境变量</text>
  <text x="410" y="190" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="10" font-weight="400" fill="white">Environment Variables</text>
  <text x="410" y="202" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="10" font-weight="400" fill="white">运行时覆盖</text>
  
  <!-- 动态配置 -->
  <rect x="540" y="150" width="180" height="60" rx="10" fill="url(#resultGradient)" filter="url(#softShadow)"/>
  <text x="630" y="175" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="12" font-weight="600" fill="white">动态配置</text>
  <text x="630" y="190" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="10" font-weight="400" fill="white">Runtime Config</text>
  <text x="630" y="202" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="10" font-weight="400" fill="white">实时注入</text>
  
  <!-- 处理流程区域 -->
  <rect x="50" y="260" width="800" height="120" rx="12" fill="#f0f9ff" 
        stroke="#bae6fd" stroke-width="1" opacity="0.5"/>
  <text x="70" y="285" font-family="Inter, sans-serif" font-size="14" font-weight="600" fill="#0369a1">
    处理流程</text>
  
  <!-- 配置加载器 -->
  <rect x="200" y="300" width="500" height="60" rx="10" fill="url(#processGradient)" filter="url(#softShadow)"/>
  <text x="450" y="320" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="14" font-weight="600" fill="white">配置加载流程</text>
  <text x="280" y="340" font-family="Inter, sans-serif" 
        font-size="10" font-weight="400" fill="white">1. 加载 YAML</text>
  <text x="380" y="340" font-family="Inter, sans-serif" 
        font-size="10" font-weight="400" fill="white">2. 环境变量覆盖</text>
  <text x="500" y="340" font-family="Inter, sans-serif" 
        font-size="10" font-weight="400" fill="white">3. 动态配置注入</text>
  <text x="600" y="340" font-family="Inter, sans-serif" 
        font-size="10" font-weight="400" fill="white">4. 缓存结果</text>
  
  <!-- 结果区域 -->
  <rect x="50" y="420" width="800" height="100" rx="12" fill="#f0fdf4" 
        stroke="#bbf7d0" stroke-width="1" opacity="0.5"/>
  <text x="70" y="445" font-family="Inter, sans-serif" font-size="14" font-weight="600" fill="#15803d">
    配置结果</text>
  
  <!-- 最终配置对象 -->
  <rect x="300" y="460" width="200" height="50" rx="10" fill="url(#resultGradient)" filter="url(#softShadow)"/>
  <text x="400" y="480" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="12" font-weight="600" fill="white">最终配置</text>
  <text x="400" y="495" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="10" font-weight="400" fill="white">DotDict 对象</text>
  
  <!-- get_config() 调用 -->
  <rect x="100" y="460" width="120" height="50" rx="10" fill="url(#sourceGradient)" filter="url(#softShadow)"/>
  <text x="160" y="480" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="11" font-weight="600" fill="white">get_config()</text>
  <text x="160" y="495" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="9" font-weight="400" fill="white">配置获取接口</text>
  
  <!-- 配置缓存 -->
  <rect x="580" y="460" width="120" height="50" rx="10" fill="url(#envGradient)" filter="url(#softShadow)"/>
  <text x="640" y="480" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="11" font-weight="600" fill="white">配置缓存</text>
  <text x="640" y="495" text-anchor="middle" font-family="Inter, sans-serif" 
        font-size="9" font-weight="400" fill="white">内存缓存</text>
  
  <!-- 流程箭头 -->
  <!-- 配置源到处理器 -->
  <path d="M 190 210 L 280 290" stroke="#6b7280" stroke-width="2" 
        marker-end="url(#arrowhead)" fill="none"/>
  <path d="M 410 210 L 410 290" stroke="#6b7280" stroke-width="2" 
        marker-end="url(#arrowhead)" fill="none"/>
  <path d="M 630 210 L 620 290" stroke="#6b7280" stroke-width="2" 
        marker-end="url(#arrowhead)" fill="none"/>
  
  <!-- 处理器到结果 -->
  <path d="M 450 360 L 450 410" stroke="#6b7280" stroke-width="2" 
        marker-end="url(#arrowhead)" fill="none"/>
  
  <!-- get_config 调用 -->
  <path d="M 220 485 L 290 485" stroke="#6b7280" stroke-width="2" 
        marker-end="url(#arrowhead)" fill="none"/>
  
  <!-- 缓存连接 -->
  <path d="M 500 485 L 570 485" stroke="#6b7280" stroke-width="2" 
        marker-end="url(#arrowhead)" fill="none"/>
  
  <!-- 说明区域 -->
  <rect x="50" y="540" width="800" height="50" rx="12" fill="white" 
        stroke="#e2e8f0" stroke-width="1" filter="url(#softShadow)"/>
  <text x="70" y="560" font-family="Inter, sans-serif" font-size="12" font-weight="600" fill="#1e293b">
    配置特性：</text>
  <text x="70" y="575" font-family="Inter, sans-serif" font-size="10" fill="#64748b">
    • 支持多环境配置 • 环境变量优先级覆盖 • 动态配置热更新 • 内存缓存优化 • 类型安全的配置访问</text>
  
</svg>