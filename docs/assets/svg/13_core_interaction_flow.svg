<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="2100" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
  <defs>
    <!-- Modern Gradient Definitions (采用11文件的配色方案) -->
    <linearGradient id="systemGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="authGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="chatGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ef4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="queryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#06b6d4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0891b2;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="responseGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="stepGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f1f5f9;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="containerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.7" />
    </linearGradient>

    <!-- 保留原有的渐变，但使用新的配色 -->
    <linearGradient id="syncGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="asyncGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7c3aed;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="decisionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d97706;stop-opacity:1" />
    </linearGradient>

    <linearGradient id="dataGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#059669;stop-opacity:1" />
    </linearGradient>

    <style>
      /* Modern Typography (采用11文件的字体样式) */
      .main-title {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 32px;
        font-weight: 700;
        fill: #1e293b;
        letter-spacing: -0.025em;
      }
      .subtitle {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 18px;
        fill: #64748b;
        font-weight: 400;
      }
      .section-title {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 20px;
        font-weight: 600;
        fill: #334155;
        letter-spacing: -0.025em;
      }
      .flow-title {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 18px;
        font-weight: 600;
        fill: #1e293b;
        letter-spacing: -0.025em;
      }
      .system-text {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 16px;
        fill: white;
        font-weight: 600;
        letter-spacing: -0.025em;
      }
      .api-text {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 13px;
        fill: white;
        font-weight: 500;
      }
      .flow-text {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 12px;
        fill: #475569;
        font-weight: 500;
      }
      .arch-point-title {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 14px;
        font-weight: 600;
        fill: #1e293b;
        letter-spacing: -0.025em;
      }
      .arch-point-text {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 13px;
        fill: #64748b;
        font-weight: 400;
      }
      .feature-title {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 20px;
        font-weight: 600;
        fill: #1e293b;
      }
      .feature-text {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 14px;
        fill: #475569;
      }
      .feature-text-bold {
        font-weight: 600;
        fill: #334155;
      }
      .stage-title {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 18px;
        font-weight: 600;
        fill: white;
      }
      .stage-desc {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 14px;
        font-weight: 500;
        fill: white;
        opacity: 0.9;
      }
      .stage-impl {
        font-family: 'JetBrains Mono', 'Fira Code', monospace;
        font-size: 12px;
        fill: #e0f2fe;
        opacity: 0.9;
      }
      .flow-label {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 14px;
        font-weight: 500;
      }
      .code-title {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 16px;
        font-weight: 600;
        fill: #334155;
      }
      .code-text {
        font-family: 'JetBrains Mono', 'Fira Code', monospace;
        font-size: 12px;
        fill: #334155;
      }

      /* Modern Shapes and Boxes (采用11文件的样式) */
      .system-box {
        fill: url(#systemGradient);
        stroke: none;
        rx: 20;
        filter: url(#modernShadow);
      }
      .api-box {
        stroke: none;
        rx: 12;
        filter: url(#softShadow);
      }
      .auth-box { fill: url(#authGradient); }
      .chat-box { fill: url(#chatGradient); }
      .query-box { fill: url(#queryGradient); }
      .response-box {
        fill: url(#responseGradient);
        stroke: none;
        rx: 8;
        filter: url(#softShadow);
      }
      .response-text {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 12px;
        fill: white;
        font-weight: 500;
      }
      .step-circle {
        fill: url(#stepGradient);
        stroke: none;
        filter: url(#modernShadow);
      }
      .step-number {
        font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
        font-size: 16px;
        fill: white;
        font-weight: 700;
      }

      /* Modern Lines and Borders */
      .flow-arrow {
        stroke: #64748b;
        stroke-width: 2.5;
        fill: none;
        marker-end: url(#arrowhead);
        transition: all 0.3s ease;
      }
      .flow-arrow:hover {
        stroke: #334155;
        stroke-width: 3;
      }
      .dashed-container {
        fill: url(#containerGradient);
        stroke: #cbd5e1;
        stroke-width: 2;
        stroke-dasharray: 12,6;
        rx: 20;
        filter: url(#softShadow);
      }
      .card {
        rx: 20;
        filter: url(#modernShadow);
      }
      .stage-box {
        rx: 16;
      }
      .data-arrow {
        stroke: #10b981;
        stroke-dasharray: 8,4;
        stroke-width: 2.5;
        fill: none;
        marker-end: url(#arrowhead-data);
      }
    </style>

    <!-- Modern Arrowhead Marker (采用11文件的箭头样式) -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#64748b" />
    </marker>

    <marker id="arrowhead-data" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#10b981" />
    </marker>

    <!-- Modern Shadow Filters (采用11文件的阴影效果) -->
    <filter id="modernShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.08"/>
      <feDropShadow dx="0" dy="1" stdDeviation="2" flood-color="#000000" flood-opacity="0.06"/>
    </filter>

    <filter id="softShadow" x="-50%" y="-50%" width="200%" height="200%">
      <feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#000000" flood-opacity="0.1"/>
    </filter>

    <!-- 保留原有的dropShadow滤镜以兼容现有元素 -->
    <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="8" stdDeviation="15" flood-color="#1e293b" flood-opacity="0.08"/>
    </filter>
  </defs>

  <!-- === BACKGROUND === -->
  <rect width="100%" height="100%" fill="url(#bgGradient)"/>

  <!-- Header Section (采用11文件的现代化标题样式) -->
  <g id="header">
    <text x="800" y="50" text-anchor="middle" class="main-title">核心交互流程：同步 vs 异步</text>
    <text x="800" y="80" text-anchor="middle" class="subtitle">API/Service/Agent 架构下的两种核心处理模式</text>
  </g>

  <!-- Architecture Points Section (采用11文件的架构要点样式) -->
  <g id="architecture-points">
    <text x="800" y="130" text-anchor="middle" class="section-title">🚀 核心设计亮点</text>
    <rect x="100" y="150" width="1400" height="120" fill="url(#containerGradient)" stroke="#e2e8f0" stroke-width="1" rx="20" filter="url(#softShadow)"/>

    <!-- Architecture Point Cards -->
    <g transform="translate(150, 180)">
      <rect width="320" height="60" fill="rgba(139, 92, 246, 0.1)" stroke="#8b5cf6" stroke-width="1" rx="12"/>
      <text x="20" y="25" class="arch-point-title">🛡️ 可靠的入口</text>
      <text x="20" y="45" class="arch-point-text">统一的API网关进行认证与验证，确保请求安全</text>
    </g>

    <g transform="translate(500, 180)">
      <rect width="320" height="60" fill="rgba(6, 182, 212, 0.1)" stroke="#06b6d4" stroke-width="1" rx="12"/>
      <text x="20" y="25" class="arch-point-title">🚦 智能路由</text>
      <text x="20" y="45" class="arch-point-text">Service层根据Agent类型动态选择最优处理路径</text>
    </g>

    <g transform="translate(850, 180)">
      <rect width="320" height="60" fill="rgba(16, 185, 129, 0.1)" stroke="#10b981" stroke-width="1" rx="12"/>
      <text x="20" y="25" class="arch-point-title">⚡ 实时与长时兼顾</text>
      <text x="20" y="45" class="arch-point-text">一套架构无缝支持快速响应和长时推理两种任务形态</text>
    </g>

    <g transform="translate(1200, 180)">
      <rect width="250" height="60" fill="rgba(245, 158, 11, 0.1)" stroke="#f59e0b" stroke-width="1" rx="12"/>
      <text x="20" y="25" class="arch-point-title">🧩 松耦合通信</text>
      <text x="20" y="45" class="arch-point-text">异步模式通过Redis解耦，提升系统健壮性</text>
    </g>
  </g>

  <!-- Central Flow (Entry & Decision) - 采用现代化样式 -->
  <g id="central-flow">
    <!-- 步骤1：请求入口 -->
    <g transform="translate(600, 340)">
      <rect width="400" height="90" fill="url(#systemGradient)" class="card stage-box" filter="url(#modernShadow)"/>
      <text x="200" y="35" text-anchor="middle" class="stage-title">① 请求入口与预处理</text>
      <text x="200" y="55" text-anchor="middle" class="stage-desc">统一处理JWT认证、参数验证</text>
      <text x="200" y="75" text-anchor="middle" class="stage-impl">POST /api/v1/chat, ChatContext(...)</text>
    </g>

    <!-- 流程箭头 -->
    <path class="flow-arrow" d="M 800 430 V 470"/>

    <!-- 步骤2：策略决策 -->
    <g transform="translate(600, 470)">
      <rect width="400" height="90" fill="url(#stepGradient)" class="card stage-box" filter="url(#modernShadow)"/>
      <text x="200" y="35" text-anchor="middle" class="stage-title">② 处理策略决策</text>
      <text x="200" y="55" text-anchor="middle" class="stage-desc">根据Agent类型选择同步或异步处理器</text>
      <text x="200" y="75" text-anchor="middle" class="stage-impl">is_long_running_agent(agent_type)</text>
    </g>
  </g>

  <!-- Branching Arrows with modern labels -->
  <path class="flow-arrow" d="M 600 515 C 450 515, 450 535, 450 595 V 630"/>
  <g transform="translate(350, 560)">
    <rect width="200" height="30" fill="rgba(59, 130, 246, 0.1)" stroke="#3b82f6" stroke-width="1" rx="15"/>
    <text x="100" y="20" text-anchor="middle" class="flow-text" style="fill:#3b82f6; font-weight: 600;">🚀 快速Agent (如GPT)</text>
  </g>

  <path class="flow-arrow" d="M 1000 515 C 1150 515, 1150 535, 1150 595 V 630"/>
  <g transform="translate(1050, 560)">
    <rect width="240" height="30" fill="rgba(139, 92, 246, 0.1)" stroke="#8b5cf6" stroke-width="1" rx="15"/>
    <text x="120" y="20" text-anchor="middle" class="flow-text" style="fill:#7c3aed; font-weight: 600;">🧠 长时间Agent (ReasoningAgent)</text>
  </g>

  <!-- === PARALLEL FLOWS === -->
  <g id="parallel-flows">
    <!-- SYNC FLOW (LEFT) with modern card design -->
    <g id="sync-flow">
      <!-- 同步流程标题 -->
      <rect x="100" y="630" width="700" height="80" fill="url(#syncGradient)" class="card" filter="url(#modernShadow)"/>
      <text x="450" y="675" text-anchor="middle" class="system-text">🔄 同步处理流程</text>

      <!-- 步骤3-S：初始化 -->
      <g transform="translate(150, 740)">
        <rect width="600" height="90" fill="url(#authGradient)" class="card stage-box" filter="url(#modernShadow)"/>
        <text x="300" y="35" text-anchor="middle" class="stage-title">③-S 初始化</text>
        <text x="300" y="55" text-anchor="middle" class="stage-desc">设置会话并创建Agent实例</text>
        <text x="300" y="75" text-anchor="middle" class="stage-impl">SessionManager.setup() → AgentManager.create()</text>
      </g>
      <path class="flow-arrow" d="M 450 830 V 860"/>

      <!-- 步骤4-S：执行与响应 -->
      <g transform="translate(150, 860)">
        <rect width="600" height="90" fill="url(#chatGradient)" class="card stage-box" filter="url(#modernShadow)"/>
        <text x="300" y="35" text-anchor="middle" class="stage-title">④-S 执行与响应</text>
        <text x="300" y="55" text-anchor="middle" class="stage-desc">直接调用Agent，并将事件流透传给客户端</text>
        <text x="300" y="75" text-anchor="middle" class="stage-impl">agent.astream(...) → yield event</text>
      </g>
      <path class="flow-arrow" d="M 450 950 V 980"/>

      <!-- 步骤5-S：完成与收尾 -->
      <g transform="translate(150, 980)">
        <rect width="600" height="90" fill="url(#responseGradient)" class="card stage-box" filter="url(#modernShadow)"/>
        <text x="300" y="35" text-anchor="middle" class="stage-title">⑤-S 完成与收尾</text>
        <text x="300" y="55" text-anchor="middle" class="stage-desc">持久化最终结果并进行策略性缓存</text>
        <text x="300" y="75" text-anchor="middle" class="stage-impl">persist_ai_response() → cache_with_strategy()</text>
      </g>

      <!-- 代码示例卡片 -->
      <g transform="translate(150, 1100)">
        <rect width="600" height="150" fill="rgba(224, 242, 254, 0.9)" stroke="#0891b2" stroke-width="1" class="card stage-box" filter="url(#softShadow)"/>
        <text x="300" y="25" text-anchor="middle" class="code-title">代码示例: SynchronousRequestProcessor.process</text>
        <g class="code-text" transform="translate(20, 50)">
          <text y="0">async def process(self, context):</text>
          <text y="18" x="20">session_info = await SessionManager.setup_session(...)</text>
          <text y="36" x="20">agent = AgentManager.create_agent(...)</text>
          <text y="54" x="20">async for event in agent.astream(...):</text>
          <text y="72" x="40">await self._cache_event_strategically(...)</text>
          <text y="90" x="40">yield event</text>
          <text y="108" x="20">await SessionManager.persist_ai_response(...)</text>
        </g>
      </g>
    </g>

    <!-- ASYNC FLOW (RIGHT) with modern card design -->
    <g id="async-flow">
      <!-- 异步流程标题 -->
      <rect x="800" y="630" width="700" height="80" fill="url(#asyncGradient)" class="card" filter="url(#modernShadow)"/>
      <text x="1150" y="675" text-anchor="middle" class="system-text">⚡ 异步处理流程</text>

      <!-- 步骤3-A：初始化并启动 -->
      <g transform="translate(850, 740)">
        <rect width="600" height="90" fill="url(#authGradient)" class="card stage-box" filter="url(#modernShadow)"/>
        <text x="300" y="35" text-anchor="middle" class="stage-title">③-A 初始化并启动</text>
        <text x="300" y="55" text-anchor="middle" class="stage-desc">创建AI消息占位符，并启动后台任务</text>
        <text x="300" y="75" text-anchor="middle" class="stage-impl">create_placeholder() → asyncio.create_task()</text>
      </g>
      <path class="flow-arrow" d="M 1150 830 V 860"/>

      <!-- 步骤4-A：解耦通信 -->
      <g transform="translate(850, 860)">
        <rect width="600" height="150" fill="url(#queryGradient)" class="card stage-box" filter="url(#modernShadow)"/>
        <text x="300" y="35" text-anchor="middle" class="stage-title">④-A 解耦通信</text>
        <text x="300" y="55" text-anchor="middle" class="stage-desc">前端轮询Redis，后端任务发布事件到Redis</text>

        <!-- Redis通信箭头 -->
        <path class="data-arrow" d="M 150 110 H 450" />
        <text x="60" y="115" class="stage-impl">yield from _stream_from_redis()</text>
        <text x="460" y="115" class="stage-impl">redis.publish(event)</text>
      </g>
      <path class="flow-arrow" d="M 1150 1010 V 1040"/>

      <!-- 步骤5-A：后台收尾 -->
      <g transform="translate(850, 1040)">
        <rect width="600" height="90" fill="url(#responseGradient)" class="card stage-box" filter="url(#modernShadow)"/>
        <text x="300" y="35" text-anchor="middle" class="stage-title">⑤-A 后台收尾</text>
        <text x="300" y="55" text-anchor="middle" class="stage-desc">后台任务完成后，更新数据库中的最终结果</text>
        <text x="300" y="75" text-anchor="middle" class="stage-impl">update_ai_message_content(final_result)</text>
      </g>

      <!-- 后台任务代码示例 -->
      <g transform="translate(850, 1160)">
        <rect width="600" height="160" fill="rgba(243, 232, 255, 0.9)" stroke="#8b5cf6" stroke-width="1" class="card stage-box" filter="url(#softShadow)"/>
        <text x="300" y="25" text-anchor="middle" class="code-title">代码示例: 后台任务 _run_agent_in_background</text>
        <g class="code-text" transform="translate(20, 50)">
          <text y="0">async def _run_agent_in_background(...):</text>
          <text y="18" x="20">agent = AgentManager.create_agent(...)</text>
          <text y="36" x="20">async for event in agent.astream(...):</text>
          <text y="54" x="40">await self._publish_to_redis(..., event)</text>
          <text y="72" x="20">await SessionManager.update_ai_message(...)</text>
        </g>
      </g>

      <!-- 前端轮询代码示例 -->
      <g transform="translate(850, 1340)">
        <rect width="600" height="160" fill="rgba(224, 242, 254, 0.9)" stroke="#0891b2" stroke-width="1" class="card stage-box" filter="url(#softShadow)"/>
        <text x="300" y="25" text-anchor="middle" class="code-title">代码示例: 前端轮询 _stream_from_redis</text>
        <g class="code-text" transform="translate(20, 50)">
          <text y="0">async def _stream_from_redis(request_id):</text>
          <text y="18" x="20">while not (is_done or is_timeout):</text>
          <text y="36" x="40">event_data = await self._get_from_redis(...)</text>
          <text y="54" x="40">if event_data: yield event</text>
          <text y="72" x="40">await asyncio.sleep(POLL_INTERVAL)</text>
        </g>
      </g>
    </g>
  </g>

  <!-- Redis Icon as communication hub - 现代化设计 -->
  <g transform="translate(1125, 940)">
    <circle cx="25" cy="25" r="30" fill="url(#chatGradient)" filter="url(#modernShadow)"/>
    <circle cx="25" cy="25" r="25" fill="#dc2626"/>
    <text x="25" y="32" text-anchor="middle" style="font-family: 'Inter', sans-serif; font-size: 14px; font-weight: 700; fill: white;">Redis</text>

    <!-- 添加连接线装饰 -->
    <g opacity="0.6">
      <path d="M 5 25 Q 25 15, 45 25" stroke="white" stroke-width="2" fill="none"/>
      <path d="M 5 25 Q 25 35, 45 25" stroke="white" stroke-width="2" fill="none"/>
    </g>
  </g>

</svg>