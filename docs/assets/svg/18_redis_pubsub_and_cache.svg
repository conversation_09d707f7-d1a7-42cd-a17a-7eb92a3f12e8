<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="950" viewBox="0 0 1400 950" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Modern Gradients based on Reference SVG -->
    <linearGradient id="producerGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#8b5cf6;"/><stop offset="100%" style="stop-color:#7c3aed;"/></linearGradient>
    <linearGradient id="consumerGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#3b82f6;"/><stop offset="100%" style="stop-color:#1d4ed8;"/></linearGradient>
    <linearGradient id="infraGradient" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:#10b981;"/><stop offset="100%" style="stop-color:#059669;"/></linearGradient>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="0%" y2="100%"><stop offset="0%" style="stop-color:#f8fafc;"/><stop offset="100%" style="stop-color:#f1f5f9;"/></linearGradient>

    <!-- New Gradient for Core Value Cards -->
    <linearGradient id="cardGradient" x1="0%" y1="0%" x2="100%" y2="100%">
        <stop offset="0%" style="stop-color:#fdfdff;"/>
        <stop offset="100%" style="stop-color:#f7f9ff;"/>
    </linearGradient>

    <!-- Modern Styles based on Reference SVG -->
    <style>
      .title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 32px; font-weight: 700; fill: #1e293b; letter-spacing: -0.025em; }
      .subtitle { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 18px; fill: #64748b; font-weight: 500; }

      /* New Styles for Core Value Section */
      .core-value-title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 24px; font-weight: 700; fill: #1e293b; text-anchor: middle; }
      .card-number { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 48px; font-weight: 700; fill: #eef2ff; text-anchor: end; user-select: none; }
      .card-title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 20px; font-weight: 600; fill: #374151; }
      .card-desc { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 15px; line-height: 1.6; fill: #475569; }
      .card-desc-bold { font-weight: 600; fill: #4f46e5; }
      .card-box { fill: url(#cardGradient); rx: 16; stroke: #e2e8f0; stroke-width: 1; filter: url(#modernShadow); }

      .layer-title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 20px; font-weight: 600; fill: white; }
      .layer-desc { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 14px; fill: white; opacity: 0.9; }
      .component-title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 16px; font-weight: 600; fill: #1e293b; }
      .component-desc { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 13px; fill: #475569; }
      .flow-text { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 13px; fill: #334155; font-weight: 500; }
      .mono { font-family: 'SF Mono', 'Monaco', monospace; font-size: 12px; fill: #334155; }

      .layer-box { rx: 16; filter: url(#modernShadow); }
      .component-box { fill: rgba(255,255,255,0.9); rx: 12; stroke: rgba(255,255,255,0.9); stroke-width: 1.5; filter: url(#softShadow); }
      .flow-arrow { stroke: #64748b; stroke-width: 2.5; fill: none; marker-end: url(#arrowhead); }
    </style>

    <!-- Modern Markers & Filters -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto"><polygon points="0 0, 10 3.5, 0 7" fill="#64748b" /></marker>
    <filter id="modernShadow" x="-10%" y="-10%" width="120%" height="130%"><feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#1e293b" flood-opacity="0.08"/><feDropShadow dx="0" dy="1" stdDeviation="2" flood-color="#1e293b" flood-opacity="0.06"/></filter>
    <filter id="softShadow" x="-10%" y="-10%" width="120%" height="120%"><feDropShadow dx="0" dy="2" stdDeviation="4" flood-color="#1e293b" flood-opacity="0.1"/></filter>
  </defs>

  <!-- === BACKGROUND === -->
  <rect width="100%" height="100%" fill="url(#bgGradient)"/>

  <!-- Header Section -->
  <g id="header">
    <text x="700" y="50" text-anchor="middle" class="title">Redis Pub/Sub 与快照缓存双轨制架构</text>
    <text x="700" y="80" text-anchor="middle" class="subtitle">低延迟实时推送与高可靠数据快照的融合实践</text>
  </g>

  <!-- Architecture Points Section -->
  <g id="architecture-points">
    <text x="700" y="130" text-anchor="middle" class="core-value-title">🚀 核心价值</text>
    <rect x="100" y="150" width="1200" height="120" fill="rgba(255,255,255,0.9)" stroke="#e2e8f0" stroke-width="1" rx="20" filter="url(#softShadow)"/>

    <!-- Architecture Point Cards -->
    <g transform="translate(150, 180)">
      <rect width="280" height="60" fill="rgba(139, 92, 246, 0.1)" stroke="#8b5cf6" stroke-width="1" rx="12"/>
      <text x="20" y="25" class="card-title" style="font-size:14px;">⚡ 低延迟与高可靠兼得</text>
      <text x="20" y="45" class="card-desc" style="font-size:12px;">实时轨道 <tspan style="font-weight:600;fill:#dc2626;">~ms级</tspan> + 回放轨道保障</text>
    </g>

    <g transform="translate(450, 180)">
      <rect width="280" height="60" fill="rgba(16, 185, 129, 0.1)" stroke="#10b981" stroke-width="1" rx="12"/>
      <text x="20" y="25" class="card-title" style="font-size:14px;">🎯 智能降级机制</text>
      <text x="20" y="45" class="card-desc" style="font-size:12px;">优先Pub/Sub，异常时无缝切换轮询</text>
    </g>

    <g transform="translate(750, 180)">
      <rect width="280" height="60" fill="rgba(245, 158, 11, 0.1)" stroke="#f59e0b" stroke-width="1" rx="12"/>
      <text x="20" y="25" class="card-title" style="font-size:14px;">🧩 统一总线封装</text>
      <text x="20" y="45" class="card-desc" style="font-size:12px;">ChatEventBus 简化上层调用接口</text>
    </g>

    <g transform="translate(1050, 180)">
      <rect width="200" height="60" fill="rgba(59, 130, 246, 0.1)" stroke="#3b82f6" stroke-width="1" rx="12"/>
      <text x="20" y="25" class="card-title" style="font-size:14px;">📊 有序保证</text>
      <text x="20" y="45" class="card-desc" style="font-size:12px;">seq序列号+缓冲机制</text>
    </g>
  </g>

  <!-- === MAIN ARCHITECTURE DIAGRAM === -->
  <g id="main-architecture" transform="translate(0, 300)">
    <!-- Producer Module -->
    <g id="producer-layer">
      <rect x="50" y="0" width="400" height="500" fill="url(#producerGradient)" class="layer-box"/>
      <text x="80" y="40" class="layer-title">📤 生产者模块</text>
      <text x="80" y="65" class="layer-desc">AsynchronousRequestProcessor</text>

      <!-- Agent Execution -->
      <g transform="translate(80, 100)">
        <rect width="340" height="80" fill="rgba(255,255,255,0.2)" rx="12"/>
        <text x="170" y="30" text-anchor="middle" class="layer-desc" style="font-size:16px;font-weight:600;">后台Agent执行</text>
        <text x="170" y="50" text-anchor="middle" class="mono" style="fill:#e0f2fe;">_run_agent_in_background()</text>
        <text x="170" y="65" text-anchor="middle" class="layer-desc" style="font-size:12px;">循环产生 BaseAgentOutputEvent</text>
      </g>

      <!-- Arrow down -->
      <path class="flow-arrow" d="M 250 180 V 220" style="stroke:white;"/>

      <!-- Dual Track Publishing -->
      <g transform="translate(80, 220)">
        <rect width="340" height="240" fill="rgba(255,255,255,0.2)" rx="12"/>
        <text x="170" y="25" text-anchor="middle" class="layer-desc" style="font-size:16px;font-weight:600;">双轨发布机制</text>

        <!-- Real-time Track -->
        <g transform="translate(20, 50)">
          <text x="0" y="0" class="layer-desc" style="font-size:14px;font-weight:600;fill:#fde68a;">实时轨道 (Pub/Sub):</text>
          <text x="20" y="25" class="mono" style="fill:#e0f2fe;">event_bus.publish(event)</text>
          <text x="20" y="45" class="layer-desc" style="font-size:12px;">每条事件立即发布，毫秒级延迟</text>
          <text x="20" y="65" class="layer-desc" style="font-size:12px;fill:#fde68a;">优势: 极致实时性</text>
        </g>

        <!-- Snapshot Track -->
        <g transform="translate(20, 130)">
          <text x="0" y="0" class="layer-desc" style="font-size:14px;font-weight:600;fill:#fde68a;">回放轨道 (Snapshot):</text>
          <text x="20" y="25" class="mono" style="fill:#e0f2fe;">event_bus.write_snapshot(event)</text>
          <text x="20" y="45" class="layer-desc" style="font-size:12px;">节流写入 (每10条或5秒)</text>
          <text x="20" y="65" class="layer-desc" style="font-size:12px;fill:#fde68a;">优势: 高可靠性，数据保底</text>
        </g>
      </g>
    </g>

    <!-- Arrow to Redis -->
    <path class="flow-arrow" d="M 450 250 H 500"/>
    <text x="460" y="240" class="flow-text">双轨写入</text>

    <!-- Redis Infrastructure -->
    <g id="infra-layer">
      <rect x="500" y="0" width="400" height="500" fill="url(#infraGradient)" class="layer-box"/>
      <text x="530" y="40" class="layer-title">💾 基础设施: Redis</text>
      <text x="530" y="65" class="layer-desc">双轨制存储核心</text>

      <!-- Pub/Sub Channel -->
      <g transform="translate(530, 100)">
        <rect width="340" height="120" fill="rgba(255,255,255,0.2)" rx="12"/>
        <text x="170" y="25" text-anchor="middle" class="layer-desc" style="font-size:16px;font-weight:600;">Pub/Sub Channel</text>
        <text x="170" y="50" text-anchor="middle" class="layer-desc" style="font-size:12px;">实时消息传递通道</text>
        <text x="170" y="75" text-anchor="middle" class="mono" style="fill:#e0f2fe;">CHAT_EVENT:channel:{req_id}</text>
        <text x="170" y="95" text-anchor="middle" class="layer-desc" style="font-size:11px;fill:#fde68a;">特性: 毫秒级延迟，无持久化</text>
      </g>

      <!-- Snapshot Cache -->
      <g transform="translate(530, 250)">
        <rect width="340" height="120" fill="rgba(255,255,255,0.2)" rx="12"/>
        <text x="170" y="25" text-anchor="middle" class="layer-desc" style="font-size:16px;font-weight:600;">Snapshot Cache Key</text>
        <text x="170" y="50" text-anchor="middle" class="layer-desc" style="font-size:12px;">回放和历史查询存储</text>
        <text x="170" y="75" text-anchor="middle" class="mono" style="fill:#e0f2fe;">CHAT_EVENT:{req_id} (TTL: 6h)</text>
        <text x="170" y="95" text-anchor="middle" class="layer-desc" style="font-size:11px;fill:#fde68a;">特性: 高可靠性，数据持久化</text>
      </g>

      <!-- Redis Icon -->
      <g transform="translate(650, 400)">
        <circle cx="50" cy="50" r="40" fill="#dc2626" filter="url(#modernShadow)"/>
        <text x="50" y="58" text-anchor="middle" style="font-family: 'Inter', sans-serif; font-size: 16px; font-weight: 700; fill: white;">Redis</text>
      </g>
    </g>

    <!-- Arrow to Consumer -->
    <path class="flow-arrow" d="M 900 250 H 950"/>
    <text x="910" y="240" class="flow-text">智能读取</text>

    <!-- Consumer Module -->
    <g id="consumer-layer">
      <rect x="950" y="0" width="400" height="500" fill="url(#consumerGradient)" class="layer-box"/>
      <text x="980" y="40" class="layer-title">📥 消费者模块</text>
      <text x="980" y="65" class="layer-desc">AsynchronousRequestProcessor</text>

      <!-- Smart Degradation Logic -->
      <g transform="translate(980, 100)">
        <rect width="340" height="360" fill="rgba(255,255,255,0.2)" rx="12"/>
        <text x="170" y="25" text-anchor="middle" class="layer-desc" style="font-size:16px;font-weight:600;">智能降级消费逻辑</text>

        <!-- Priority Path: Pub/Sub -->
        <g transform="translate(20, 50)">
          <text x="0" y="0" class="layer-desc" style="font-size:14px;font-weight:600;fill:#fde68a;">1. 优先路径: 订阅Pub/Sub</text>
          <text x="20" y="25" class="mono" style="fill:#e0f2fe;font-size:11px;">stream = event_bus.subscribe()</text>
          <text x="20" y="45" class="mono" style="fill:#e0f2fe;font-size:11px;">_stream_via_pubsub(stream)</text>
          <text x="20" y="70" class="layer-desc" style="font-size:12px;">✓ 毫秒级延迟，实时性极佳</text>
          <text x="20" y="90" class="layer-desc" style="font-size:12px;">✓ 基于seq的乱序缓冲，保证有序</text>
        </g>

        <!-- Degradation Arrow -->
        <path class="flow-arrow" d="M 170 150 V 180" style="stroke:#fde68a;stroke-dasharray:4,4;"/>
        <text x="180" y="170" class="layer-desc" style="font-size:11px;fill:#fde68a;">异常时自动切换</text>

        <!-- Fallback Path: Polling -->
        <g transform="translate(20, 190)">
          <text x="0" y="0" class="layer-desc" style="font-size:14px;font-weight:600;fill:#fde68a;">2. 降级路径: 轮询快照</text>
          <text x="20" y="25" class="mono" style="fill:#e0f2fe;font-size:11px;">_stream_from_redis()</text>
          <text x="20" y="45" class="mono" style="fill:#e0f2fe;font-size:11px;">event_bus.read_snapshot()</text>
          <text x="20" y="70" class="layer-desc" style="font-size:12px;">✓ 可靠性高，保证最终一致</text>
          <text x="20" y="90" class="layer-desc" style="font-size:12px;">✓ 低频轮询，仅在数据变化时产出</text>
        </g>

        <!-- Performance Box -->
        <g transform="translate(20, 290)">
          <rect width="300" height="50" fill="rgba(16, 185, 129, 0.2)" rx="8"/>
          <text x="150" y="20" text-anchor="middle" class="layer-desc" style="font-size:12px;font-weight:600;">性能对比</text>
          <text x="150" y="35" text-anchor="middle" class="layer-desc" style="font-size:11px;">Pub/Sub: ~1-5ms | 轮询: ~100-500ms</text>
        </g>
      </g>
    </g>
  </g>
</svg>