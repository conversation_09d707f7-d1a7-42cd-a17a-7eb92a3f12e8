<svg width="1600" height="1300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Modern Gradients -->
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;"/>
      <stop offset="30%" style="stop-color:#f8fafc;"/>
      <stop offset="70%" style="stop-color:#f1f5f9;"/>
      <stop offset="100%" style="stop-color:#e2e8f0;"/>
    </linearGradient>
    <linearGradient id="fastPathGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;"/>
      <stop offset="100%" style="stop-color:#1d4ed8;"/>
    </linearGradient>
    <linearGradient id="slowPathGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10b981;"/>
      <stop offset="100%" style="stop-color:#059669;"/>
    </linearGradient>
    <linearGradient id="agentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;"/>
      <stop offset="100%" style="stop-color:#7c3aed;"/>
    </linearGradient>
    <linearGradient id="conclusionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f97316;"/>
      <stop offset="100%" style="stop-color:#ea580c;"/>
    </linearGradient>
    
    <!-- Enhanced Filters -->
    <filter id="dropShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="5" stdDeviation="10" flood-color="#1e293b" flood-opacity="0.1"/>
    </filter>
    <filter id="subtleShadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#1e293b" flood-opacity="0.06"/>
    </filter>
    <filter id="glowEffect" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge>
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>

    <!-- Arrow Markers -->
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#475569" />
    </marker>
    <marker id="arrowhead-data" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
      <polygon points="0 0, 8 3, 0 6" fill="#059669" />
    </marker>

    <!-- Enhanced Styles -->
    <style>
      .title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 32px; font-weight: 700; fill: #1e293b; text-anchor: middle; }
      .subtitle { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 18px; fill: #64748b; font-weight: 500; text-anchor: middle; letter-spacing: 0.5px; }
      
      .group-box { rx: 16; filter: url(#dropShadow); }
      .group-label { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 22px; font-weight: 600; fill: white; text-anchor: middle; }
      
      .node-rect { fill: white; stroke: #94a3b8; stroke-width: 1.5; rx: 10; filter: url(#subtleShadow); }
      .llm-node { fill: rgba(255,255,255,0.95); stroke: #f97316; stroke-width: 2; rx: 10; filter: url(#subtleShadow); }
      .decision-diamond { fill: rgba(255,255,255,0.95); stroke: #64748b; stroke-width: 2; filter: url(#subtleShadow); }
      .start-end-oval { fill: #f0fdf4; stroke: #22c55e; stroke-width: 2; filter: url(#subtleShadow); }
      .data-store { fill: rgba(255,255,255,0.3); stroke: rgba(255,255,255,0.8); stroke-width: 2; }

      .node-text { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 16px; font-weight: 600; fill: #1e293b; text-anchor: middle; dominant-baseline: central; }
      .small-text { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 12px; fill: #475569; text-anchor: middle; dominant-baseline: central; }
      .line-label { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 12px; fill: #475569; text-anchor: middle; }
      .white-text { fill: white; }

      .connector { fill: none; stroke: #475569; stroke-width: 2.5; marker-end: url(#arrowhead); }
      .data-connector { fill: none; stroke: #059669; stroke-width: 2.5; marker-end: url(#arrowhead-data); stroke-dasharray: 6,4; }
      
      /* 技术要点样式 */
      .tech-highlight-box { fill: white; rx: 16; stroke: #e2e8f0; stroke-width: 1; filter: url(#subtleShadow); }
      .tech-title { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 20px; font-weight: 600; fill: #1e293b; }
      .tech-subtitle { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 15px; font-weight: 600; fill: #f97316; }
      .tech-desc { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 13px; fill: #475569; }
      .tech-desc-bold { font-weight: 600; fill: #334155; }
      
      /* 标记圆圈样式 */
      .marker-circle { font-family: 'Inter', 'Segoe UI', sans-serif; font-size: 14px; font-weight: 700; stroke: rgba(255,255,255,0.7); stroke-width: 2px; }
    </style>
  </defs>

  <!-- Background -->
  <rect width="100%" height="100%" fill="url(#bgGradient)"/>

  <!-- Title -->
  <text x="800" y="60" class="title">双路径混合智能体架构</text>
  <text x="800" y="95" class="subtitle">效率与动态能力的完美平衡</text>

  <!-- 技术要点区域 -->
  <g id="tech-highlights">
    <rect x="50" y="130" width="1500" height="140" class="tech-highlight-box"/>
    <g transform="translate(70, 160)">
        <text x="20" y="20" class="tech-title">核心技术亮点</text>
        <g transform="translate(0, 40)">
            <text x="45" y="0" class="tech-subtitle">1. 自适应路径选择</text>
            <text x="45" y="18" class="tech-desc"><tspan class="tech-desc-bold">智能场景匹配：</tspan><tspan>基于查询复杂度自动选择快速或深度路径</tspan></text>
        </g>
        <g transform="translate(500, 40)">
            <text x="45" y="0" class="tech-subtitle">2. 并发执行与动态合并</text>
            <text x="45" y="18" class="tech-desc"><tspan class="tech-desc-bold">并发机制：</tspan><tspan>双路径同时启动，在不确定性下保证最优响应</tspan></text>
        </g>
        <g transform="translate(1000, 40)">
            <text x="45" y="0" class="tech-subtitle">3. 分层LLM选择机制</text>
            <text x="45" y="18" class="tech-desc"><tspan class="tech-desc-bold">智能模型选择：</tspan><tspan>根据任务复杂度动态选择 L1-L3 模型</tspan></text>
        </g>
    </g>
  </g>

  <!-- Central Data: ExecutionState -->
  <g transform="translate(675, 300)">
      <rect width="250" height="70" class="data-store" rx="35"/>
      <text x="125" y="35" class="node-text white-text">ExecutionState</text>
  </g>

  <!-- Start & Agent Entry -->
  <g transform="translate(725, 400)">
    <rect x="0" y="0" width="150" height="50" rx="25" class="start-end-oval"/>
    <text x="75" y="25" class="node-text">用户查询</text>
  </g>
  <path d="M 800 450 C 800 460, 800 470, 800 480" class="connector"/>
  
  <!-- Interactive Agent Box -->
  <g transform="translate(675, 480)">
    <rect x="0" y="0" width="250" height="80" fill="url(#agentGradient)" class="group-box"/>
    <text x="125" y="30" class="node-text white-text">🤖 交互智能体</text>
    <text x="125" y="55" class="small-text white-text">创建执行状态</text>
  </g>

  <!-- Path Split -->
  <path d="M 800 560 V 590" class="connector"/>
  <path d="M 325 590 L 1275 590" style="stroke:#94a3b8; stroke-width:2; stroke-dasharray:5,5;"/>
  <path d="M 325 590 V 620" class="connector"/>
  <path d="M 1275 590 V 620" class="connector"/>
  <text x="800" y="585" class="line-label">并发启动两个路径</text>

  <!-- Fast Path Lane -->
  <g id="fast-path">
    <rect x="50" y="620" width="550" height="480" fill="url(#fastPathGradient)" class="group-box"/>
    <text x="325" y="655" class="group-label">⚡ 快速路径：场景匹配</text>
    <g transform="translate(235, 690)">
      <rect x="0" y="0" width="180" height="70" class="llm-node"/>
      <text x="90" y="25" class="node-text">场景匹配器</text>
      <text x="90" y="50" class="small-text">(LLM 调用)</text>
    </g>
    <path d="M 325 760 V 800" class="connector"/>
    <g transform="translate(325, 840)">
      <path d="M 0 -35 L 70 0 L 0 35 L -70 0 Z" class="decision-diamond"/>
      <text x="0" y="0" class="node-text">匹配成功?</text>
    </g>
    <path d="M 255 840 C 150 840, 150 910, 150 1020 C 150 1130, 650 1130, 750 1130" class="connector"/>
    <text x="200" y="830" class="line-label white-text">否 (退出)</text>
    <path d="M 395 840 H 450 V 910" class="connector"/>
    <text x="420" y="860" class="line-label white-text">是</text>
    <g transform="translate(360, 910)">
        <rect x="0" y="0" width="180" height="70" class="node-rect"/>
        <text x="90" y="25" class="node-text">加载 JSON 计划</text>
        <text x="90" y="50" class="small-text">(预定义 DAG)</text>
    </g>
    <path d="M 450 980 V 1020" class="connector"/>
    <g transform="translate(360, 1020)">
        <rect x="0" y="0" width="180" height="70" class="node-rect"/>
        <text x="90" y="25" class="node-text">任务调度器</text>
        <text x="90" y="50" class="small-text">(执行工具)</text>
    </g>
    <path d="M 540 1090 C 600 1110, 650 1120, 750 1130" class="connector"/>
  </g>

  <!-- Slow Path Lane -->
  <g id="slow-path">
    <rect x="1000" y="620" width="550" height="480" fill="url(#slowPathGradient)" class="group-box"/>
    <text x="1275" y="655" class="group-label">🕒 深度路径：智能推理</text>
    <g transform="translate(1185, 690)">
      <rect x="0" y="0" width="180" height="70" class="llm-node"/>
      <text x="90" y="25" class="node-text">规划器</text>
      <text x="90" y="50" class="small-text">(LLM 流式规划)</text>
    </g>
    <path d="M 1275 760 V 800" class="connector"/>
    <g transform="translate(1185, 800)">
        <rect x="0" y="0" width="180" height="70" class="node-rect"/>
        <text x="90" y="25" class="node-text">任务调度器</text>
        <text x="90" y="50" class="small-text">(接收流式计划)</text>
    </g>
    <path d="M 1275 870 V 910" class="connector"/>
    <g transform="translate(1185, 910)">
        <rect x="0" y="0" width="180" height="130" class="node-rect"/>
        <text x="90" y="20" class="node-text" font-weight="bold">步骤执行器</text>
        <g transform="translate(10, 45)">
          <rect x="0" y="0" width="160" height="50" class="llm-node"/>
          <text x="80" y="25" class="node-text">参数提取器</text>
        </g>
        <path d="M 90 95 V 110" class="connector"/>
        <text x="90" y="120" class="small-text">工具调用</text>
    </g>
    <path d="M 1185 1040 C 1100 1080, 950 1110, 850 1130" class="connector"/>
  </g>

  <!-- Merge and Conclude -->
  <g id="conclusion" transform="translate(0, 90)">
    <rect x="650" y="1050" width="300" height="150" fill="url(#conclusionGradient)" class="group-box"/>
    <text x="800" y="1080" class="group-label">🎤 结论生成</text>
    
    <g transform="translate(800, 1030)">
      <path d="M 0 -25 L 50 0 L 0 25 L -50 0 Z" class="decision-diamond"/>
      <text x="0" y="0" class="node-text" style="font-size:12px;">快速路径成功?</text>
    </g>
    <text x="720" y="1075" class="line-label white-text">快速路径</text>
    <text x="880" y="1075" class="line-label white-text">深度路径</text>

    <path d="M 800 1055 V 1110" class="connector"/>
    <g transform="translate(710, 1110)">
      <rect x="0" y="0" width="180" height="50" class="llm-node"/>
      <text x="90" y="25" class="node-text">结论生成器 (L1-L3)</text>
    </g>
  </g>

  <!-- 标记圆圈 -->
  <g id="markers" class="marker-circle">
    <g transform="translate(70, 190)">
        <circle cx="20" cy="-5" r="12" fill="#f97316"/>
        <text x="20" y="0" text-anchor="middle" fill="white">1</text>
    </g>
    <g transform="translate(570, 190)">
        <circle cx="20" cy="-5" r="12" fill="#f97316"/>
        <text x="20" y="0" text-anchor="middle" fill="white">2</text>
    </g>
    <g transform="translate(1070, 190)">
        <circle cx="20" cy="-5" r="12" fill="#f97316"/>
        <text x="20" y="0" text-anchor="middle" fill="white">3</text>
    </g>
    
    <g transform="translate(325, 725)">
        <circle cx="0" cy="0" r="12" fill="#f97316" filter="url(#glowEffect)"/>
        <text x="0" y="5" text-anchor="middle" fill="white">1</text>
    </g>
    <g transform="translate(800, 530)">
        <circle cx="0" cy="0" r="12" fill="#f97316" filter="url(#glowEffect)"/>
        <text x="0" y="5" text-anchor="middle" fill="white">2</text>
    </g>
    <g transform="translate(800, 1200)">
        <circle cx="0" cy="0" r="12" fill="#f97316" filter="url(#glowEffect)"/>
        <text x="0" y="5" text-anchor="middle" fill="white">3</text>
    </g>
  </g>
</svg>