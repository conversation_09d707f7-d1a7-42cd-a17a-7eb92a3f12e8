<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <linearGradient id="layer1Gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#dbeafe;" />
            <stop offset="100%" style="stop-color:#bfdbfe;" />
        </linearGradient>
        <linearGradient id="layer2Gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#d1fae5;" />
            <stop offset="100%" style="stop-color:#a7f3d0;" />
        </linearGradient>
        <linearGradient id="layer3Gradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#e0e7ff;" />
            <stop offset="100%" style="stop-color:#c7d2fe;" />
        </linearGradient>
        <filter id="modernShadow" x="-50%" y="-50%" width="200%" height="200%">
            <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="#000000" flood-opacity="0.08"/>
        </filter>
        <marker id="arrowhead" viewBox="0 0 10 10" refX="8" refY="5" markerWidth="8" markerHeight="8" orient="auto-start-reverse">
            <path d="M 0 0 L 10 5 L 0 10 z" fill="#475569" />
        </marker>
        <pattern id="modernGrid" width="30" height="30" patternUnits="userSpaceOnUse">
            <path d="M 30 0 L 0 0 0 30" fill="none" stroke="#e5e7eb" stroke-width="0.5" opacity="0.4"/>
        </pattern>
    </defs>

    <rect width="1400" height="1000" fill="#f8fafc"/>
    <rect width="1400" height="1000" fill="url(#modernGrid)"/>

    <rect x="50" y="20" width="1300" height="60" rx="16" fill="white" filter="url(#modernShadow)"/>
    <text x="700" y="45" text-anchor="middle" font-family="Inter, sans-serif" font-size="24" font-weight="700" fill="#111827">事件驱动异步处理架构</text>
    <text x="700" y="65" text-anchor="middle" font-family="Inter, sans-serif" font-size="12" font-weight="400" fill="#6b7280">IO与CPU分离的高性能事件处理流水线</text>

    <!-- Key Highlights -->
    <g filter="url(#modernShadow)">
        <rect x="50" y="95" width="1300" height="100" rx="16" fill="#ffffff"/>
        <text x="700" y="120" text-anchor="middle" font-family="Inter, sans-serif" font-size="16" font-weight="600" fill="#334155">核心架构亮点</text>
        <g transform="translate(100, 140)">
            <path d="M12 2.25c.963 0 1.89.183 2.75.512.928.348 1.768.83 2.494 1.456s1.108 1.566 1.456 2.494c.33.86.512 1.787.512 2.75s-.183 1.89-.512 2.75a6.166 6.166 0 01-1.456 2.494 6.166 6.166 0 01-2.494 1.456c-.86.33-1.787.512-2.75.512s-1.89-.183-2.75-.512a6.166 6.166 0 01-2.494-1.456 6.166 6.166 0 01-1.456-2.494c-.33-.86-.512-1.787-.512-2.75s.183-1.89.512-2.75a6.166 6.166 0 011.456-2.494A6.166 6.166 0 019.25 2.762c.86-.33 1.787-.512 2.75-.512zM12 1.5a7.5 7.5 0 100 15 7.5 7.5 0 000-15z M12 5.25a.75.75 0 01.75.75v3a.75.75 0 01-1.5 0v-3a.75.75 0 01.75-.75z M11.25 12a.75.75 0 01.75-.75h.008a.75.75 0 01.75.75v.008a.75.75 0 01-.75.75h-.008a.75.75 0 01-.75-.75v-.008z" fill="#3b82f6" transform="scale(1.5)"/>
            <text x="30" y="10" font-family="Inter, sans-serif" font-size="14" font-weight="600" fill="#1e3a8a">IO/CPU 分离 (毫秒级响应)</text>
            <text x="30" y="30" font-family="Inter, sans-serif" font-size="12" fill="#475569">Fire-and-Forget 模式，确保主流程绝对流畅</text>
        </g>
        <g transform="translate(500, 140)">
            <path d="M15.929 2.929a.75.75 0 01.958 1.144l-4.5 3.75a.75.75 0 01-.958 0l-4.5-3.75a.75.75 0 11.958-1.144L12 5.662l3.929-2.733z M15.929 7.429a.75.75 0 01.958 1.144l-4.5 3.75a.75.75 0 01-.958 0l-4.5-3.75a.75.75 0 11.958-1.144L12 10.162l3.929-2.733z" fill="#10b981" transform="scale(1.5)"/>
            <text x="30" y="10" font-family="Inter, sans-serif" font-size="14" font-weight="600" fill="#065f46">事件驱动模型 (异步解耦)</text>
            <text x="30" y="30" font-family="Inter, sans-serif" font-size="12" fill="#475569">`asyncio.Queue` + `_processor_loop` 消费模型</text>
        </g>
        <g transform="translate(900, 140)">
            <path d="M8.25 3.75a.75.75 0 01.75.75v.5c0 .414.336.75.75.75h1a.75.75 0 01.75.75v1a.75.75 0 01-.75.75h-.5a.75.75 0 01-.75-.75V8.25a.75.75 0 00-.75-.75H6.75a.75.75 0 01-.75-.75v-1c0-.414.336-.75.75-.75h.5a.75.75 0 01.75.75v.5z M6.75 12.75a.75.75 0 01.75-.75h.5a.75.75 0 01.75.75v.5c0 .414.336.75.75.75h1a.75.75 0 01.75.75v1a.75.75 0 01-.75.75h-.5a.75.75 0 01-.75-.75v-.5a.75.75 0 00-.75-.75H6.75a.75.75 0 01-.75-.75v-1z M12.75 6.75a.75.75 0 01-.75.75h-.5a.75.75 0 01-.75-.75v-.5c0-.414-.336-.75-.75-.75h-1a.75.75 0 01-.75-.75v-1a.75.75 0 01.75-.75h.5a.75.75 0 01.75.75v.5c0 .414.336.75.75.75h1a.75.75 0 01.75.75v1z M15.75 12.75a.75.75 0 01-.75.75h-.5a.75.75 0 01-.75-.75v-.5a.75.75 0 00-.75-.75h-1a.75.75 0 01-.75-.75v-1c0-.414.336-.75.75-.75h.5a.75.75 0 01.75.75v.5c0 .414.336.75.75.75h1a.75.75 0 01.75.75v1z" fill="#4f46e5" transform="scale(1.5)"/>
            <text x="30" y="10" font-family="Inter, sans-serif" font-size="14" font-weight="600" fill="#3730a3">插件化处理器 (策略模式)</text>
            <text x="30" y="30" font-family="Inter, sans-serif" font-size="12" fill="#475569">`Processor` 子类化，轻松扩展业务能力</text>
        </g>
    </g>

    <!-- Layers -->
    <g transform="translate(0, 150)">
        <rect x="50" y="100" width="420" height="750" rx="12" fill="url(#layer1Gradient)"/>
        <text x="260" y="130" text-anchor="middle" font-family="Inter, sans-serif" font-size="18" font-weight="600" fill="#1e3a8a">事件生产者 &amp; 输出层</text>
        <text x="260" y="150" text-anchor="middle" font-family="Inter, sans-serif" font-size="12" font-weight="400" fill="#1e40af">ReasoningAgent</text>

        <rect x="490" y="100" width="420" height="750" rx="12" fill="url(#layer2Gradient)"/>
        <text x="700" y="130" text-anchor="middle" font-family="Inter, sans-serif" font-size="18" font-weight="600" fill="#065f46">事件协调与驱动层</text>
        <text x="700" y="150" text-anchor="middle" font-family="Inter, sans-serif" font-size="12" font-weight="400" fill="#047857">ReasoningAgentEvent</text>

        <rect x="930" y="100" width="420" height="750" rx="12" fill="url(#layer3Gradient)"/>
        <text x="1140" y="130" text-anchor="middle" font-family="Inter, sans-serif" font-size="18" font-weight="600" fill="#3730a3">插件化业务处理层</text>
        <text x="1140" y="150" text-anchor="middle" font-family="Inter, sans-serif" font-size="12" font-weight="400" fill="#4338ca">BusinessMessageProcessor</text>

        <!-- Components -->
        <!-- Layer 1 -->
        <g filter="url(#modernShadow)">
            <rect x="80" y="180" width="360" height="100" rx="8" fill="white"/>
            <text x="260" y="210" text-anchor="middle" font-family="Inter, sans-serif" font-size="14" font-weight="600" fill="#334155">astream (Async Generator)</text>
            <text x="260" y="235" text-anchor="middle" font-family="Inter, sans-serif" font-size="12" fill="#475569">接收 LangGraph 事件</text>
            <text x="260" y="255" text-anchor="middle" font-family="Inter, sans-serif" font-size="12" fill="#475569">`async for event in ...`</text>
        </g>
        <g filter="url(#modernShadow)">
            <rect x="80" y="450" width="360" height="100" rx="8" fill="white"/>
            <text x="260" y="480" text-anchor="middle" font-family="Inter, sans-serif" font-size="14" font-weight="600" fill="#334155">等待状态变更</text>
            <text x="260" y="505" text-anchor="middle" font-family="Inter, sans-serif" font-size="12" fill="#475569">`await _version_changed.wait()`</text>
        </g>
        <g filter="url(#modernShadow)">
            <rect x="80" y="700" width="360" height="100" rx="8" fill="#fff1f2"/>
            <text x="260" y="730" text-anchor="middle" font-family="Inter, sans-serif" font-size="14" font-weight="600" fill="#be123c">生命周期管理 (finally)</text>
            <text x="260" y="755" text-anchor="middle" font-family="Inter, sans-serif" font-size="12" fill="#c2410c">`reasoning_event.stop()`</text>
        </g>

        <!-- Layer 2 -->
        <g filter="url(#modernShadow)">
            <rect x="520" y="180" width="360" height="80" rx="8" fill="white"/>
            <text x="700" y="210" text-anchor="middle" font-family="Inter, sans-serif" font-size="14" font-weight="600" fill="#334155">`asyncio.Queue`</text>
            <text x="700" y="235" text-anchor="middle" font-family="Inter, sans-serif" font-size="12" fill="#475569">事件的异步缓冲通道</text>
        </g>
        <g filter="url(#modernShadow)">
            <rect x="520" y="300" width="360" height="350" rx="8" fill="white"/>
            <text x="700" y="330" text-anchor="middle" font-family="Inter, sans-serif" font-size="14" font-weight="600" fill="#334155">`_processor_loop` (后台消费者)</text>
            <text x="700" y="355" text-anchor="middle" font-family="Inter, sans-serif" font-size="12" fill="#475569">`while not _stop_event.is_set()`</text>
            <rect x="540" y="380" width="320" height="250" rx="4" fill="#f1f5f9"/>
            <text x="700" y="405" text-anchor="middle" font-family="Inter, sans-serif" font-size="12" font-weight="600" fill="#1e293b">三阶段处理流水线</text>
            <text x="700" y="440" text-anchor="middle" font-family="Inter, sans-serif" font-size="12">2a. `MessageCollector`</text>
            <path d="M 700 455 L 700 470" stroke="#94a3b8" stroke-width="1.5" marker-end="url(#arrowhead)"/>
            <text x="700" y="485" text-anchor="middle" font-family="Inter, sans-serif" font-size="12">2b. `MessageRepository`</text>
            <path d="M 700 500 L 700 515" stroke="#94a3b8" stroke-width="1.5" marker-end="url(#arrowhead)"/>
            <text x="700" y="530" text-anchor="middle" font-family="Inter, sans-serif" font-size="12">2c. `BusinessMessageProcessor`</text>
        </g>
        <g filter="url(#modernShadow)">
            <rect x="520" y="700" width="360" height="100" rx="8" fill="#fff1f2"/>
            <text x="700" y="730" text-anchor="middle" font-family="Inter, sans-serif" font-size="14" font-weight="600" fill="#be123c">优雅退出 `stop()`</text>
            <text x="700" y="755" text-anchor="middle" font-family="Inter, sans-serif" font-size="12" fill="#c2410c">`_stop_event.set()`</text>
            <text x="700" y="775" text-anchor="middle" font-family="Inter, sans-serif" font-size="12" fill="#c2410c">`_queue.put(None)`</text>
        </g>

        <!-- Layer 3 -->
        <g filter="url(#modernShadow)">
            <rect x="960" y="300" width="360" height="350" rx="8" fill="white"/>
            <text x="1140" y="330" text-anchor="middle" font-family="Inter, sans-serif" font-size="14" font-weight="600" fill="#334155">`process_business_messages`</text>
            <rect x="980" y="380" width="320" height="250" rx="4" fill="#f1f5f9"/>
            <text x="1140" y="405" text-anchor="middle" font-family="Inter, sans-serif" font-size="12" font-weight="600" fill="#1e293b">策略模式: 按 `agent_name` 分发</text>
            <text x="1140" y="440" text-anchor="middle" font-family="Inter, sans-serif" font-size="12">`PlannerMessageProcessor`</text>
            <text x="1140" y="470" text-anchor="middle" font-family="Inter, sans-serif" font-size="12">`ResearcherMessageProcessor`</text>
            <text x="1140" y="500" text-anchor="middle" font-family="Inter, sans-serif" font-size="12">`ReporterMessageProcessor`</text>
            <text x="1140" y="530" text-anchor="middle" font-family="Inter, sans-serif" font-size="12">`HtmlReportFinishMessageProcessor`</text>
            <text x="1140" y="560" text-anchor="middle" font-family="Inter, sans-serif" font-size="12" font-style="italic" fill="#4338ca">...可轻松扩展...</text>
        </g>

        <!-- Arrows and Highlights -->
        <path d="M 440 230 C 470 230, 470 220, 520 220" stroke="#3b82f6" stroke-width="2.5" fill="none" marker-end="url(#arrowhead)"/>
        <text x="475" y="205" text-anchor="middle" font-family="Inter, sans-serif" font-size="12" font-weight="600" fill="#1e40af">1. `handle_message()`</text>
        <g transform="translate(410, 160)">
            <rect x="0" y="0" width="130" height="25" rx="12.5" fill="#e0f2fe"/>
            <text x="65" y="17" text-anchor="middle" font-family="Inter, sans-serif" font-size="11" font-weight="500" fill="#0369a1">Fire-and-Forget</text>
        </g>

        <path d="M 700 260 L 700 300" stroke="#10b981" stroke-width="2.5" fill="none" marker-end="url(#arrowhead)"/>
        <text x="730" y="285" text-anchor="start" font-family="Inter, sans-serif" font-size="12" font-weight="600" fill="#047857">2. `await _queue.get()`</text>

        <path d="M 880 475 L 930 475" stroke="#4f46e5" stroke-width="2.5" fill="none" marker-end="url(#arrowhead)"/>
        <text x="880" y="460" text-anchor="end" font-family="Inter, sans-serif" font-size="12" font-weight="600" fill="#4338ca">2c. process</text>

        <path d="M 930 500 C 900 500, 900 600, 880 600" stroke="#64748b" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5"/>
        <text x="930" y="550" text-anchor="start" font-family="Inter, sans-serif" font-size="12" font-weight="500" fill="#475569">返回 event_data</text>

        <path d="M 700 650 L 700 680" stroke="#10b981" stroke-width="2" fill="none" marker-end="url(#arrowhead)" stroke-dasharray="5,5"/>
        <text x="700" y="670" text-anchor="middle" font-family="Inter, sans-serif" font-size="12" font-weight="500" fill="#047857">3. `_apply_event_data`</text>

        <path d="M 520 500 C 470 500, 470 500, 440 500" stroke="#fb923c" stroke-width="2.5" fill="none" marker-end="url(#arrowhead)"/>
        <text x="475" y="485" text-anchor="middle" font-family="Inter, sans-serif" font-size="12" font-weight="600" fill="#c2410c">4. `_version_changed.set()`</text>
        <g transform="translate(410, 430)">
            <rect x="0" y="0" width="130" height="25" rx="12.5" fill="#ffedd5"/>
            <text x="65" y="17" text-anchor="middle" font-family="Inter, sans-serif" font-size="11" font-weight="500" fill="#9a3412">Event-Driven Signal</text>
        </g>

        <path d="M 260 550 L 260 600 C 260 650, 1000 650, 1000 600" stroke="#0ea5e9" stroke-width="2.5" fill="none" marker-end="url(#arrowhead)"/>
        <text x="630" y="620" text-anchor="middle" font-family="Inter, sans-serif" font-size="12" font-weight="600" fill="#0369a1">5. `yield` 推送完整状态</text>

        <path d="M 440 750 L 520 750" stroke="#dc2626" stroke-width="2.5" fill="none" marker-end="url(#arrowhead)"/>
        <text x="480" y="735" text-anchor="middle" font-family="Inter, sans-serif" font-size="12" font-weight="600" fill="#991b1b">6. 触发优雅退出</text>
    </g>
</svg>