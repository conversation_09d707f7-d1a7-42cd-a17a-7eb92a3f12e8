# Makefile for ecs-deep-diagnose project

.PHONY: help install dev-install lint format check test clean format-check

# Default target
help:
	@echo "Available targets:"
	@echo "  install       - Install production dependencies"
	@echo "  dev-install   - Install development dependencies"
	@echo "  lint          - Run linting checks (flake8, mypy) - 可选"
	@echo "  format        - 手动格式化代码 (black + isort)"
	@echo "  format-check  - 检查代码格式但不修改"
	@echo "  test          - Run tests"
	@echo "  clean         - Clean up temporary files"

# Install dependencies
install:
	uv sync --no-dev

dev-install:
	uv sync
	uv add --dev black isort flake8 mypy pre-commit

# 可选的代码质量检查
lint:
	@echo "🔍 可选: Running flake8..."
	@uv run flake8 src tests || echo "⚠️  Flake8 发现一些问题，可选择修复"
	@echo "🔍 可选: Running mypy..."
	@uv run mypy src || echo "⚠️  MyPy 发现一些问题，可选择修复"
	@echo "✅ 代码质量检查完成 (仅供参考)!"

# 手动格式化代码
format:
	@echo "🎨 手动格式化代码..."
	@echo "⚠️  这将修改你的代码格式，确认继续吗? [y/N]" && read ans && [ $${ans:-N} = y ]
	@echo "🎨 Formatting with black..."
	uv run black src tests
	@echo "🎨 Sorting imports with isort..."
	uv run isort src tests
	@echo "✅ 代码格式化完成!"

# 检查格式但不修改
format-check:
	@echo "🔍 检查代码格式 (不修改)..."
	@uv run black --check --diff src tests || echo "💡 运行 'make format' 可自动修复格式"
	@uv run isort --check-only --diff src tests || echo "💡 运行 'make format' 可自动修复 import 排序"
	@echo "✅ 格式检查完成!"

# Run tests
test:
	uv run pytest

# Clean up
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type f -name ".coverage" -delete
	rm -rf .pytest_cache/