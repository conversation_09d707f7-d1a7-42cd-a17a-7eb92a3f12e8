# ECS 深度诊断

<p align="center">
  <img src="./assets/architecture.png" alt="系统架构图" width="800"/>
</p>

<p align="center">
  <em>一个基于多智能体（Multi-Agent）的云服务器（ECS）深度问题诊断与分析引擎。</em>
</p>

---

## 核心特性

- **🤖 多智能体协同**：基于 `LangGraph` 构建，实现任务分解、执行与协作。
- **🛠️ 强大工具集成**：无缝集成MCP服务、代码执行等工具。
- **🤝 人机协作**：支持通过自然语言审查、修改诊断计划与报告。

## 系统架构

<p align="center">
  <img src="./assets/diagnose_flow.png" alt="诊断流程图" width="800"/>
</p>

## 📁 项目结构

```
ecs-deep-diagnose/
├── src/                    # 源代码
│   ├── deep_diagnose/      # 主应用包
│   │   ├── core/           # 核心业务逻辑
│   │   │   ├── agents/     # 智能体模块
│   │   │   ├── workflow/   # 工作流引擎
│   │   │   └── planning/   # 规划模块
│   │   ├── common/         # 共享组件
│   │   │   ├── config/     # 配置管理
│   │   │   ├── constants/  # 常量定义
│   │   │   ├── types/      # 类型定义
│   │   │   ├── utils/      # 工具函数
│   │   │   └── exceptions/ # 异常定义
│   │   ├── api/            # API接口层
│   │   ├── services/       # 服务层
│   │   ├── tools/          # 工具模块
│   │   ├── llms/           # LLM模块
│   │   ├── security/       # 安全模块
│   │   ├── storage/        # 存储模块
│   │   └── prompts/        # 提示模板
│   ├── server.py           # 服务器入口
│   └── logging_config.py   # 日志配置
├── tests/                  # 测试代码
│   ├── unit/               # 单元测试
│   └── integration/        # 集成测试
├── scripts/                # 脚本文件
├── pyproject.toml          # 项目配置
├── conf/                   # 配置文件
└── docs/                   # 文档
```

## 🚀 快速开始

### 环境要求
- Python 3.12+, Node.js 20+
- 推荐使用 pipx进行 安装 `uv` 和 `pnpm`
- 安装pipx
   ```bash
   brew install pipx
   pipx ensurepath
   ```
- 安装uv
  ```bash
   pipx install uv
   ```

### 安装与启动

1. **克隆仓库**
   ```bash
   # https://code.alibaba-inc.com/cloud-ecs-devops/ecs-deep-diagnose
   <NAME_EMAIL>:cloud-ecs-devops/ecs-deep-diagnose.git
   cd ecs-deep-diagnose
   ```

2. **安装依赖**
   ```bash
   # 安装依赖
   uv sync
   ```

3. **配置环境**
   - 点击PyCharm右下角添加 ./.venv 为 本地解释器
       <p align="center">
          <img src="./assets/add_interpreter.png" alt="添加本地解释器" width="800"/>
        </p>
     
4. **启动应用**
   ```bash
   # 启动后端API服务
   cd src && uv run server.py
   
   # 启动Celery服务 (Worker + Flower + Beat)
   source .venv/bin/activate
   ./celery.sh
   ```
   后端服务启动后，访问 `http://localhost:8000`。
   API 文档可在 `http://localhost:8000/docs` 查看。
   Celery Flower 监控界面可在 `http://localhost:5555` 查看。

5. **设置根目录**
   - 你可以将 目录设置为如下图所示，这样你可以使用相对路径来引用其他文件 
   - 右击文件夹 -> 将目录标记为 -> 源代码根目录
   - tests需要先标记为 “源代码根目录” 然后再标记为 “测试源代码根目录”
        <p align="center">
        <img src="./assets/root_directory_diagram.png" alt="目录示意图" width="800"/>
        </p>

## 🧪 运行测试

```bash
# 运行所有测试
uv run pytest

# 运行单元测试
uv run pytest tests/unit/

# 运行集成测试
uv run pytest tests/integration/

# 运行特定测试文件
uv run pytest tests/integration/api/test_create_task.py
```

## 🔧 开发指南

### 核心模块说明

- **core/agents/**: 智能体实现，包含诊断、规划、研究等智能体
- **core/workflow/**: 基于 LangGraph 的工作流引擎
- **core/planning/**: SOP 管理和规划逻辑
- **common/config/**: 统一配置管理系统
- **api/**: FastAPI 路由和中间件
- **services/**: 业务服务层，包含报告生成、任务管理等
- **tools/**: 工具集成，包含 MCP 工具、搜索工具等

### 配置管理

项目使用分层配置系统：
- 环境变量 (`.env`)
- YAML 配置文件 (`config_daily.yaml`, `config_prod.yaml`)
- 运行时配置

### API 接口

主要 API 端点：
- `POST /api/token` - 获取访问令牌
- `POST /api/v1/tasks` - 创建诊断任务
- `GET /api/v1/tasks/{task_id}` - 获取任务状态
- `GET /docs` - API 文档

## 📝 部署

### 使用 Circus 部署

```bash
# 启动服务
circusd conf/circus.ini

# 查看状态
circusctl status

# 重启服务
circusctl restart gunicorn
```

### 环境变量配置

关键环境变量：
- `APP_ENV`: 应用环境 (development/production)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

