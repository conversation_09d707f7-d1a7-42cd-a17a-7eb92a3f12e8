#!/bin/bash

# Start ECS Deep Diagnose server.
# If the user presses Ctrl+C, kill them both.

# 保存项目根目录
PROJECT_ROOT=$(pwd)

# Default to production mode
BACKEND_CMD="uv run server.py"
MODE="PRODUCTION"

if [ "$1" = "--dev" -o "$1" = "-d" -o "$1" = "dev" -o "$1" = "development" ]; then
  MODE="DEVELOPMENT"
  BACKEND_CMD="uv run server.py --reload"
fi

echo -e "Starting ECS Deep Diagnose in [$MODE] mode...
"

# 启动后端服务
echo "Starting server..."
cd "$PROJECT_ROOT/src" && $BACKEND_CMD & SERVER_PID=$!

PIDS_TO_KILL="$SERVER_PID"


trap "kill $PIDS_TO_KILL 2>/dev/null" SIGINT SIGTERM
wait
