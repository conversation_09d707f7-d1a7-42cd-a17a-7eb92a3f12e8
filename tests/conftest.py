"""
Pytest configuration file for backend tests.
"""

import sys
import os
import pytest
import asyncio

# Add the 'src' directory to the Python path to allow for absolute imports
# from 'deep_diagnose' modules.
# tests_dir -> /path/to/project/tests
tests_dir = os.path.dirname(os.path.abspath(__file__))
# project_root -> /path/to/project
project_root = os.path.dirname(tests_dir)
# src_dir -> /path/to/project/src
src_dir = os.path.join(project_root, 'src')

if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

# Set test environment before importing any modules
os.environ.setdefault('APP_ENV', 'daily')


@pytest.fixture(scope="session")
async def database():
    """Initialize database for tests"""
    try:
        from deep_diagnose.data.database import db_manager
        
        # Initialize database connection
        await db_manager.init_database()
        print("✅ Test database initialized")
        
        yield
        
        # Clean up database connection
        await db_manager.close_database()
        print("✅ Test database closed")
        
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        # For integration tests, we might want to use SQLite as fallback
        try:
            from tortoise import Tortoise
            await Tortoise.init(
                db_url="sqlite://:memory:",
                modules={'models': [
                    'deep_diagnose.domain.user.models',
                    'deep_diagnose.domain.chat.models'
                ]}
            )
            await Tortoise.generate_schemas()
            print("✅ Fallback SQLite database initialized")
            
            yield
            
            await Tortoise.close_connections()
            print("✅ Fallback SQLite database closed")
            
        except Exception as fallback_error:
            print(f"❌ Fallback database initialization failed: {fallback_error}")
            raise


@pytest.fixture(scope="session")
async def test_users(database):
    """Create test users for authentication"""
    try:
        from deep_diagnose.domain.user.models import CloudbotAgentUser
        
        # Create test users
        test_users_data = [
            {"username": "admin", "password": "admin"},
        ]
        
        created_users = []
        for user_data in test_users_data:
            # Check if user already exists
            existing_user = await CloudbotAgentUser.get_or_none(username=user_data["username"])
            if existing_user:
                print(f"✅ Test user already exists: {user_data['username']}")
                created_users.append(existing_user)
            else:
                user = await CloudbotAgentUser.create(**user_data)
                print(f"✅ Test user created: {user_data['username']}")
                created_users.append(user)
        
        yield created_users
        
        # Cleanup is handled by database fixture
        
    except Exception as e:
        print(f"❌ Test user creation failed: {e}")
        yield []


@pytest.fixture(autouse=True)
async def setup_test_environment(database, test_users):
    """Auto-setup test environment for all tests"""
    # This fixture runs before each test and ensures database and users are ready
    pass


# Event loop fixture for async tests
@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()
