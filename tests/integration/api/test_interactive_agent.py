"""
InteractiveAgent Chat V1 API 集成测试

测试 InteractiveAgent 的 SSE 事件，确保与 V1 Tasks API 返回的 SSE 事件完全一样。
"""

import pytest
import os
import sys
from typing import Dict, Any
from pathlib import Path

# 添加当前目录到 Python 路径，以支持直接运行
current_dir = Path(__file__).parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

from base_api_test_framework import ChatV1APITester

# 设置测试环境
os.environ.setdefault('APP_ENV', 'test')


class InteractiveAgentTester(ChatV1APITester):
    """InteractiveAgent 专用测试器"""
    
    async def test_interactive_agent_sse_events(self, question: str, additional_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """测试 InteractiveAgent 的 SSE 事件，复用通用流式方法。"""
        request_data = {
            "question": question,
            "agent": "InteractiveAgent",
            "user_id": "149789",
            "additional_info": additional_info,
        }
        return await self._stream_chat(request_data, save_to_file=True, file_prefix="interactive_agent")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_interactive_agent_sse_events_e2e():
    """
    InteractiveAgent SSE 事件端到端测试（需要服务器运行）
    
    运行此测试前，请确保：
    1. 启动后端服务器：python src/run.py
    2. 服务器运行在 http://localhost:8000
    """
    print("\n" + "="*80)
    print("开始 InteractiveAgent SSE 事件端到端测试")
    print("="*80)
    
    # 初始化测试器
    tester = InteractiveAgentTester()
    
    # 身份认证
    if not await tester.setup_authentication():
        pytest.skip("认证失败，跳过测试")
    
    # 准备测试数据
    test_question = "请分析这段日志，找出可能的异常原因。"
    print(f"📝 测试问题: {test_question}")
    
    # 测试 InteractiveAgent
    print("🚀 开始测试 InteractiveAgent SSE 事件...")
    try:
        result = await tester.test_interactive_agent_sse_events(
            test_question, 
            additional_info={"context": "系统日志分析"}
        )
        
        # 验证结果
        tester.validate_sse_response(result)
        
        print("🎉 InteractiveAgent SSE 事件测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_interactive_agent_with_context():
    """
    测试 InteractiveAgent 带上下文信息的交互
    """
    print("\n" + "="*80)
    print("开始 InteractiveAgent 上下文交互测试")
    print("="*80)
    
    # 初始化测试器
    tester = InteractiveAgentTester()
    
    # 身份认证
    if not await tester.setup_authentication():
        pytest.skip("认证失败，跳过测试")
    
    # 准备测试数据
    test_question = "帮我分析一下这个错误日志的根本原因"
    additional_info = {
        "context": "生产环境故障排查",
        "log_level": "ERROR",
        "timestamp": "2025-08-27 10:30:00",
        "service": "user-service"
    }
    
    print(f"📝 测试问题: {test_question}")
    print(f"📋 附加信息: {additional_info}")
    
    # 测试 InteractiveAgent
    print("🚀 开始测试 InteractiveAgent 上下文交互...")
    try:
        result = await tester.test_interactive_agent_sse_events(test_question, additional_info)
        
        # 验证结果
        tester.validate_sse_response(result)
        
        print("🎉 InteractiveAgent 上下文交互测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_interactive_agent_troubleshooting():
    """
    测试 InteractiveAgent 故障排查场景
    """
    print("\n" + "="*80)
    print("开始 InteractiveAgent 故障排查测试")
    print("="*80)
    
    # 初始化测试器
    tester = InteractiveAgentTester()
    
    # 身份认证
    if not await tester.setup_authentication():
        pytest.skip("认证失败，跳过测试")
    
    # 准备测试数据
    test_question = "系统响应时间突然变慢，请帮我分析可能的原因和解决方案"
    additional_info = {
        "context": "性能问题排查",
        "symptoms": ["响应时间从100ms增加到2000ms", "CPU使用率正常", "内存使用率偏高"],
        "environment": "生产环境",
        "affected_services": ["api-gateway", "user-service", "order-service"]
    }
    
    print(f"📝 测试问题: {test_question}")
    print(f"📋 附加信息: {additional_info}")
    
    # 测试 InteractiveAgent
    print("🚀 开始测试 InteractiveAgent 故障排查...")
    try:
        result = await tester.test_interactive_agent_sse_events(test_question, additional_info)
        
        # 验证结果
        tester.validate_sse_response(result)
        
        print("🎉 InteractiveAgent 故障排查测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


if __name__ == "__main__":
    """
    直接运行 InteractiveAgent 测试
    
    运行方式：
    python tests/integration/api/test_interactive_agent.py
    
    # 或使用 pytest
    pytest tests/integration/api/test_interactive_agent.py -v -s
    
    # 运行特定测试
    pytest tests/integration/api/test_interactive_agent.py::test_interactive_agent_sse_events_e2e -v -s
    """
    pytest.main([__file__, "-v", "-s", "--tb=short"])
    print("InteractiveAgent 测试完成！")
