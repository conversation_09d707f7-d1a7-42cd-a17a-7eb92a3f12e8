"""
Inspect Agent Langfuse 集成测试

验证 Inspect Agent 的 Langfuse 跟踪功能和数据收集完整性
"""

from langfuse.callback.langchain import LangchainCallbackHandler


import asyncio
import logging
import pytest
from typing import List, Dict, Any

from deep_diagnose.core.interactive.agents.inspect import InspectAgent
from deep_diagnose.core.interactive.workflows.inspect_workflow import (
    stream_inspect_workflow,
    create_langfuse_handler
)
from deep_diagnose.core.events.inspect_event import InspectEvent


logger = logging.getLogger(__name__)


@pytest.mark.asyncio
class TestLangfuseInspectIntegration:
    """Inspect Agent Langfuse 集成测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.test_machine_id = "i-test123456"
        self.test_start_time = "2025-08-21 01:00:00"
        self.test_end_time = "2025-08-21 23:59:59"
    
    async def test_langfuse_handler_creation(self):
        """测试 Langfuse 处理器创建"""
        print("\n=== 测试 Langfuse 处理器创建 ===")
        
        # 创建 Langfuse 处理器
        handler: LangchainCallbackHandler | None = create_langfuse_handler(
            session_id="test_session",
            user_id="test_user",
            trace_name="test_inspect"
        )
        
        if handler:
            print("✓ Langfuse 处理器创建成功")
            # 测试清理
            try:
                handler.flush()
                print("✓ Langfuse 处理器清理成功")
            except Exception as e:
                print(f"⚠ Langfuse 处理器清理失败: {e}")
        else:
            print("⚠ Langfuse 处理器创建失败（可能是配置问题）")
    
    async def test_inspect_agent_langfuse_integration(self):
        """测试 InspectAgent Langfuse 集成"""
        print("\n=== 测试 InspectAgent Langfuse 集成 ===")
        
        # 创建 InspectAgent
        agent = InspectAgent({})
        
        # 准备测试参数
        additional_info = {
            "machine_id": self.test_machine_id,
            "start_time": self.test_start_time,
            "end_time": self.test_end_time,
        }
        
        events_received = []
        overview_updates = 0
        recommendations_updates = 0
        
        try:
            print(f"📡 开始执行 InspectAgent.astream...")
            print(f"🔧 机器ID: {self.test_machine_id}")
            print(f"⏰ 时间范围: {self.test_start_time} ~ {self.test_end_time}")
            
            async for event in agent.astream(
                question="请分析这个机器的状态",
                additional_info=additional_info
            ):
                events_received.append(event)
                
                # 统计更新次数
                if hasattr(event, 'overview') and event.overview:
                    overview_updates += 1
                    if len(event.overview) > 50:  # 避免输出太长
                        print(f"📝 概览更新: {event.overview[:50]}...")
                    else:
                        print(f"📝 概览更新: {event.overview}")
                
                if hasattr(event, 'recommendations') and event.recommendations:
                    recommendations_updates += 1
                    print(f"💡 推荐更新: 收到 {len(event.recommendations)} 条推荐")
                
                if hasattr(event, 'finished') and event.finished:
                    print("✅ 执行完成")
                    break
        
        except Exception as e:
            print(f"❌ 执行失败: {e}")
            raise
        
        # 验证结果
        print(f"\n📊 执行统计:")
        print(f"   - 总事件数: {len(events_received)}")
        print(f"   - 概览更新次数: {overview_updates}")
        print(f"   - 推荐更新次数: {recommendations_updates}")
        
        # 基本验证
        assert len(events_received) > 0, "应该至少收到一个事件"
        
        # 检查最终事件
        final_event = events_received[-1]
        assert hasattr(final_event, 'finished'), "最终事件应该有 finished 属性"
        assert final_event.finished == True, "最终事件应该标记为完成"
        
        print("✓ 基本验证通过")
    
    async def test_stream_inspect_workflow_langfuse(self):
        """测试 stream_inspect_workflow Langfuse 集成"""
        print("\n=== 测试 stream_inspect_workflow Langfuse 集成 ===")
        
        events_received = []
        steps_observed = set()
        
        try:
            print(f"📡 开始执行 stream_inspect_workflow...")
            
            async for event in stream_inspect_workflow(
                machine_id=self.test_machine_id,
                start_time=self.test_start_time,
                end_time=self.test_end_time,
                session_id="test_session_workflow",
                user_id="test_user_workflow",
                trace_id="test_trace_workflow",
                with_memory=False
            ):
                events_received.append(event)
                
                # 记录观察到的步骤
                if hasattr(event, 'overview') and "正在" in event.overview:
                    steps_observed.add(event.overview)
                    print(f"🔧 步骤: {event.overview}")
                
                if hasattr(event, 'finished') and event.finished:
                    print("✅ 工作流完成")
                    break
        
        except Exception as e:
            print(f"❌ 工作流执行失败: {e}")
            # 非关键错误，可能是由于测试环境配置问题
            print("⚠ 这可能是由于测试环境中缺少必要的服务配置")
            pytest.skip(f"跳过测试，原因: {e}")
        
        print(f"\n📊 工作流统计:")
        print(f"   - 总事件数: {len(events_received)}")
        print(f"   - 观察到的步骤: {len(steps_observed)}")
        
        for step in steps_observed:
            print(f"     * {step}")
        
        # 验证事件流
        if events_received:
            assert len(events_received) > 0, "应该收到事件"
            print("✓ 工作流验证通过")
    
    async def test_data_collection_completeness(self):
        """测试数据收集完整性"""
        print("\n=== 测试数据收集完整性 ===")
        
        # 导入数据收集节点进行单独测试
        from deep_diagnose.core.interactive.nodes.data_collection_node import data_collection_node
        from deep_diagnose.core.interactive.types.state import InspectState
        from deep_diagnose.common.utils.machine_utils import MachineIdUtil
        
        # 创建测试状态
        state = InspectState(
            machine_id=self.test_machine_id,
            start_time=self.test_start_time,
            end_time=self.test_end_time,
            machine_id_type=MachineIdUtil.get_machine_id_type(self.test_machine_id)
        )
        
        try:
            print(f"🔧 测试数据收集节点...")
            result_state = await data_collection_node(state)
            
            # 验证结果
            print(f"📊 数据收集结果:")
            print(f"   - 当前步骤: {result_state.current_step}")
            print(f"   - 工具映射数量: {len(result_state.tool_map)}")
            print(f"   - 信息数据键: {list(result_state.info_data.keys())}")
            
            if result_state.step_metadata:
                print(f"   - 步骤元数据: {result_state.step_metadata}")
            
            # 基本验证
            assert result_state.current_step == "data_collection", "步骤应该正确设置"
            assert isinstance(result_state.tool_map, dict), "工具映射应该是字典"
            assert isinstance(result_state.info_data, dict), "信息数据应该是字典"
            
            print("✓ 数据收集完整性验证通过")
            
        except Exception as e:
            print(f"❌ 数据收集测试失败: {e}")
            # 这可能是由于测试环境中缺少 MCP 工具
            print("⚠ 这可能是由于测试环境中缺少 MCP 工具配置")
            pytest.skip(f"跳过测试，原因: {e}")
    
    async def test_langfuse_trace_metadata(self):
        """测试 Langfuse 跟踪元数据"""
        print("\n=== 测试 Langfuse 跟踪元数据 ===")
        
        # 测试元数据传递
        test_session_id = "metadata_test_session"
        test_user_id = "metadata_test_user"
        test_trace_id = "metadata_test_trace"
        
        events_with_metadata = []
        
        try:
            async for event in stream_inspect_workflow(
                machine_id=self.test_machine_id,
                start_time=self.test_start_time,
                end_time=self.test_end_time,
                session_id=test_session_id,
                user_id=test_user_id,
                trace_id=test_trace_id,
                with_memory=False
            ):
                events_with_metadata.append(event)
                
                # 检查事件是否包含跟踪信息
                if hasattr(event, 'machine_id'):
                    print(f"🏷️ 事件机器ID: {event.machine_id}")
                
                if hasattr(event, 'finished') and event.finished:
                    break
        
        except Exception as e:
            print(f"❌ 元数据测试失败: {e}")
            pytest.skip(f"跳过测试，原因: {e}")
        
        print(f"📊 元数据测试结果:")
        print(f"   - 收到事件数: {len(events_with_metadata)}")
        print(f"   - 会话ID: {test_session_id}")
        print(f"   - 用户ID: {test_user_id}")
        print(f"   - 跟踪ID: {test_trace_id}")
        
        print("✓ 元数据测试完成")


# 运行测试的便捷函数
async def run_all_tests():
    """运行所有测试"""
    print("🚀 开始 Langfuse Inspect 集成测试套件")
    print("=" * 80)
    
    test_instance = TestLangfuseInspectIntegration()
    test_instance.setup_method()
    
    tests = [
        test_instance.test_langfuse_handler_creation,
        test_instance.test_inspect_agent_langfuse_integration,
        test_instance.test_stream_inspect_workflow_langfuse,
        test_instance.test_data_collection_completeness,
        test_instance.test_langfuse_trace_metadata,
    ]
    
    passed = 0
    failed = 0
    skipped = 0
    
    for i, test in enumerate(tests, 1):
        test_name = test.__name__
        print(f"\n🧪 测试 {i}/{len(tests)}: {test_name}")
        print("-" * 60)
        
        try:
            await test()
            print(f"✅ {test_name} 通过")
            passed += 1
        except pytest.skip.Exception as e:
            print(f"⏭️ {test_name} 跳过: {e}")
            skipped += 1
        except Exception as e:
            print(f"❌ {test_name} 失败: {e}")
            failed += 1
    
    print("\n" + "=" * 80)
    print(f"🏁 测试完成:")
    print(f"   ✅ 通过: {passed}")
    print(f"   ❌ 失败: {failed}")
    print(f"   ⏭️ 跳过: {skipped}")
    print(f"   📊 总计: {len(tests)}")
    
    if failed == 0:
        print("\n🎉 所有测试都通过或被合理跳过！")
    else:
        print(f"\n⚠️ 有 {failed} 个测试失败，请检查日志")


if __name__ == "__main__":
    # 直接运行测试
    asyncio.run(run_all_tests())