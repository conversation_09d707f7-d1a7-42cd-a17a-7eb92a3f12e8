"""
Report API V1 集成测试

测试报告API的端到端功能，包括从数据库查询AI消息获取URLs并返回HTML内容。
"""

import json
import httpx
import pytest
import os
from typing import Dict, Any, Optional
from datetime import datetime

# 设置测试环境
os.environ.setdefault('APP_ENV', 'test')


class ReportAPITester:
    """Report API 测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8000", token: str = None):
        self.base_url = base_url
        self.token = token
        self.headers = {"Content-Type": "application/json"}
        if token:
            self.headers["Authorization"] = f"Bearer {token}"
    
    async def authenticate(self) -> str:
        """获取认证token"""
        auth_data = {
            "access_key": "admin",
            "secret_key": "admin",
            "token_lifetime_minutes": 60
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                f"{self.base_url}/api/token",
                json=auth_data,
                headers={"Content-Type": "application/json"}
            )
            if response.status_code == 200:
                token_data = response.json()
                return token_data.get("access_token")
            else:
                raise Exception(f"Authentication failed: {response.status_code} - {response.text}")


    async def test_get_report_html(self, request_id: str) -> Dict[str, Any]:
        """测试获取报告HTML内容"""
        async with httpx.AsyncClient(timeout=httpx.Timeout(60.0)) as client:
            response = await client.get(
                f"{self.base_url}/api/v1/report/{request_id}",
                headers=self.headers
            )
            
            return {
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "content": response.text,
                "content_length": len(response.text) if response.text else 0
            }


@pytest.mark.asyncio
@pytest.mark.integration
async def test_report_api_get_html_content():
    """
    测试报告API获取HTML内容功能
    
    运行此测试前，请确保：
    1. 启动后端服务器：python src/run.py
    2. 服务器运行在 http://localhost:8000
    """
    print("\n" + "="*80)
    print("开始 Report API HTML 内容获取测试")
    print("="*80)
    
    # 初始化测试器
    tester = ReportAPITester()
    test_request_id = None
    
    try:
        print("🔐 正在进行身份认证...")
        token = await tester.authenticate()
        tester.token = token
        tester.headers["Authorization"] = f"Bearer {token}"
        print("✅ 身份认证成功")
        
        print("📝 正在设置测试数据...")
        test_request_id = 'req_f8fe31d4-aa3a-41e3-a209-f649e1e5b0d3'
        print(f"✅ 测试数据设置完成，request_id: {test_request_id}")
        
        print("🚀 开始测试获取报告HTML内容...")
        result = await tester.test_get_report_html(test_request_id)
        print(f"✅ 获取报告HTML内容成功 {result}")
        
        # 验证响应
        assert result["status_code"] == 200, f"期望状态码200，实际: {result['status_code']}"
        assert result["content_length"] > 0, "HTML内容不应为空"
        assert "text/html" in result["headers"].get("content-type", ""), "Content-Type应为text/html"
        
        print(f"✅ 状态码: {result['status_code']}")
        print(f"✅ Content-Type: {result['headers'].get('content-type', 'unknown')}")
        print(f"✅ 内容长度: {result['content_length']} 字符")
        
        # 验证HTML内容格式
        content = result["content"]
        assert content.strip().startswith("<!DOCTYPE") or content.strip().startswith("<html"), "应返回有效的HTML内容"
        
        print("✅ HTML内容格式验证通过")
        print("🎉 报告API HTML内容获取测试通过！")
        
    except Exception as e:
        pytest.skip(f"测试失败: {e}")








if __name__ == "__main__":
    """
    直接运行测试
    
    运行方式：
    # 运行所有报告API测试
    pytest tests/integration/api/test_report_api_integration.py -v -s
    
    # 运行特定测试
    pytest tests/integration/api/test_report_api_integration.py::test_report_api_specific_request_id -v -s
    """
    import pytest
    
    pytest.main([__file__, "-v", "-s", "--tb=short"])
    print("报告API集成测试完成！")