import asyncio
import json
import sys
import pytest
import os
from datetime import datetime
import httpx

# 设置测试环境
os.environ.setdefault("APP_ENV", "test")


class ChatMessagesTester:
    """Chat Messages API 测试器"""

    def __init__(self, base_url: str = "http://localhost:8000", token: str = None):
        self.base_url = base_url
        self.token = token
        self.headers = {"Content-Type": "application/json"}
        if token:
            self.headers["Authorization"] = f"Bearer {token}"

    async def authenticate(self) -> str:
        """获取认证token"""
        auth_data = {"access_key": "admin", "secret_key": "admin", "token_lifetime_minutes": 60}

        async with httpx.AsyncClient() as client:
            response = await client.post(f"{self.base_url}/api/token", json=auth_data, headers={"Content-Type": "application/json"})
            if response.status_code == 200:
                token_data = response.json()
                return token_data.get("access_token")
            else:
                raise Exception(f"Authentication failed: {response.status_code} - {response.text}")

    async def test_get_messages(self, session_id: str):
        """
        测试获取会话消息列表

        Args:
            session_id: 会话ID

        Returns:
            Dict: 测试结果
        """
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{self.base_url}/api/v1/chat/sessions/{session_id}/messages", headers=self.headers)

            if response.status_code != 200:
                raise Exception(f"Get messages failed: {response.status_code} - {await response.aread()}")

            messages = response.json()
            print(f"✅ 成功获取消息列表，共 {len(messages)} 条消息")

            # 保存结果到文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_dir = "tests/integration/api/messages_output"
            os.makedirs(output_dir, exist_ok=True)
            file_path = f"{output_dir}/messages_{session_id}_{timestamp}.json"

            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(messages, f, ensure_ascii=False, indent=2)

            print(f"✅ 消息数据已保存到文件: {file_path}")

            return {"messages": messages, "file_path": file_path}


@pytest.mark.asyncio
@pytest.mark.integration
async def test_get_session_messages():
    """
    获取会话消息列表的端到端测试（需要服务器运行）

    运行此测试前，请确保：
    1. 启动后端服务器：python src/run.py
    2. 服务器运行在 http://localhost:8000
    """
    print("\n" + "=" * 80)
    print("开始 Chat Messages API 测试")
    print("=" * 80)

    # 初始化测试器
    tester = ChatMessagesTester()

    print("🔐 正在进行身份认证...")
    try:
        token = await tester.authenticate()
        tester.token = token
        tester.headers["Authorization"] = f"Bearer {token}"
        print("✅ 身份认证成功")
    except Exception as e:
        pytest.skip(f"认证失败，跳过测试: {e}")

    # 使用测试会话ID
    test_session_id = "21f3921e-be4d-4f38-80fe-61b395d9be92"
    print(f"👥 正在测试会话: {test_session_id}")

    # 测试获取消息列表
    print("🚀 开始测试获取会话消息列表...")
    try:
        result = await tester.test_get_messages(test_session_id)

        # 验证结果
        messages = result["messages"]
        assert isinstance(messages, list), "返回的消息列表应该是数组类型"

        print(f"✅ 测试成功，获取到 {len(messages)} 条消息")

        if result.get("file_path") and messages:
            print(f"📄 示例消息内容: {messages[0].get('message')}")
            print(f"📄 示例消息类型: {messages[0].get('message_type')}")
            print(f"🕒 示例消息创建时间: {messages[0].get('gmt_create')}")
            print(f"📁 数据已保存到: {result['file_path']}")

        print("🎉 Chat Messages API 测试通过！")
        print("=" * 80)

    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


if __name__ == "__main__":
    """
    直接运行测试

    运行方式：
    # 无需服务器的测试
    python tests/integration/api/test_chat_messages_integration.py

    # 或使用 pytest
    pytest tests/integration/api/test_chat_messages_integration.py -v -s
    """
    print("运行 Chat Sessions API 集成测试...")

    # 使用 pytest 运行测试
    exit_code = pytest.main([__file__, "-v", "-s", "--tb=short"])

    sys.exit(exit_code)
