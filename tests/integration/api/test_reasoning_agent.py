"""
ReasoningAgent Chat V1 API 集成测试

测试 ReasoningAgent 的 SSE 事件，确保与 V1 Tasks API 返回的 SSE 事件完全一样。
"""

import pytest
import os
import sys
import asyncio
from typing import Dict, Any
from pathlib import Path

# 添加当前目录到 Python 路径，以支持直接运行
current_dir = Path(__file__).parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

from base_api_test_framework import ChatV1APITester

# 设置测试环境
os.environ.setdefault('APP_ENV', 'test')


class ReasoningAgentTester(ChatV1APITester):
    """ReasoningAgent 专用测试器"""
    
    async def test_reasoning_agent_sse_events(self, question: str, save_to_file: bool = True) -> Dict[str, Any]:
        """
        测试 ReasoningAgent 的 SSE 事件（使用通用流式方法），并统计时延信息。
        """
        request_data = {
            "question": question,
            "agent": "ReasoningAgent",
            "user_id": "149789",
        }
        return await self._stream_chat(request_data, save_to_file=save_to_file, file_prefix="reasoning_agent")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_reasoning_agent_sse_events_e2e():
    """
    ReasoningAgent SSE 事件端到端测试（需要服务器运行）
    
    运行此测试前，请确保：
    1. 启动后端服务器：python src/run.py
    2. 服务器运行在 http://localhost:8000
    """
    print("\n" + "="*80)
    print("开始 ReasoningAgent SSE 事件端到端测试")
    print("="*80)
    
    # 初始化测试器
    tester = ReasoningAgentTester()
    
    # 身份认证
    if not await tester.setup_authentication():
        pytest.skip("认证失败，跳过测试")
    
    # 准备测试数据
    test_question = "利用vmcore分析这个NC ************* 在20250724的04:00宕机原因"
    test_question = "利用vmcore分析这个NC *********** 在20250826的宕机原因"
    print(f"📝 测试问题: {test_question}")
    
    # 测试 ReasoningAgent
    print("🚀 开始测试 ReasoningAgent SSE 事件...")
    try:
        result = await tester.test_reasoning_agent_sse_events(test_question, save_to_file=True)
        
        # 验证结果
        tester.validate_sse_response(result)
        
        print("🎉 ReasoningAgent SSE 事件测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_reasoning_agent_capabilities_question():
    """
    测试 ReasoningAgent 回答能力相关问题
    """
    print("\n" + "="*80)
    print("开始 ReasoningAgent 能力问题测试")
    print("="*80)
    
    # 初始化测试器
    tester = ReasoningAgentTester()
    
    # 身份认证
    if not await tester.setup_authentication():
        pytest.skip("认证失败，跳过测试")
    
    # 准备测试数据
    test_question = "你有哪些能力呢"
    print(f"📝 测试问题: {test_question}")
    
    # 测试 ReasoningAgent
    print("🚀 开始测试 ReasoningAgent 能力问题...")
    try:
        result = await tester.test_reasoning_agent_sse_events(test_question, save_to_file=True)
        
        # 验证结果
        tester.validate_sse_response(result)
        
        print("🎉 ReasoningAgent 能力问题测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_reasoning_agent_health_report_query():
    """
    测试 ReasoningAgent 查询健康报告
    """
    print("\n" + "="*80)
    print("开始 ReasoningAgent 健康报告查询测试")
    print("="*80)
    
    # 初始化测试器
    tester = ReasoningAgentTester()
    
    # 身份认证
    if not await tester.setup_authentication():
        pytest.skip("认证失败，跳过测试")
    
    # 准备测试数据
    test_question = "查询用户aliUid 1781574661016173的最近2天健康报告"
    print(f"📝 测试问题: {test_question}")
    
    # 测试 ReasoningAgent
    print("🚀 开始测试 ReasoningAgent 健康报告查询...")
    try:
        result = await tester.test_reasoning_agent_sse_events(test_question, save_to_file=True)
        
        # 验证结果
        tester.validate_sse_response(result)
        
        print("🎉 ReasoningAgent 健康报告查询测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_reasoning_agent_instance_performance_analysis():
    """
    测试 ReasoningAgent 实例性能数据采集与异常诊断
    
    测试场景：
    1. 实例性能异常诊断
    2. 查询实例和NC信息
    3. 检查监控异常和NC状态
    4. 采集性能数据并生成图表
    5. 验证SOP流程的完整执行
    """
    print("\n" + "="*80)
    print("开始 ReasoningAgent 实例性能分析测试")
    print("="*80)
    
    # 初始化测试器
    tester = ReasoningAgentTester()
    
    # 身份认证
    if not await tester.setup_authentication():
        pytest.skip("认证失败，跳过测试")
    
    # 准备测试数据 - 模拟实例性能异常问题
    test_question = "实例i-8vb1k5i5wwogeiozet2j i-bp1duea6t5ohk1umx7zk在2025-09-07 00:00:00至2025-09-07 05:00:00期间出现性能异常"
    print(f"📝 测试问题: {test_question}")
    print("🎯 预期触发SOP: instance_performance_analysis")
    print("📋 预期执行步骤:")
    print("   1. 实例性能异常诊断 (runPerformanceDiagnose)")
    print("   2. 查询实例对应的NC信息 (getVmBasicInfo)")
    print("   3. 检查实例异常状态 (listMonitorExceptions)")
    print("   4. 检查NC异常状态 (getNcDownRecord)")
    print("   5. 采集性能数据并生成图表信息 (coder)")
    
    # 测试 ReasoningAgent
    print("🚀 开始测试 ReasoningAgent 实例性能分析...")
    try:
        result = await tester.test_reasoning_agent_sse_events(test_question, save_to_file=True)
        
        # 验证结果
        tester.validate_sse_response(result)
        
        # 额外验证性能分析特定内容
        print("🔍 验证性能分析特定内容...")
        
        # 检查是否包含性能相关关键词
        response_text = str(result.get('messages', []))
        performance_keywords = [
            "性能", "CPU", "内存", "IO", "网络", 
            "runPerformanceDiagnose", "getVmBasicInfo", 
            "listMonitorExceptions", "getNcDownRecord",
            "collect_vm_data", "time_series"
        ]
        
        found_keywords = []
        for keyword in performance_keywords:
            if keyword in response_text:
                found_keywords.append(keyword)
        
        print(f"✅ 找到性能相关关键词: {found_keywords}")
        
        # 检查是否有代码生成相关内容
        if "coder" in response_text or "collect_vm_data" in response_text:
            print("✅ 检测到代码生成步骤")
        
        # 检查是否有数据采集相关内容
        if "time_series" in response_text or "性能数据" in response_text:
            print("✅ 检测到性能数据采集内容")
        
        print("🎉 ReasoningAgent 实例性能分析测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


@pytest.mark.asyncio
@pytest.mark.integration  
async def test_reasoning_agent_performance_analysis_with_specific_metrics():
    """
    测试 ReasoningAgent 特定指标的性能分析
    
    测试场景：测试针对特定性能指标（如IO延迟）的分析
    """
    print("\n" + "="*80)
    print("开始 ReasoningAgent 特定指标性能分析测试")
    print("="*80)
    
    # 初始化测试器
    tester = ReasoningAgentTester()
    
    # 身份认证
    if not await tester.setup_authentication():
        pytest.skip("认证失败，跳过测试")
    
    # 准备测试数据 - 模拟IO性能问题
    test_question = "i-uf6buark76321z169570在2025-08-30 21:00:00至2025-08-31 01:00:00期间出现性能异常"
    print(f"📝 测试问题: {test_question}")
    print("🎯 预期触发SOP: instance_performance_analysis")
    print("📋 预期关注指标: VmStorageIOLatency/read_lat_ms, VmStorageIOLatency/write_lat_ms")
    
    # 测试 ReasoningAgent
    print("🚀 开始测试 ReasoningAgent 特定指标性能分析...")
    try:
        result = await tester.test_reasoning_agent_sse_events(test_question, save_to_file=True)
        
        # 验证结果
        tester.validate_sse_response(result)
        
        # 验证IO相关内容
        response_text = str(result.get('messages', []))
        io_keywords = [
            "IO", "延迟", "VmStorageIOLatency", 
            "read_lat_ms", "write_lat_ms", "IOPS"
        ]
        
        found_io_keywords = []
        for keyword in io_keywords:
            if keyword in response_text:
                found_io_keywords.append(keyword)
        
        print(f"✅ 找到IO相关关键词: {found_io_keywords}")
        
        print("🎉 ReasoningAgent 特定指标性能分析测试通过！")
        print("="*80)
        
    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


@pytest.mark.asyncio
@pytest.mark.integration
async def test_reasoning_agent_for_aone():
    """
    测试 ReasoningAgent 特定指标的性能分析

    测试场景：测试针对特定性能指标（如IO延迟）的分析
    """
    print("\n" + "=" * 80)
    print("开始 ReasoningAgent 特定指标性能分析测试")
    print("=" * 80)

    # 初始化测试器
    tester = ReasoningAgentTester()

    # 身份认证
    if not await tester.setup_authentication():
        pytest.skip("认证失败，跳过测试")

    # 准备测试数据 - 模拟IO性能问题

    #test_question="你有哪些能力呢"
    # 实例出现操作系统崩溃并重启
    test_question = """
    您好，这里将为您解决您刚在（苏州蜗牛数字科技股份有限公司企业标准服务）提出的企业工单，当前值班小二是[@峪森]，您描述的问题为（）。 2025-09-01 10:39:53 阿里云服务台 ### 聊天记录 (2025-09-01 10:39:45) --- 赫皓(2025-09-01 10:39:02): @阿里云服务台 ECS --- 阿里云服务台(2025-09-01 10:39:52): 已为您创建新企业工单 企业工单标题：ECS 企业工单ID：E250901513KDJ1 企业工单状态：处理中 当前小二：峪森 请 进入企业工单专项群。 2025-09-01 10:40:18 公共云TAM 您好，请问您具体想咨询关于ECS的什么问题或需求呢？ 2025-09-01 10:40:55 赫皓 > ###### 云小二 > 您好，请问您具体想咨询关于ECS的什� --------------- #### 实例ID：i-2zej47d699e60ikj68vv出现操作系统崩溃并重启，需要帮忙分析一下原因@云小二 2025-09-01 10:45:48 公共云TAM 您好，经排查：实例i-2zej47d699e60ikj68vv 底层未见异常，由于该实例操作系统内核panic导致系统重启。属于是操作系统崩溃引发的。 针对panic故障需要结合故障时收集到的vmcore进一步分析根因，如之前有配置过kdump建议您可以查看/var/crash/目录下是否生成vmcore，Windows系统可查看C:\目录下的dump文件。可以压缩发给我们分析。 2025-09-01 10:48:52 赫皓 > ###### 云小二 > 您好，经排查：实例i-2zej47d699e60ikj68vv --------------- #### @周闯 闯哥，你看下有vmcore文件嘛 2025-09-01 10:58:20 赫皓 如果没有这个文件的话，还可以通过什么进行分析一下了@峪森 2025-09-01 10:59:36 峪森 @赫皓 看不到原因的 2025-09-01 11:05:28 赫皓 > ###### 峪森 > @赫皓 看不到原因的 --------------- #### @峪森 基于之前的历史经验，有没有啥建议了 2025-09-01 11:09:39 峪森 @赫皓 主要是看不到os内宕机的根因，之前没配过kdump吗 2025-09-01 11:11:38 峪森 建议配下kdump以及hungtask_panic 2025-09-01 11:11:54 赫皓 这个需要闯哥帮忙确认一下@周闯 2025-09-02 10:26:56 峪森 有帮忙确认下吗 2025-09-02 15:59:39 赫皓 i-2zej47d699e60ikj68vv 2025-09-02 15:59:48 赫皓 这个机器好像现在一直在停止中 2025-09-02 16:00:04 赫皓 就是上面那个夯机的机器 2025-09-02 16:00:07 赫皓 @峪森 2025-09-02 16:01:13 赫皓 重启的时候等了大概15分钟，启动后还是刚开始的界面 2025-09-02 16:01:18 赫皓 https://yuntai-app-data.oss-cn-shanghai.aliyuncs.com/08480caa-44fa-4e7b-b54d-cc9aa920f5b2-20250902160120-191.png?Expires=4133952000&OSSAccessKeyId=LTAI5tCHjw6GESD5xjbRwYPN&Signature=pKt0fkA0UD6JWXOYQYWxeRDypM0%3D 2025-09-02 16:12:00 峪森 挂载分区有问题，能授权不 2025-09-02 16:12:45 赫皓 @周闯 闯哥，可以授权重新挂载一下哈 2025-09-02 16:32:59 赫皓 @丁贵清 这个群哈 2025-09-02 16:33:08 丁贵清 > ###### 赫皓 >@丁贵清 这个群哈 --------------- #### [OK] 2025-09-02 16:33:09 赫皓 @峪森 客户可以授权哈 2025-09-02 16:33:25 赫皓 重新挂载对原来的数据没有啥影响吧@峪森 2025-09-02 16:33:34 峪森 不会 2025-09-02 16:35:58 赫皓 授权链接发一下哈 2025-09-02 16:45:23 丁贵清 已经授权了 2025-09-02 16:45:58 赫皓 帮忙操作一下哈@峪森 2025-09-02 16:52:52 峪森 好的，稍等 2025-09-02 17:12:27 丁贵清 怎么样了 2025-09-02 17:17:13 峪森 还得稍等下 2025-09-02 17:30:04 峪森 这个机器帮忙对系统盘打个快照 2025-09-02 17:30:15 峪森 @丁贵清 2025-09-02 17:30:26 丁贵清 > ###### 峪森 >@丁贵清 --------------- #### [OK] 2025-09-02 17:34:09 丁贵清 快照已经好了 2025-09-02 17:36:11 峪森 好的 2025-09-02 18:00:12 丁贵清 现在进展如何了？ 2025-09-02 18:00:57 峪森 这个机器的grub有问题，引导不起来， 2025-09-02 18:01:05 峪森 还需要修下 2025-09-02 18:01:25 丁贵清 好的 2025-09-02 18:21:43 峪森 @丁贵清 修好了，正常了 2025-09-02 18:22:44 丁贵清 好的，我试一下 2025-09-02 18:22:54 峪森 https://yuntai-app-data.oss-cn-shanghai.aliyuncs.com/faeb4c98-f3b6-49e8-92a5-726b0e7c9e7d-20250902182255-512.png?Expires=4133952000&OSSAccessKeyId=LTAI5tCHjw6GESD5xjbRwYPN&Signature=ZM8JYoLWWlGE4Wf1o6RyNKZgql4%3D 2025-09-02 18:23:24 峪森 > ###### 丁贵清 >好的，我试一下 --------------- #### [OK] 2025-09-02 18:28:15 丁贵清 https://yuntai-app-data.oss-cn-shanghai.aliyuncs.com/14eb70ba-841b-4e00-b832-b0bc3283a8f4-20250902182817-541.png?Expires=4133952000&OSSAccessKeyId=LTAI5tCHjw6GESD5xjbRwYPN&Signature=oLFS%2B7lSsJ8G4pLiP0CY%2BZWttXI%3D 2025-09-02 18:28:41 丁贵清 我刚重启了一下，卡在停止中有好几分钟了 2025-09-02 18:28:48 丁贵清 https://yuntai-app-data.oss-cn-shanghai.aliyuncs.com/13000c3b-f9c2-479c-9e1b-3719a859c43a-20250902182849-877.png?Expires=4133952000&OSSAccessKeyId=LTAI5tCHjw6GESD5xjbRwYPN&Signature=%2BZ4ZueDkrT%2FYP39BD061pDbpO6A%3D 2025-09-02 18:33:32 峪森 会先走内部reboot，内部reboot耗时长了 2025-09-02 18:34:04 丁贵清 那这个要怎么解决呢 2025-09-02 18:34:48 峪森 你在机器内reboot看下呢，看看是什么服务卡住了 2025-09-02 18:35:38 峪森 我启动的时候发现有很多服务在做配置 2025-09-02 18:37:46 丁贵清 我那个密码登不进去了，你当时是用那个密码登录的吗？ 2025-09-02 18:39:24 峪森 没有 2025-09-02 18:39:31 峪森 报密码错误 2025-09-02 18:39:43 丁贵清 好的，明天让运维的再试下 2025-09-02 18:39:52 峪森 启动的时候我看到有个自动修改密码的服务 2025-09-02 18:40:04 峪森 你们应该自动改密码了 2025-09-02 18:40:21 丁贵清 等明天的时候让他那边远程登录试一下吧 2025-09-02 18:40:23 丁贵清 谢谢 2025-09-02 18:40:35 峪森 > ###### 丁贵清 > > 等明天的时候让他那边远程登录试一下吧 ------ #### 好的 2025-09-03 13:38:53 公共云TAM 您好，请问远程登录正常 了吗？ 2025-09-03 17:26:08 丁贵清 正常了 2025-09-03 17:29:14 峪森 好的，那工单就先结单了哈，有其他问题您再提单反馈我们处理~ 2025-09-03 17:29:30 丁贵清 好的，麻烦了 2025-09-03 17:30:31 峪森 > ###### 丁贵清 >好的，麻烦了 --------------- #### [抱拳] 2025-09-03 17:31:44 阿里云服务台 【问题描述】ECS实例i-2zej47d699e60ikj68vv操作系统崩溃并重启，导致系统无法正常启动。 【原因分析】操作系统内核panic导致系统重启，具体原因需要结合故障时收集到的vmcore进一步分析。挂载分区存在问题，且grub引导出现问题。 【解决方案】1、建议配置kdump以及hungtask_panic以收集vmcore文件进行进一步分析。 2.无法启动为grub中指定的分区错误导致，已修复。 2025-09-03 17:31:44 公共云TAM 【问题描述】ECS实例i-2zej47d699e60ikj68vv操作系统崩溃并重启，导致系统无法正常启动。 【原因分析】操作系统内核panic导致系统重启，具体原因需要结合故障时收集到的vmcore进一步分析。挂载分区存在问题，且grub引导出现问题。 【解决方案】1、建议配置kdump以及hungtask_panic以收集vmcore文件进行进一步分析。 2.无法启动为grub中指定的分区错误导致，已修复。
    """
    # 实例出现操作系统崩溃并重启，无法开启Kdump服务
    test_question1 = """
    i-2ze7wtmioz39grs4oecd 问题：实例在出现了操作系统崩溃，导致实例被重启，目前在系统内无法开启Kdump服务， 已经授权，需要操作开启Kdump方便后期定位原因 2025-09-04 09:06:13 客户 i-2ze7wtmioz39grs4oecd 问题:实例在出现了操作系统崩溃,导致实例被重启,目前在系统内无法开启Kdump服务, 已经授权,需要操作开启Kdump方便后期定位原因 2025-09-04 09:22:39 服务小二 已收到您提交的问题 2025-09-04 09:28:50 服务小二 您好:您反馈的问题正在为您核实中,请不要关闭工单,处理后将会回复您。 2025-09-04 10:08:51 服务小二 您好,1、 经核实服务器上无法开启转储日志的原因是没有预留crashkernel导致的,这边手动设置了crashkernel,请业务低峰期做好最新的快照备份,在重启服务器观察下转储日志是否可以开启 2、对于宕机的情况,查看日志有内核调用 __list_add_valid+ 函数出错的信息,怀疑是内核问题导致的宕机 查看目前服务器启动的是三方内核,建议降级到centos官方内核运行观察一下,谢谢 2025-09-04 10:10:56 客户 不能升级高版本的内核吗? 2025-09-04 10:17:25 服务小二 您好, 可以升级高版本内核的,但是查看服务器上配置的非centos官方内核,建议使用centos官方内核比较稳定,谢谢 2025-09-04 10:40:41 客户 有官方内核的链接? 2025-09-04 10:55:42 服务小二 您好, 经核实下centos7官方应该是 3.10.0-1160.119.1 这个版本:https://**/centos/7.9.2009/updates/x86_64/Packages/kernel-3.10.0-1160.119.1.el7.x86_64.rpm, 如果要更高的,建议升级服务器到alinux3操作系统,谢谢 2025-09-04 10:57:14 客户 好的
    """
    # 多个实例进行诊断问题
    test_question2 = """
        阿里云实例因实例错误实例重启 2025-09-03 10:36:58 阿里云服务台 ![screenshot](https://img.alicdn.com/imgextra/i3/O1CN017B6UP91aJZE5o3eg0_!!6000000003309-2-tps-1440-216.png) #### @谢依桐 您好，这里将为您解决您刚在（网易互娱企业级服务）提出的[E250903F242MHF](dingtalk://dingtalkclient/page/link?pc_slide=true&url=https%3A%2F%2Fm-yunqi.gts.work%2Fyunqi-customer-h5%2Ftask%2Furge%3Ftid%3D3624380%26departmentId%3D583646874%26snapshot%3D%26language%3D%26openconversitionid%3DcidUOz4w%252FpofdpeDW5tpsrD4g%253D%253D)企业工单，当前值班小二是[@尹立强]，您描述的问题为（[阿里云实例因实例错误实例重启](dingtalk://dingtalkclient/page/link?pc_slide=true&url=https%3A%2F%2Fm-yunqi.gts.work%2Fyunqi-customer-h5%2Ftask%2Furge%3Ftid%3D3624380%26departmentId%3D583646874%26snapshot%3D%26language%3D%26openconversitionid%3DcidUOz4w%252FpofdpeDW5tpsrD4g%253D%253D)）。 2025-09-03 10:36:58 阿里云服务台 ### 聊天记录 (2025-09-03 10:36:54) --- 阿里云服务台(2025-09-03 10:36:57): @谢依桐 已为您创建新企业工单 企业工单标题：阿里云实例因实例错误实例重启 企业工单ID：E250903F242MHF 企业工单状态：处理中 当前小二：尹立强 请[点击此处](dingtalk://dingtalkclient/page/link?pc_slide=true&url=https%3A%2F%2Fm-yunqi.gts.work%2Fyunqi-customer-h5%2Fgroup%2FjoinSubgroup%3FtaskId%3D3624380%26departmentId%3D583646874%26language%3D%26openconversitionid%3DcidwIByieMDOl%252FxIwLEWZcixQ%253D%253D) 进入企业工单专项群。 2025-09-03 10:37:54 谢依桐 i-wz9gcx7u64wghn8ny1z9 i-wz93yk96spgcbocopcji i-wz93hn7jm1k1ei4psi55 i-wz9hgan2bzgxtjz7jo86 i-wz985ddhar8npo7iz980 i-wz92800vffo9601pkak9 帮确认一下这6台实例2025年9月2日 21:26:05之前底层硬件和宿主的状况是否正常 2025-09-03 10:38:30 尹立强 收到，这边看下 2025-09-03 10:39:16 谢依桐 > ###### 尹立强 >收到，这边看下 --------------- #### [OK] 2025-09-03 10:45:31 尹立强 > ###### 谢依桐 > i-wz9gcx7u64wghn8ny1z9 > >i-wz93yk96spgcbocopcji > >i-wz93hn7jm1k1ei4psi55 > >i-wz9hgan2bzgxtjz7jo86 > >i-wz985ddhar8npo7iz980 > >i-wz92800vffo9601pkak9 > >帮确认一下这6台实例2025年9月2日 21:26:05之前底层硬件和宿主的状况是否正常 --------------- #### @谢依桐 这边看了下，这6个ecs实例对应的底层硬件和宿主机均无异常，看ecs实例在该时间点前都是出现了系统内部oom的情况 2025-09-03 10:48:47 谢依桐 > ###### 尹立强 > >#### @谢依桐 这边看了下，这6个ecs实例对应的底层硬件和宿主机均无异常，看ecs实例在该时间点前都是出现了系统内部oom的情况 --------------- #### [OK] 2025-09-03 11:15:43 谢依桐 这个事件查看的地方是有什么时效性的吗？ 2025-09-03 11:16:07 谢依桐 ![d8dd92c2-2871-4037-9060-7418c124f92d-20250903111607-936.png](https://yuntai-app-data.oss-cn-shanghai.aliyuncs.com/d8dd92c2-2871-4037-9060-7418c124f92d-20250903111607-936.png?Expires=4133952000&OSSAccessKeyId=LTAI5tCHjw6GESD5xjbRwYPN&Signature=Ol7ChBaevNBQDXr4FQCwVlS9%2FTg%3D) 刷新一下居然没有了 2025-09-03 11:16:38 谢依桐 ![604e69f7-e0eb-4d28-9d95-081715e798da-20250903111640-948.png](https://yuntai-app-data.oss-cn-shanghai.aliyuncs.com/604e69f7-e0eb-4d28-9d95-081715e798da-20250903111640-948.png?Expires=4133952000&OSSAccessKeyId=LTAI5tCHjw6GESD5xjbRwYPN&Signature=oMHxo4VCfjVxjA54mOe1W5ESB2w%3D) 我前面有截图，长这样的 2025-09-03 11:17:09 尹立强 @谢依桐 看这6台实例是都已经释放掉了 2025-09-03 11:17:27 谢依桐 释放了的话，这里就不会再有记录了对吧？ 2025-09-03 11:17:41 尹立强 > ###### 谢依桐 > 释放了的话，这里就不会再有记录了对吧？ --------------- #### @谢依桐 是的哈 2025-09-03 11:17:43 谢依桐 > ###### 尹立强 > >#### @谢依桐 是的哈 --------------- #### [OK] 2025-09-03 11:18:55 尹立强 @谢依桐 辛苦看下还有其他需要协助的不？ 2025-09-03 11:19:07 谢依桐 暂无，谢谢 2025-09-03 11:19:14 尹立强 > ###### 谢依桐 >暂无，谢谢 --------------- #### [送花花] 2025-09-03 11:19:34 尹立强 客气了哈，那这边就先待确认了，辛苦稍后点下结单按钮并进行评价哈[抱拳][送花花] 2025-09-03 11:21:11 尹立强 【问题描述】ecs实例底层是否有异常 【原因分析】6个ecs实例对应的底层硬件和宿主机均无异常，看ecs实例在该时间点前都是出现了系统内部oom的情况 2025-09-03 11:21:11 阿里云服务台 【问题描述】ecs实例底层是否有异常 【原因分析】6个ecs实例对应的底层硬件和宿主机均无异常，看ecs实例在该时间点前都是出现了系统内部oom的情况
    """
    # 分析宿主机CPU故障原因及G8A架构CPU高故障率问题
    test_question3 = """
        请在【2025-09-05 14:17:30】前进行评论响应，在【2025-09-06 01:17:30】前进行解决，超时未处理，会触发向上升级通知。云台升级单处理地址：https://yuntainext.aliyun-inc.com/order/my/detail?ticketId=452428 【紧急度】普通 【客户名称】北京欧凯联创顶级服务 【提单UID】1559032671635936 【业务影响】产品咨询 【是否有企业支持钉钉群】有 【产品名称】神龙数据稳定性值班（nc值班） 【升级原因】咨询问题 【工单地址】https://ticket.aliyun-inc.com/yunqi-workbench/page/yunqi-workbench?corpId=ding14b15b56be15e048&tenantCode=YUNQI_ETS&eidOrTaskId=3632090 【工单ID】3632090 【问题描述】 问题描述：核实实例宕机的具体原因 实例信息： i-j6c7kmpd1a24gtsi4vxp i-j6c9eeg2o773ludynr25 时间点：2025-09-04 21:09 处理进展：从后台来看是宿主机cpu故障导致的，但是g8a架构用的是不同的cpu，故障率太高了，不符合预期
    """
    # 实例未知原因重启需要诊断
    test_question4 = """
        问下 我有一台服务器 怎么莫名其妙重启了 能帮忙查下原因吗 2025-09-04 09:10:45 客户 问下 我有一台服务器 怎么莫名其妙重启了 能帮忙查下原因吗 2025-09-04 09:11:26 服务小二 您好,目前已进入在线人工服务,售后工程师将尽快为您处理,请稍候。 2025-09-04 09:11:43 客户 问下 我有一台服务器 怎么莫名其妙重启了 能帮忙查下原因吗 刚给我推送的短信 2025-09-04 09:11:48 服务小二 您好,麻烦您提供下实例id/公网ip。谢谢。 2025-09-04 09:12:08 客户 i-bp15fh424r0xa22kyva4 没有公网ip 2025-09-04 09:12:50 服务小二 您好:您的云服务器(实例ID:i-bp15fh424r0xa22kyva4,实例**_git,私网IP:["*************"]在北京时间 2025-09-04T09:08:03 因底层宿主机出现非预期的软硬件故障而宕机,重启的。 2025-09-04 09:13:06 客户 ??? 2025-09-04 09:13:15 客户 这个是怎么回事 2025-09-04 09:13:39 服务小二 您好,这台服务器重启,是因为底层宿主机出现非预期的软硬件故障而宕机,重启的。 2025-09-04 09:14:52 客户 这个风险怎么避免 2025-09-04 09:17:48 服务小二 您好,您可以理解这个是突发性的,不是人为去控制的,重启过程中,我们会将该实例迁移到其他健康的宿主机上。 2025-09-04 09:29:46 服务小二 您好,请问您是否还有其他问题咨询 2025-09-04 09:34:38 客户 行 2025-09-04 09:35:53 服务小二 嗯呢,有其他问题您及时反馈哈,这边协助您查看处理 2025-09-04 09:40:22 服务小二 您好,在线服务还请保持实时交互,长时间没回复,会话将会被系统自动关闭,谢谢 2025-09-04 09:45:30 服务小二 您可能在忙,所以一直没回复。在线服务无法长时间保持,长时间占线会导致其他用户无法接入,若您的问题还需继续处理可以点击下方“唤回工程师”,如您遇到其他问题,可选择提交新问题。
    """
    # 诊断实例因云盘BPS超限导致的连接问题
    test_question5 = """
        ecs问题排查 2025-09-02 16:38:56 阿里云服务台 ![screenshot](https://img.alicdn.com/imgextra/i3/O1CN017B6UP91aJZE5o3eg0_!!6000000003309-2-tps-1440-216.png) #### @Victor 您好，这里将为您解决您刚在（洋钱罐顶级服务）提出的[E250902B73P7W3](dingtalk://dingtalkclient/page/link?pc_slide=true&url=https%3A%2F%2Fm-yunqi.gts.work%2Fyunqi-customer-h5%2Ftask%2Furge%3Ftid%3D3622719%26departmentId%3D66589057%26snapshot%3D%26language%3D%26openconversitionid%3DcideQc9gHfudiCG%252BJpq0ICTeg%253D%253D)企业工单，当前值班小二是[@煊童]，您描述的问题为（[ecs问题排查](dingtalk://dingtalkclient/page/link?pc_slide=true&url=https%3A%2F%2Fm-yunqi.gts.work%2Fyunqi-customer-h5%2Ftask%2Furge%3Ftid%3D3622719%26departmentId%3D66589057%26snapshot%3D%26language%3D%26openconversitionid%3DcideQc9gHfudiCG%252BJpq0ICTeg%253D%253D)）。 2025-09-02 16:38:57 阿里云服务台 ### 聊天记录 (2025-09-02 16:38:53) --- Victor(2025-09-02 16:38:51): @阿里云服务台 ecs问题排查 --- 阿里云服务台(2025-09-02 16:38:56): @Victor 已为您创建新企业工单 企业工单标题：ecs问题排查 企业工单ID：E250902B73P7W3 企业工单状态：处理中 当前小二：煊童 请[点击此处](dingtalk://dingtalkclient/page/link?pc_slide=true&url=https%3A%2F%2Fm-yunqi.gts.work%2Fyunqi-customer-h5%2Fgroup%2FjoinSubgroup%3FtaskId%3D3622719%26departmentId%3D66589057%26language%3D%26openconversitionid%3DcidomHk9juhWAi462Pi%252BMPD9Q%253D%253D) 进入企业工单专项群。 2025-09-02 16:39:08 煊童 您好，请问有什么可以帮到您？ 2025-09-02 16:39:15 Victor 有台节点今天9点20-到10点：30 都连不上，帮忙排查下是什么问题 emr-user@gateway-2-8(172.20.143.44) 2025-09-02 16:39:34 煊童 您好，辛苦提供下实例ID 2025-09-02 16:39:42 Victor https://yuntai-app-data.oss-cn-shanghai.aliyuncs.com/f6cb00b8-d2c2-43f1-8044-18cffb12f734-20250902163944-183.png?Expires=4133952000&OSSAccessKeyId=LTAI5tCHjw6GESD5xjbRwYPN&Signature=gU3H7A7OBl0mrwh1atc4wXKdGvo%3D 2025-09-02 16:40:10 煊童 @Victor 实例ID发下哈 2025-09-02 16:42:30 Victor 我查下 2025-09-02 16:42:48 煊童 好的 2025-09-02 16:47:05 Victor i-2ze1q3tkgd3gtxiccnl6 2025-09-02 16:47:18 煊童 好的 2025-09-02 16:48:36 Victor https://yuntai-app-data.oss-cn-shanghai.aliyuncs.com/58a9a078-31f1-4fb1-bbce-fa5e8437e6ca-20250902164836-470.png?Expires=4133952000&OSSAccessKeyId=LTAI5tCHjw6GESD5xjbRwYPN&Signature=HReZhTHYkpiA%2FoO3Jmo5huh4Sco%3D 2025-09-02 16:50:50 煊童 @Victor 您好，我看了下，早上的时候云盘超限了 2025-09-02 16:51:53 煊童 两块盘的bps都超了 2025-09-02 16:52:32 Victor 能查是哪个程序超了么？或者下次超的时候，如何能让我登录上or记录下 2025-09-02 16:55:50 煊童 > ###### Victor > 能查是哪个程序超了么？或者下次超的时候，如何能让我登录上or记录下 --------------- #### @Victor 当时有试过vnc登陆吗？ 2025-09-02 16:56:47 Victor 当时是ssh登录的 2025-09-02 16:57:41 煊童 > ###### Victor > 当时是ssh登录的 --------------- #### @Victor 您后续如果再次遇到的话可以尝试通过VNC登陆看下，另外也可以通过云监控的进程监控来侧面分析下 2025-09-03 10:08:16 煊童 @Victor 您好，请问针对该问题还有其它我们能够协助您的吗？ 2025-09-03 10:57:03 煊童 @Victor 您好，请问针对该问题还有其它我们能够协助您的吗？ 2025-09-04 17:32:15 煊童 @Victor 您好，鉴于您长时间未反馈，为避免频繁打扰您，当前工单这边就暂时置为待确认了，工单保留7天，您这边后期有问题可以重新提交工单反馈。 2025-09-04 17:32:42 煊童 【问题描述】一台ecs节点在9点20到10点30之间无法连接，实例ID为i-2ze1q3tkgd3gtxiccnl6，原因是云盘bps超限。 【原因分析】两块盘的bps都超限。 【解决方案】1、后续如果再次遇到类似问题，可以通过VNC登陆查看 2、也可以通过云监控的进程监控来侧面分析问题 2025-09-04 17:32:42 阿里云服务台 【问题描述】一台ecs节点在9点20到10点30之间无法连接，实例ID为i-2ze1q3tkgd3gtxiccnl6，原因是云盘bps超限。 【原因分析】两块盘的bps都超限。 【解决方案】1、后续如果再次遇到类似问题，可以通过VNC登陆查看 2、也可以通过云监控的进程监控来侧面分析问题
    """
    # 诊断实例频繁死机问题
    test_question6 = """
        人工服务。 2025-09-03 21:09:23 客户 人工服务。 2025-09-03 21:09:56 客户 ECS频繁死机 2025-09-03 21:10:00 服务小二 您好,目前已进入在线人工服务,售后工程师将尽快为您处理,请稍候。 2025-09-03 21:10:24 服务小二 您好,请您提供下服务器的实例id/IP地址,谢谢。 2025-09-03 21:10:28 客户 i-25w7oxyh9 2025-09-03 21:10:47 客户 这台服务器今天晚上已经死机3次了。 2025-09-03 21:11:04 客户 看下这个工单:000BSK4YUW 2025-09-03 21:11:20 客户 我已经联系你们2次了。 2025-09-03 21:13:07 服务小二 您好,您的这个工单:000BSK4YUW 中的工程师,正在帮您处理 看您之前是给对应工程师有授权服务器。麻烦您稍等,这边帮您联系对应的工程师加急帮您再看一下 2025-09-03 21:13:36 客户 马上,太着急了,已经影响我们业务了。 2025-09-03 21:20:01 服务小二 当前后台工程师正在加急帮您处理。 您后续关注工单:000BSK4YUW 工程师有结果会第一时间给您反馈 2025-09-03 21:29:17 服务小二 您好,后续麻烦您关注工单:000BSK4YUW 当前无其它问题的话。稍后我将结束本次服务;后期如有问题可随时联系我们,我们将竭诚为您服务。
    """
    # 分析三台实例在CPU利用率和内存使用率不高的情况下多次宕机的原因。
    test_question7 = """
        我的三台服务器组cpu利用率和内存不高的情况下，宕机了是什么情况 2025-09-02 18:07:12 客户 我的三台服务器组cpu利用率和内存不高的情况下,宕机了是什么情况 2025-09-02 18:08:10 客户 我的三台服务器组cpu利用率和内存不高的情况下,宕机了是什么情况 2025-09-02 18:08:15 服务小二 您好,目前已进入在线人工服务,售后工程师将尽快为您处理,请稍候。 2025-09-02 18:08:28 服务小二 您好,已经收到您的反馈,正在为您查看中。请耐心等待。谢谢。 2025-09-02 18:08:37 服务小二 您分别反馈下实例ID信息 2025-09-02 18:08:47 客户 你好我alb服务器后面又3台服务器,利用率都不高,单机好几次了 2025-09-02 18:09:13 客户 alb实例id:alb-7gbpava50y8j62sdjz 2025-09-02 18:09:48 客户 i-2ze7eyqs7x9h2hi10yl7 i-2zegufn86w2t79metyhi i-2zedyd31r2cactynk6v3 2025-09-02 18:10:06 客户 这是后端服务器,宕机这是这3台 2025-09-02 18:10:13 服务小二 好的,稍后 2025-09-02 18:11:33 服务小二 查询看近期这三个实例没有宕机的记录,您是业务有出现什么异常么 2025-09-02 18:13:21 客户 我刚才重启了服务器 2025-09-02 18:13:30 客户 我ssh连接失败 2025-09-02 18:13:49 服务小二 现在可以连接么 2025-09-02 18:13:51 客户 附件 2025-09-02 18:14:02 客户 重启之后,过了3分钟,又连不上了 2025-09-02 18:14:22 服务小二 现在可以复现问题么 2025-09-02 18:14:32 客户 附件 2025-09-02 18:14:40 客户 是的,现在也连不上 2025-09-02 18:14:48 客户 这利用率并不高呀 2025-09-02 18:15:10 服务小二 您可以任意提供下其中一台实例的授权,这边登录检查看看 2025-09-02 18:16:18 客户 已操作授权 2025-09-02 18:25:44 服务小二 您好,出现问题前有做过什么特殊操作没 2025-09-02 18:26:03 客户 没做过呀 2025-09-02 18:26:21 客户 安全组操作也没有做 2025-09-02 18:26:34 客户 而且重启也能连上一阵 2025-09-02 18:26:54 客户 我看后台也是正常 2025-09-02 18:27:08 客户 请求也不超时 2025-09-02 18:28:46 服务小二 根据日志信息来看是提示这个模块无法加载,您看方便可以给这个服务器做一个最新的快照备份么,这边尝试修复 2025-09-02 18:30:53 客户 好 2025-09-02 18:32:27 服务小二 OK 看到您已经创建了,该问题修复方法较为复杂,这边帮您把问题转交下后端专员协助您进行处理及跟进下 2025-09-02 18:33:00 服务小二 这边帮您转接工单核实一下是否可以恢复,请您稍等。 您好,您的问题需要通过工单进一步排查处理。稍后我会将当前对话转交工单处理,无需您新建工单,届时您可以通过新创建的工单号继续查看问题处理。 提醒:工单排查需要一定时间,不是实时回复,届时您可以在控制台-工单-我的工单中查看进度并进行回复,辛苦耐心等待。 2025-09-02 18:33:24 客户 好的
    """
    ##test_question ="查询用户aliUid 1781574661016173的最近2天健康报告"
    print(f"📝 测试问题: {test_question}")
    print(f"📝 测试问题: {test_question}")
    print("🎯 预期触发SOP: instance_performance_analysis")
    print("📋 预期关注指标: VmStorageIOLatency/read_lat_ms, VmStorageIOLatency/write_lat_ms")

    # 测试 ReasoningAgent
    print("🚀 开始测试 ReasoningAgent 特定指标性能分析...")
    try:
        result = await tester.test_reasoning_agent_sse_events(test_question, save_to_file=True)

        # 验证结果
        tester.validate_sse_response(result)

        # 验证IO相关内容
        response_text = str(result.get('messages', []))
        io_keywords = [
            "IO", "延迟", "VmStorageIOLatency",
            "read_lat_ms", "write_lat_ms", "IOPS"
        ]

        found_io_keywords = []
        for keyword in io_keywords:
            if keyword in response_text:
                found_io_keywords.append(keyword)

        print(f"✅ 找到IO相关关键词: {found_io_keywords}")

        print("🎉 ReasoningAgent 特定指标性能分析测试通过！")
        print("=" * 80)

    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")



@pytest.mark.asyncio
@pytest.mark.integration
async def test_reasoning_agent_for_aone():
    """
    并发发送多个Aone场景问题到 ReasoningAgent，验证SSE响应
    覆盖 test_question ~ test_question7 共8个请求
    """
    print("\n" + "=" * 80)
    print("开始 ReasoningAgent 并发场景测试 (Aone)")
    print("=" * 80)

    tester = ReasoningAgentTester()
    if not await tester.setup_authentication():
        pytest.skip("认证失败，跳过测试")

    # 准备测试数据 - 复用原用例中的8个问题
    test_question = """
    您好，这里将为您解决您刚在（苏州蜗牛数字科技股份有限公司企业标准服务）提出的企业工单，当前值班小二是[@峪森]，您描述的问题为（）。 2025-09-01 10:39:53 阿里云服务台 ### 聊天记录 (2025-09-01 10:39:45) --- 赫皓(2025-09-01 10:39:02): @阿里云服务台 ECS --- 阿里云服务台(2025-09-01 10:39:52): 已为您创建新企业工单 企业工单标题：ECS 企业工单ID：E250901513KDJ1 企业工单状态：处理中 当前小二：峪森 请 进入企业工单专项群。 2025-09-01 10:40:18 公共云TAM 您好，请问您具体想咨询关于ECS的什么问题或需求呢？ 2025-09-01 10:40:55 赫皓 > ###### 云小二 > 您好，请问您具体想咨询关于ECS的什� --------------- #### 实例ID：i-2zej47d699e60ikj68vv出现操作系统崩溃并重启，需要帮忙分析一下原因@云小二 2025-09-01 10:45:48 公共云TAM 您好，经排查：
    """
    test_question1 = """
    i-2ze7wtmioz39grs4oecd 问题：实例在出现了操作系统崩溃，导致实例被重启，目前在系统内无法开启Kdump服务， 已经授权，需要操作开启Kdump方便后期定位原因 2025-09-04 09:06:13 客户 i-2ze7wtmioz39grs4oecd 问题:实例在出现了操作系统崩溃,导致实例被重启,目前在系统内无法开启Kdump服务, 已经授权,需要操作开启Kdump方便后期定位原因 2025-09-04 09:22:39 服务小二 已收到您提交的问题 2025-09-04 09:28:50 服务小二 您好:您反馈的问题正在为您核实中,请不要关闭工单,处理后将会回复您。 2025-09-04 10:08:51 服务小二 您好,1、 经核实服务器上无法开启转储日志的原因是没有预留crashkernel导致的,这边手动设置了crashkernel,请业务低峰期做好最新的快照备份,在重启服务器观察下转储日志是否可以开启 2、对于宕机的情况,查看日志有内核调用 __list_add_valid+ 函数出错的信息,怀疑是内核问题导致的宕机 查看目前服务器启动的是三方
    """
    test_question2 = """
        阿里云实例因实例错误实例重启 2025-09-03 10:36:58 阿里云服务台 ![screenshot](https://img.alicdn.com/imgextra/i3/O1CN017B6UP91aJZE5o3eg0_!!6000000003309-2-tps-1440-216.png) #### @谢依桐 您好，这里将为您解决您刚在（网易互娱企业级服务）提出的[E250903F242MHF](dingtalk://dingtalkclient/page/link?pc_slide=true&url=https%3A%2F%2Fm-yunqi.gts.work%2Fyunqi-customer-h5%2Ftask%2Furge%3Ftid%3D3624380%26departmentId%3D583646874%26snapshot%3D%26language%3D%26openconversitionid%3DcidUOz4w%252FpofdpeDW5tpsrD4g%253D%253D)企业工单，当前值班小二是[@尹立强]，您描述的问题为（[阿里云实例因
    """
    test_question3 = """
        请在【2025-09-05 14:17:30】前进行评论响应，在【2025-09-06 01:17:30】前进行解决，超时未处理，会触发向上升级通知。云台升级单处理地址：https://yuntainext.aliyun-inc.com/order/my/detail?ticketId=452428 【紧急度】普通 【客户名称】北京欧凯联创顶级服务 【提单UID】1559032671635936 【业务影响】产品咨询 【是否有企业支持钉钉群】有 【产品名称】神龙数据稳定性值班（nc值班） 【升级原因】咨询问题 【工单地址】https://ticket.aliyun-inc.com/yunqi-workbench/page/yunqi-workbench?corpId=ding14b15b56be15e048&tenantCode=YUNQI_ETS&eidOrTaskId=3632090 【工单ID】3632090 【问题描述】 问题描述：核实实例宕机的具体原因 实例信息： i-j6c7kmpd1a24gtsi4vxp i-j6c9eeg2o773ludynr25 时间
    """
    test_question4 = """
        问下 我有一台服务器 怎么莫名其妙重启了 能帮忙查下原因吗 2025-09-04 09:10:45 客户 问下 我有一台服务器 怎么莫名其妙重启了 能帮忙查下原因吗 2025-09-04 09:11:26 服务小二 您好,目前已进入在线人工服务,售后工程师将尽快为您处理,请稍候。 2025-09-04 09:11:43 客户 问下 我有一台服务器 怎么莫名其妙重启了 能帮忙查下原因吗 刚给我推送的短信 2025-09-04 09:11:48 服务小二 您好,麻烦您提供下实例id/公网ip。谢谢。 2025-09-04 09:12:08 客户 i-bp15fh424r0xa22kyva4 没有公网ip 2025-09-04 09:12:50 服务小二 您好:您的云服务器(实例ID:i-bp15fh424r0xa22kyva4,实例**_git,私网IP:[\"*************\"]在北京时间 2025-09-04T09:08:03 因底层宿主机出现非预期的软硬件故障而宕机,重启的。 2025-09-04 09:13:06 客户 ??? 2025-09-04 09:1
    """
    test_question5 = """
        ecs问题排查 2025-09-02 16:38:56 阿里云服务台 ![screenshot](https://img.alicdn.com/imgextra/i3/O1CN017B6UP91aJZE5o3eg0_!!6000000003309-2-tps-1440-216.png) #### @Victor 您好，这里将为您解决您刚在（洋钱罐顶级服务）提出的[E250902B73P7W3](dingtalk://dingtalkclient/page/link?pc_slide=true&url=https%3A%2F%2Fm-yunqi.gts.work%2Fyunqi-customer-h5%2Ftask%2Furge%3Ftid%3D3622719%26departmentId%3D66589057%26snapshot%3D%26language%3D%26openconversitionid%3DcideQc9gHfudiCG%252BJpq0ICTeg%253D%253D)企业工单，当前值班小二是[@煊童]，您描述的问题为（[ecs问题排查](dingt
    """
    test_question6 = """
        人工服务。 2025-09-03 21:09:23 客户 人工服务。 2025-09-03 21:09:56 客户 ECS频繁死机 2025-09-03 21:10:00 服务小二 您好,目前已进入在线人工服务,售后工程师将尽快为您处理,请稍候。 2025-09-03 21:10:24 服务小二 您好,请您提供下服务器的实例id/IP地址,谢谢。 2025-09-03 21:10:28 客户 i-25w7oxyh9 2025-09-03 21:10:47 客户 这台服务器今天晚上已经死机3次了。 2025-09-03 21:11:04 客户 看下这个工单:000BSK4YUW 2025-09-03 21:11:20 客户 我已经联系你们2次了。 2025-09-03 21:13:07 服务小二 您好,您的这个工单:000BSK4YUW 中的工程师,正在帮您处理 看您之前是给对应工程师有授权服务器。麻烦您稍等,这边帮您联系对应的工程师加急帮您再看一下 2025-09-03 21:13:36 客户 马上,太着急了,已经影响我们业务了。 2025-09-03 21:20:01
    """
    test_question7 = """
        我的三台服务器组cpu利用率和内存不高的情况下，宕机了是什么情况 2025-09-02 18:07:12 客户 我的三台服务器组cpu利用率和内存不高的情况下,宕机了是什么情况 2025-09-02 18:08:10 客户 我的三台服务器组cpu利用率和内存不高的情况下,宕机了是什么情况 2025-09-02 18:08:15 服务小二 您好,目前已进入在线人工服务,售后工程师将尽快为您处理,请稍候。 2025-09-02 18:08:28 服务小二 您好,已经收到您的反馈,正在为您查看中。请耐心等待。谢谢。 2025-09-02 18:08:37 服务小二 您分别反馈下实例ID信息 2025-09-02 18:08:47 客户 你好我alb服务器后面又3台服务器,利用率都不高,单机好几次了 2025-09-02 18:09:13 客户 alb实例id:alb-7gbpava50y8j62sdjz 2025-09-02 18:09:48 客户 i-2ze7eyqs7x9h2hi10yl7 i-2zegufn86w2t79metyhi i-2zedyd31r2cactyn
    """

    questions = [
        test_question,
        test_question1,
        test_question2,
        test_question3,
        test_question4,
        test_question5,
        test_question6,
        test_question7,
    ]

    print(f"📝 本次并发测试共 {len(questions)} 个问题")

    async def run_one(idx: int, q: str):
        short = q.strip().replace("\n", " ")[:80]
        print(f"[Task {idx}] ▶️ 开始: {short}...")
        result = await tester.test_reasoning_agent_sse_events(q, save_to_file=False)
        tester.validate_sse_response(result)
        print(f"[Task {idx}] ✅ 完成，共 {result['total_events']} 个事件")
        return result

    tasks = [run_one(i + 1, q) for i, q in enumerate(questions)]
    results = await asyncio.gather(*tasks, return_exceptions=True)

    errors = []
    for i, res in enumerate(results, 1):
        if isinstance(res, Exception):
            print(f"[Task {i}] ❌ 失败: {res}")
            errors.append(res)

    assert not errors, f"存在 {len(errors)} 个并发任务失败"

    print("🎉 ReasoningAgent 并发场景测试通过！")
    print("=" * 80)

if __name__ == "__main__":
    """
    直接运行 ReasoningAgent 测试
    
    运行方式：
    python tests/integration/api/test_reasoning_agent.py
    
    # 或使用 pytest
    pytest tests/integration/api/test_reasoning_agent.py -v -s
    
    # 运行特定测试
    pytest tests/integration/api/test_reasoning_agent.py::test_reasoning_agent_sse_events_e2e -v -s
    
    # 运行性能分析测试
    pytest tests/integration/api/test_reasoning_agent.py::test_reasoning_agent_instance_performance_analysis -v -s
    """
    pytest.main([__file__, "-v", "-s", "--tb=short"])
    print("ReasoningAgent 测试完成！")
