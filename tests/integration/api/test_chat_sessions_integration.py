"""
Chat Sessions API Integration Test

测试 Chat Sessions API 的两个核心接口：
1. GET /api/v1/chat/sessions - 获取用户会话列表
2. GET /api/v1/chat/sessions/{session_id}/messages - 获取会话消息列表
"""

import asyncio
import json
import pytest
import httpx
import os
from typing import Dict, Any, List
import sys

# 设置测试环境
os.environ.setdefault("APP_ENV", "test")


class ChatSessionsAPITester:
    """Chat Sessions API 测试器"""

    def __init__(self, base_url: str = "http://localhost:8000", token: str = ""):
        self.base_url = base_url
        self.token = token
        self.headers = {"Content-Type": "application/json"}
        if token:
            self.headers["Authorization"] = f"Bearer {token}"

    async def authenticate(self) -> str:
        """获取认证token - 与 V1 API 相同的认证方式"""
        auth_data = {"access_key": "admin", "secret_key": "admin", "token_lifetime_minutes": 60}

        async with httpx.AsyncClient() as client:
            response = await client.post(f"{self.base_url}/api/token", json=auth_data, headers={"Content-Type": "application/json"})
            if response.status_code == 200:
                token_data = response.json()
                return token_data.get("access_token")
            else:
                raise Exception(f"Authentication failed: {response.status_code} - {response.text}")

    async def test_get_user_sessions(self, user_id: str = "-1") -> Dict[str, Any]:
        """
        测试获取用户会话列表 API

        Args:
            user_id: 用户ID，默认使用 "-1"

        Returns:
            Dict: 包含测试结果的字典
        """
        print(f"\n🚀 测试获取用户会话列表 API - user_id: {user_id}")

        async with httpx.AsyncClient(timeout=httpx.Timeout(30.0)) as client:
            response = await client.get(f"{self.base_url}/api/v1/chat/sessions", params={"user_id": user_id}, headers=self.headers)

            print(f"📊 响应状态码: {response.status_code}")
            print(f"📋 响应头: {dict(response.headers)}")

            if response.status_code == 200:
                sessions_data = response.json()
                total_records = sessions_data.get("total", 0)
                sessions_data = sessions_data.get("sessions", [])
                print(f"✅ 成功获取会话列表，数量: {total_records}")

                # 打印前3个会话的详细信息
                for i, session in enumerate(sessions_data[:3]):
                    print(f"📝 会话 {i+1}: {session}")

                return {
                    "success": True,    
                    "status_code": response.status_code,
                    "sessions_count": total_records,
                    "sessions_data": sessions_data,
                    "response_headers": dict(response.headers),
                }
            else:
                error_text = response.text
                print(f"❌ 请求失败: {response.status_code} - {error_text}")
                return {"success": False, "status_code": response.status_code, "error": error_text, "response_headers": dict(response.headers)}

    async def test_get_session_messages(self, session_id: str) -> Dict[str, Any]:
        """
        测试获取会话消息列表 API

        Args:
            session_id: 会话ID

        Returns:
            Dict: 包含测试结果的字典
        """
        print(f"\n🚀 测试获取会话消息列表 API - session_id: {session_id}")

        async with httpx.AsyncClient(timeout=httpx.Timeout(30.0)) as client:
            response = await client.get(f"{self.base_url}/api/v1/chat/sessions/{session_id}/messages", headers=self.headers)

            print(f"📊 响应状态码: {response.status_code}")
            print(f"📋 响应头: {dict(response.headers)}")

            if response.status_code == 200:
                messages_data = response.json()
                print(f"✅ 成功获取消息列表，数量: {len(messages_data)}")

                # 打印前3条消息的详细信息
                for i, message in enumerate(messages_data[:3]):
                    print(f"💬 消息 {i+1}: {message}")

                return {
                    "success": True,
                    "status_code": response.status_code,
                    "messages_count": len(messages_data),
                    "messages_data": messages_data,
                    "response_headers": dict(response.headers),
                }
            else:
                error_text = response.text
                print(f"❌ 请求失败: {response.status_code} - {error_text}")
                return {"success": False, "status_code": response.status_code, "error": error_text, "response_headers": dict(response.headers)}


@pytest.mark.asyncio
@pytest.mark.integration
async def test_chat_sessions_api_e2e():
    """
    Chat Sessions API 端到端测试（需要服务器运行）

    运行此测试前，请确保：
    1. 启动后端服务器：python src/run.py
    2. 服务器运行在 http://localhost:8000
    """
    print("\n" + "=" * 80)
    print("开始 Chat Sessions API 端到端测试")
    print("=" * 80)

    # 初始化测试器
    tester = ChatSessionsAPITester()

    print("🔐 正在进行身份认证...")
    try:
        token = await tester.authenticate()
        tester.token = token
        tester.headers["Authorization"] = f"Bearer {token}"
        print("✅ 身份认证成功")
    except Exception as e:
        pytest.skip(f"认证失败，跳过测试: {e}")

    # 测试1: 获取用户会话列表
    print("\n" + "=" * 60)
    print("测试 1: 获取用户会话列表")
    print("=" * 60)

    try:
        sessions_result = await tester.test_get_user_sessions(user_id="149789")

        # 验证基本结果
        assert sessions_result["status_code"] in [200, 404], f"期望状态码 200 或 404，实际: {sessions_result['status_code']}"

        if sessions_result["success"]:
            assert isinstance(sessions_result["sessions_data"], list), "会话数据应该是列表格式"
            print(f"✅ 会话列表测试通过，获取到 {sessions_result['sessions_count']} 个会话")

            # 如果有会话数据，测试第一个会话的消息
            if sessions_result["sessions_count"] > 0:
                first_session = sessions_result["sessions_data"][0]
                session_id = first_session.get("session_id")

                if session_id:
                    print("\n" + "=" * 60)
                    print("测试 2: 获取会话消息列表")
                    print("=" * 60)

                    messages_result = await tester.test_get_session_messages(session_id)

                    # 验证消息结果
                    assert messages_result["status_code"] in [200, 404], f"期望状态码 200 或 404，实际: {messages_result['status_code']}"

                    if messages_result["success"]:
                        assert isinstance(messages_result["messages_data"], list), "消息数据应该是列表格式"
                        print(f"✅ 消息列表测试通过，获取到 {messages_result['messages_count']} 条消息")
                    else:
                        print(f"⚠️ 消息列表请求失败: {messages_result['error']}")
                else:
                    print("⚠️ 第一个会话没有 session_id，跳过消息测试")
            else:
                print("⚠️ 没有会话数据，跳过消息测试")

                # 测试一个不存在的会话ID
                print("\n" + "=" * 60)
                print("测试 2: 获取不存在会话的消息列表")
                print("=" * 60)

                fake_session_id = "non-existent-session-id"
                messages_result = await tester.test_get_session_messages(fake_session_id)

                # 验证错误响应
                assert messages_result["status_code"] in [404, 400], f"期望状态码 404 或 400，实际: {messages_result['status_code']}"
                print(f"✅ 不存在会话的错误处理测试通过")
        else:
            print(f"⚠️ 会话列表请求失败: {sessions_result['error']}")
            # 即使会话列表失败，也测试消息接口的错误处理
            print("\n" + "=" * 60)
            print("测试 2: 获取不存在会话的消息列表")
            print("=" * 60)

            fake_session_id = "non-existent-session-id"
            messages_result = await tester.test_get_session_messages(fake_session_id)

            # 验证错误响应
            assert messages_result["status_code"] in [404, 400], f"期望状态码 404 或 400，实际: {messages_result['status_code']}"
            print(f"✅ 不存在会话的错误处理测试通过")

        print("\n🎉 Chat Sessions API 测试完成！")
        print("=" * 80)

    except Exception as e:
        pytest.skip(f"服务器连接失败，跳过测试: {e}")


if __name__ == "__main__":
    """
    直接运行测试

    运行方式：
    python tests/integration/api/test_chat_sessions_integration.py

    或使用 pytest：
    pytest tests/integration/api/test_chat_sessions_integration.py -v -s
    """
    print("运行 Chat Sessions API 集成测试...")

    # 使用 pytest 运行测试
    exit_code = pytest.main([__file__ + "::test_chat_sessions_api_e2e", "-v", "-s", "--tb=short"])

    sys.exit(exit_code)
