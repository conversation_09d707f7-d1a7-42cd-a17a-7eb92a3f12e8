#!/usr/bin/env python3
"""
运行所有 Agent 的 Chat V1 API 集成测试

这个脚本会依次运行：
1. ReasoningAgent 测试
2. InteractiveAgent 测试  
3. InspectAgent 测试

运行前请确保：
1. 启动后端服务器：python src/run.py
2. 服务器运行在 http://localhost:8000
"""

import subprocess
import sys
import os
from pathlib import Path

def run_test_file(test_file: str, description: str) -> bool:
    """运行单个测试文件"""
    print(f"\n{'='*80}")
    print(f"🚀 开始运行 {description}")
    print(f"📁 测试文件: {test_file}")
    print(f"{'='*80}")
    
    try:
        # 使用 pytest 运行测试
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            test_file, 
            "-v", "-s", "--tb=short"
        ], check=True, capture_output=False)
        
        print(f"✅ {description} 测试通过！")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 测试失败！返回码: {e.returncode}")
        return False
    except Exception as e:
        print(f"❌ {description} 测试出现异常: {e}")
        return False

def main():
    """主函数"""
    print("🎯 Chat V1 API 集成测试套件")
    print("=" * 80)
    
    # 获取当前脚本所在目录
    current_dir = Path(__file__).parent
    
    # 定义测试文件列表
    test_files = [
        {
            "file": current_dir / "test_reasoning_agent.py",
            "description": "ReasoningAgent 测试"
        },
        {
            "file": current_dir / "test_interactive_agent.py", 
            "description": "InteractiveAgent 测试"
        },
        {
            "file": current_dir / "test_inspect_agent.py",
            "description": "InspectAgent 测试"
        }
    ]
    
    # 检查测试文件是否存在
    missing_files = []
    for test_info in test_files:
        if not test_info["file"].exists():
            missing_files.append(str(test_info["file"]))
    
    if missing_files:
        print(f"❌ 以下测试文件不存在:")
        for file in missing_files:
            print(f"   - {file}")
        sys.exit(1)
    
    # 运行测试
    results = []
    for test_info in test_files:
        success = run_test_file(str(test_info["file"]), test_info["description"])
        results.append({
            "description": test_info["description"],
            "success": success
        })
    
    # 输出总结
    print(f"\n{'='*80}")
    print("📊 测试结果总结")
    print(f"{'='*80}")
    
    passed = 0
    failed = 0
    
    for result in results:
        status = "✅ 通过" if result["success"] else "❌ 失败"
        print(f"{status} - {result['description']}")
        if result["success"]:
            passed += 1
        else:
            failed += 1
    
    print(f"\n📈 总计: {passed} 个通过, {failed} 个失败")
    
    if failed > 0:
        print("❌ 部分测试失败，请检查上面的错误信息")
        sys.exit(1)
    else:
        print("🎉 所有测试都通过了！")
        sys.exit(0)

if __name__ == "__main__":
    main()
