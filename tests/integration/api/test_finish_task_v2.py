import requests
import json
import time

endpoint = "http://localhost:8000"
auth_api = "/api/token"
create_task_api = "/api/v1/tasks"

# 认证
auth_body = {
    "access_key": "admin",
    "secret_key": "admin",
    "token_lifetime_minutes": 60
}
headers = {"Content-Type": "application/json"}
token_response = requests.post(endpoint + auth_api, json=auth_body, headers=headers)
authorization = f"Bearer {json.loads(token_response.content).get('access_token')}"
headers = {"Authorization": authorization, "Content-Type": "application/json"}

# 创建新任务
task_body = {
    "agent": "ecs_diagnostic_agent",
    "question": "这些实例在2025年6月26日凌晨1-4点发生了不可用，请排查问题的原因。（i-t4n4vky24zw2w1qnqoyf, i-t4n74bsfzx58x0lj4qbh, i-t4na3cc0c9mimcw9667x, i-j6ch2zf4qfy1rltbql6r, i-2vc5alcmxz75rw8aol4g, i-2vc0zliaw8ilg744cdrq, i-2vc6qv34j96hkmcwms5d"
}

print("📝 正在创建新任务...")
create_response = requests.post(endpoint + create_task_api, json=task_body, headers=headers)
if create_response.status_code != 202:
    print(f"❌ 任务创建失败: {create_response.status_code}")
    print(f"响应: {create_response.text}")
    exit(1)

task_data = create_response.json()
task_id = task_data.get("task_id")
print(f"✅ 任务创建成功: {task_id}")

# 等待任务完成
get_task_api = f"/api/v1/tasks/{task_id}"
max_wait_time = 300  # 5分钟
check_interval = 10  # 10秒检查一次

print("⏳ 等待任务完成...")
start_time = time.time()

while time.time() - start_time < max_wait_time:
    try:
        response = requests.get(endpoint + get_task_api, headers=headers)
        if response.status_code == 200:
            data = response.json()
            status = data.get("status")
            print(f"   任务状态: {status}")
            
            if status in ["SUCCESS", "FAILED"]:
                print(f"🎯 任务完成，状态: {status}")
                
                # 检查URLs
                task_data = data.get("data", {})
                detail_str = task_data.get("detail", "{}")
                detail = data.get("data", {}).get("detail", "{}")
                if not isinstance(detail, str):
                    detail = "{}"
                try:
                    detail = json.loads(detail_str)
                    urls = detail.get("urls", [])
                    
                    print(f"\n📊 任务结果:")
                    print(f"  - 任务ID: {data.get('task_id')}")
                    print(f"  - 状态: {status}")
                    print(f"  - URLs数量: {len(urls)}")
                    
                    if urls:
                        print(f"✅ 找到 {len(urls)} 个URL:")
                        for i, url in enumerate(urls):
                            print(f"  {i+1}. 名称: {url.get('name')}")
                            print(f"     URL: {url.get('url')}")
                        print("\n🎉 修复成功！URLs已正确生成")
                    else:
                        print("❌ 没有找到URLs")
                        print("Detail结构:")
                        for key, value in detail.items():
                            if key == "urls":
                                print(f"  - {key}: {value}")
                            else:
                                print(f"  - {key}: {type(value)}")
                        print("\n⚠️ 修复可能未生效，或HTML生成失败")
                        
                except json.JSONDecodeError as e:
                    print(f"❌ Detail JSON解析失败: {e}")
                
                # 打印完整响应（可选）
                print(f"\n📄 完整响应:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                break
                
        else:
            print(f"❌ 查询任务状态失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 查询任务时出错: {e}")
    
    time.sleep(check_interval)
else:
    print("⏰ 任务等待超时")
