"""
Unit tests for DashScopeEmbeddingService - testing get_embedding method without mockup.
"""
import pytest
import numpy as np
from deep_diagnose.core.reasoning.planning.strategy.embedding_service import DashScopeEmbeddingService, get_embedding_service


class TestDashScopeEmbeddingService:
    """Test DashScopeEmbeddingService get_embedding method."""
    
    def setup_method(self):
        """Setup embedding service."""
        self.service = get_embedding_service()

    @pytest.mark.asyncio
    async def test_get_embedding_output(self):
        """Test get_embedding method and print output content (no mockup)."""
        test_text = "ECS实例网络连接异常，需要检查网络配置"
        
        try:
            # 调用get_embedding方法
            embedding = await self.service.get_embedding(test_text)
            
            # 验证返回结果
            assert isinstance(embedding, np.ndarray)
            assert len(embedding) > 0
            
            # 打印详细的embedding输出信息
            print(f"\n=== Embedding Service 测试结果 ===")
            print(f"输入文本: {test_text}")
            print(f"模型名称: {self.service.model_name}")
            print(f"向量维度: {self.service.dimension}")
            print(f"实际输出维度: {len(embedding)}")
            print(f"向量类型: {type(embedding)}")
            print(f"向量数据类型: {embedding.dtype}")
            
            # 打印前10个和后10个向量值
            print(f"\n=== Embedding向量内容 ===")
            print(f"前10个值: {embedding[:10]}")
            print(f"后10个值: {embedding[-10:]}")
            print(f"向量范数: {np.linalg.norm(embedding)}")
            print(f"最大值: {np.max(embedding)}")
            print(f"最小值: {np.min(embedding)}")
            print(f"均值: {np.mean(embedding)}")
            print(f"标准差: {np.std(embedding)}")
            
        except Exception as e:
            # 如果API调用失败，打印错误信息但不让测试失败
            print(f"\n=== Embedding Service 错误信息 ===")
            print(f"输入文本: {test_text}")
            print(f"错误类型: {type(e).__name__}")
            print(f"错误信息: {str(e)}")
            print(f"模型配置: {self.service.model_name}")
            
            # 对于API相关错误，我们仍然认为测试通过（因为这可能是网络或配置问题）
            if "API" in str(e) or "DashScope" in str(e) or "api_key" in str(e).lower():
                print("注意: 这是API配置相关错误，不是代码逻辑错误")
                pytest.skip(f"跳过测试，API配置问题: {str(e)}")
            else:
                # 对于其他错误，让测试失败
                raise e