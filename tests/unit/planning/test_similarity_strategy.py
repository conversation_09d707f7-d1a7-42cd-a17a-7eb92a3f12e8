"""
Unit tests for SimilaritySelectionStrategy - testing select_sop method with full integration (no mockup).
"""
import pytest
from deep_diagnose.core.reasoning.planning.strategy.similarity import SimilaritySelectionStrategy
from deep_diagnose.core.reasoning.planning.strategy.embedding_service import get_embedding_service
from deep_diagnose.core.reasoning.planning import create_sop_service


class TestSimilaritySelectionStrategy:
    """Test SimilaritySelectionStrategy select_sop method with full integration."""
    
    def setup_method(self):
        """Setup similarity strategy and related services."""
        self.embedding_service = get_embedding_service()
        self.similarity_strategy = SimilaritySelectionStrategy(
            embedding_service=self.embedding_service,
            similarity_threshold=0.6,
            semantic_weight=0.7,
            example_weight=0.3
        )
        self.sop_service = create_sop_service()

    def _print_sop_selection_result(self, query, result, sops_count):
        """Print detailed SOP selection result."""
        print(f"\n=== SOP选择结果详情 ===")
        print(f"查询内容: {query}")
        print(f"可选SOP数量: {sops_count}")
        print(f"选择成功: {result.success}")
        print(f"选择理由: {result.reason}")
        print(f"匹配分数: {result.score}")
        
        if result.error_message:
            print(f"错误信息: {result.error_message}")
            
        if result.sop:
            print(f"\n=== 选中的SOP信息 ===")
            print(f"SOP ID: {result.sop.sop_id}")
            print(f"SOP名称: {result.sop.name}")
            print(f"适用场景: {result.sop.scenario}")
            print(f"示例查询: {result.sop.example_user_queries}")
            print(f"工具依赖: {result.sop.tool_dependencies}")
            print(f"模板路径: {result.sop.template_file_path}")
            
            if result.sop.content:
                content_preview = result.sop.content[:300] + "..." if len(result.sop.content) > 300 else result.sop.content
                print(f"内容预览: {content_preview}")
        else:
            print("未选择任何SOP")

    @pytest.mark.asyncio
    async def test_select_sop_network_connectivity(self):
        """Test select_sop with network connectivity query (full integration)."""
        query = "ECS实例网络连接异常，无法SSH登录"
        
        try:
            # 获取所有可用的SOP
            all_sops = await self.sop_service.get_all_sops()
            assert len(all_sops) > 0, "系统中应该有可用的SOP"
            
            # 使用similarity策略选择SOP
            result = await self.similarity_strategy.select_sop(query, all_sops)
            
            # 验证返回结果的结构
            assert hasattr(result, 'success')
            assert hasattr(result, 'sop')
            assert hasattr(result, 'reason')
            assert hasattr(result, 'score')
            
            # 打印详细结果
            self._print_sop_selection_result(query, result, len(all_sops))
            
        except Exception as e:
            print(f"\n=== 测试异常信息 ===")
            print(f"查询内容: {query}")
            print(f"异常类型: {type(e).__name__}")
            print(f"异常信息: {str(e)}")
            
            # 对于API相关错误，跳过测试
            if "API" in str(e) or "DashScope" in str(e) or "api_key" in str(e).lower():
                print("注意: 这是API配置相关错误，跳过测试")
                pytest.skip(f"跳过测试，API配置问题: {str(e)}")
            else:
                raise e

    @pytest.mark.asyncio
    async def test_select_sop_vmcore_analysis(self):
        """Test select_sop with vmcore analysis query (full integration)."""
        query = "利用vmcore分析这个NC ************* 在20250724的04:00:00宕机原因"
        
        try:
            # 获取所有可用的SOP
            all_sops = await self.sop_service.get_all_sops()
            assert len(all_sops) > 0, "系统中应该有可用的SOP"
            
            # 使用similarity策略选择SOP
            result = await self.similarity_strategy.select_sop(query, all_sops)
            
            # 验证返回结果的结构
            assert hasattr(result, 'success')
            assert hasattr(result, 'sop')
            assert hasattr(result, 'reason')
            assert hasattr(result, 'score')
            
            # 打印详细结果
            self._print_sop_selection_result(query, result, len(all_sops))
            
        except Exception as e:
            print(f"\n=== 测试异常信息 ===")
            print(f"查询内容: {query}")
            print(f"异常类型: {type(e).__name__}")
            print(f"异常信息: {str(e)}")
            
            # 对于API相关错误，跳过测试
            if "API" in str(e) or "DashScope" in str(e) or "api_key" in str(e).lower():
                print("注意: 这是API配置相关错误，跳过测试")
                pytest.skip(f"跳过测试，API配置问题: {str(e)}")
            else:
                raise e

    @pytest.mark.asyncio
    async def test_select_sop_batch_instances_issue(self):
        """Test select_sop with batch instances issue query (full integration)."""
        query = "实例i-xxx、i-yyy、i-zzz、i-aaa同时不可用"
        
        try:
            # 获取所有可用的SOP
            all_sops = await self.sop_service.get_all_sops()
            assert len(all_sops) > 0, "系统中应该有可用的SOP"
            
            # 使用similarity策略选择SOP
            result = await self.similarity_strategy.select_sop(query, all_sops)
            
            # 验证返回结果的结构
            assert hasattr(result, 'success')
            assert hasattr(result, 'sop')
            assert hasattr(result, 'reason')
            assert hasattr(result, 'score')
            
            # 打印详细结果
            self._print_sop_selection_result(query, result, len(all_sops))
            
        except Exception as e:
            print(f"\n=== 测试异常信息 ===")
            print(f"查询内容: {query}")
            print(f"异常类型: {type(e).__name__}")
            print(f"异常信息: {str(e)}")
            
            # 对于API相关错误，跳过测试
            if "API" in str(e) or "DashScope" in str(e) or "api_key" in str(e).lower():
                print("注意: 这是API配置相关错误，跳过测试")
                pytest.skip(f"跳过测试，API配置问题: {str(e)}")
            else:
                raise e

    @pytest.mark.asyncio
    async def test_select_sop_empty_query(self):
        """Test select_sop with empty query (edge case)."""
        query = ""
        
        try:
            # 获取所有可用的SOP
            all_sops = await self.sop_service.get_all_sops()
            
            # 使用similarity策略选择SOP（空查询）
            result = await self.similarity_strategy.select_sop(query, all_sops)
            
            # 验证返回结果的结构
            assert hasattr(result, 'success')
            assert hasattr(result, 'sop')
            assert hasattr(result, 'reason')
            
            # 打印详细结果
            self._print_sop_selection_result(query, result, len(all_sops))
            
        except Exception as e:
            print(f"\n=== 测试异常信息 ===")
            print(f"查询内容: '{query}'")
            print(f"异常类型: {type(e).__name__}")
            print(f"异常信息: {str(e)}")
            
            # 对于API相关错误，跳过测试
            if "API" in str(e) or "DashScope" in str(e) or "api_key" in str(e).lower():
                print("注意: 这是API配置相关错误，跳过测试")
                pytest.skip(f"跳过测试，API配置问题: {str(e)}")
            else:
                raise e

    @pytest.mark.asyncio
    async def test_select_sop_no_sops_available(self):
        """Test select_sop with no SOPs available (edge case)."""
        query = "测试查询"
        empty_sops = []
        
        # 使用similarity策略选择SOP（空SOP列表）
        result = await self.similarity_strategy.select_sop(query, empty_sops)
        
        # 验证返回结果
        assert result.success is False
        assert result.sop is None
        assert "No SOPs available" in result.reason
        
        # 打印详细结果
        self._print_sop_selection_result(query, result, len(empty_sops))