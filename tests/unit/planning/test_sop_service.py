"""
Unit tests for SOPService - testing find_sop and get_all_sops methods.
"""
import pytest
from deep_diagnose.core.reasoning.planning import create_sop_service


class TestSOPService:
    """Test SOPService core methods."""
    
    def setup_method(self):
        """Setup SOPService."""
        self.service = create_sop_service()

    def _print_sop_info(self, sop, query=""):
        """Print SOP详细信息."""
        if sop:
            print(f"\n=== SOP信息 ===\n查询: {query}\nSOP ID: {sop.sop_id}\nSOP名称: {sop.name}\n适用场景: {sop.scenario}\n示例查询: {sop.example_user_queries}\n工具依赖: {sop.tool_dependencies}\n模板路径: {sop.template_file_path}")
            if sop.content:
                print(f"\n=== SOP内容预览 ===\n{sop.content[:500]}..." if len(sop.content) > 500 else f"\n=== SOP内容 ===\n{sop.content}")
        else:
            print(f"\n=== 未找到SOP ===\n查询: {query}")

    @pytest.mark.asyncio
    async def test_get_all_sops(self):
        """Test get_all_sops method and print all SOPs."""
        result = await self.service.get_all_sops()
        assert isinstance(result, list)
        
        print(f"\n=== 系统中共有 {len(result)} 个SOP ===")
        for i, sop in enumerate(result, 1):
            print(f"\n--- SOP {i} ---")
            self._print_sop_info(sop)

    @pytest.mark.asyncio
    async def test_find_sop_network_issue(self):
        """Test find_sop with network connectivity query."""
        query = "网络连接问题"
        result = await self.service.find_sop(query)
        assert hasattr(result, 'success')
        assert hasattr(result, 'sop')
        assert hasattr(result, 'reason')
        
        print(f"\n=== 网络问题查询结果 ===")
        print(f"查询成功: {result.success}")
        print(f"选择理由: {result.reason}")
        if hasattr(result, 'score') and result.score:
            print(f"匹配分数: {result.score}")
        self._print_sop_info(result.sop, query)

    @pytest.mark.asyncio
    async def test_find_sop_vmcore_analysis(self):
        """Test find_sop with vmcore analysis query."""
        query = "利用vmcore分析这个NC 26.52.185.141 在20250724的04:00:00宕机原因"
        result = await self.service.find_sop(query)
        assert hasattr(result, 'success')
        assert hasattr(result, 'sop')
        assert hasattr(result, 'reason')
        
        print(f"\n=== VMCore分析查询结果 ===")
        print(f"查询成功: {result.success}")
        print(f"选择理由: {result.reason}")
        if hasattr(result, 'score') and result.score:
            print(f"匹配分数: {result.score}")
        self._print_sop_info(result.sop, query)

    @pytest.mark.asyncio
    async def test_find_sop_exception_events_query(self):
        """Test find_sop with exception events and visualization query."""
        query = "查询这个NC的 26.52.185.141 最近一个月的异常事件并且画出对应图"
        result = await self.service.find_sop(query)
        assert hasattr(result, 'success')
        assert hasattr(result, 'sop')
        assert hasattr(result, 'reason')
        
        print(f"\n=== 异常事件查询结果 ===")
        print(f"查询成功: {result.success}")
        print(f"选择理由: {result.reason}")
        if hasattr(result, 'score') and result.score:
            print(f"匹配分数: {result.score}")
        self._print_sop_info(result.sop, query)

    @pytest.mark.asyncio
    async def test_find_sop_batch_instance_unavailable(self):
        """Test find_sop with batch instance unavailable query."""
        query = "实例i-xxx、i-yyy、i-zzz、i-aaa同时不可用"
        result = await self.service.find_sop(query)
        assert hasattr(result, 'success')
        assert hasattr(result, 'sop')
        assert hasattr(result, 'reason')
        
        print(f"\n=== 批量实例不可用查询结果 ===")
        print(f"查询成功: {result.success}")
        print(f"选择理由: {result.reason}")
        if hasattr(result, 'score') and result.score:
            print(f"匹配分数: {result.score}")
        self._print_sop_info(result.sop, query)

    @pytest.mark.asyncio
    async def test_find_sop_customer_stability_check(self):
        """Test find_sop with customer stability check query."""
        query = "为客户生成健康度检查报告"
        result = await self.service.find_sop(query)
        assert hasattr(result, 'success')
        assert hasattr(result, 'sop')
        assert hasattr(result, 'reason')
        
        print(f"\n=== 客户健康度检查查询结果 ===")
        print(f"查询成功: {result.success}")
        print(f"选择理由: {result.reason}")
        if hasattr(result, 'score') and result.score:
            print(f"匹配分数: {result.score}")
        self._print_sop_info(result.sop, query)

    @pytest.mark.asyncio
    async def test_find_sop_empty_query(self):
        """Test find_sop with empty query."""
        result = await self.service.find_sop("")
        assert result.success is False
        assert result.sop is None
        assert "Query is empty" in result.reason
        
        print(f"\n=== 空查询测试结果 ===")
        print(f"查询成功: {result.success}")
        print(f"错误信息: {result.error_message}")
        print(f"选择理由: {result.reason}")

    @pytest.mark.asyncio
    async def test_find_sop_nc_execption_query(self):
        """Test find_sop with empty query."""
        result = await self.service.find_sop("分析这个NC NC 26.52.185.141 的最近一个星期的异常信息列表，并且画出对应分析图表")
        assert hasattr(result, 'success')
        assert hasattr(result, 'sop')
        assert hasattr(result, 'reason')

        print(f"\n=== 空查询测试结果 ===")
        print(f"查询成功: {result.success}")
        print(f"错误信息: {result.error_message}")
        print(f"选择理由: {result.reason}")
