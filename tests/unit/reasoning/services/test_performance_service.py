
import unittest
from typing import Dict, Any, List

from deep_diagnose.core.reasoning.services.performance_service import MetricsAnalyzer, PerformanceService


class TestMetricsAnalyzer(unittest.TestCase):

    def setUp(self):
        self.analyzer = MetricsAnalyzer()
        self.config = {
            'thresholds': {
                'high_is_bad': {'warning': 80, 'critical': 95},
                'low_is_bad': {'warning': 20, 'critical': 5}
            }
        }

    def test_calculate_trend(self):
        self.assertEqual(self.analyzer._calculate_trend([10, 10, 10, 10, 10]), "stable")
        self.assertEqual(self.analyzer._calculate_trend([10, 20, 30, 40, 50]), "increasing")
        self.assertEqual(self.analyzer._calculate_trend([50, 40, 30, 20, 10]), "decreasing")
        self.assertEqual(self.analyzer._calculate_trend([10, 11, 10, 11, 10]), "stable")
        self.assertEqual(self.analyzer._calculate_trend([10]), "stable")
        self.assertEqual(self.analyzer._calculate_trend([]), "stable")

    def test_get_status(self):
        # Test high_is_bad metric
        self.assertEqual(self.analyzer._get_status('high_is_bad', 70, self.config)['level'], "normal")
        self.assertEqual(self.analyzer._get_status('high_is_bad', 85, self.config)['level'], "warning")
        self.assertEqual(self.analyzer._get_status('high_is_bad', 98, self.config)['level'], "critical")

        # Test low_is_bad metric
        self.assertEqual(self.analyzer._get_status('low_is_bad', 30, self.config)['level'], "normal")
        self.assertEqual(self.analyzer._get_status('low_is_bad', 15, self.config)['level'], "warning")
        self.assertEqual(self.analyzer._get_status('low_is_bad', 4, self.config)['level'], "critical")

        # Test unknown metric
        self.assertEqual(self.analyzer._get_status('unknown_metric', 100, self.config)['level'], "normal")

    def test_analyze_metrics(self):
        data = {
            'instance-1': {
                'high_is_bad': [
                    {'datetime': '2024-01-01 00:00:00', 'value': 90},
                    {'datetime': '2024-01-01 00:01:00', 'value': 100}
                ],
                'low_is_bad': [
                    {'datetime': '2024-01-01 00:00:00', 'value': 10},
                    {'datetime': '2024-01-01 00:01:00', 'value': 5}
                ]
            }
        }
        analysis = self.analyzer.analyze_metrics(data, self.config)
        
        instance_analysis = analysis.get('instance-1', {})
        
        # Check high_is_bad analysis
        self.assertIn('high_is_bad', instance_analysis)
        self.assertEqual(instance_analysis['high_is_bad']['max'], 100)
        self.assertEqual(instance_analysis['high_is_bad']['avg'], 95)
        self.assertEqual(instance_analysis['high_is_bad']['status']['level'], 'critical')
        
        # Check low_is_bad analysis
        self.assertIn('low_is_bad', instance_analysis)
        self.assertEqual(instance_analysis['low_is_bad']['min'], 5)
        self.assertEqual(instance_analysis['low_is_bad']['avg'], 7.5)
        self.assertEqual(instance_analysis['low_is_bad']['status']['level'], 'critical')


class TestPerformanceService(unittest.TestCase):

    def setUp(self):
        # We instantiate the service directly to test its internal methods
        # We can pass dummy components since we are not calling generate_report
        self.service = PerformanceService(use_enhanced_ui=False)

    def test_filter_charts_with_data(self):
        charts_config = {
            'chart_with_data': {
                'title': 'Chart With Data',
                'metrics': ['metric1', 'metric2']
            },
            'chart_without_data': {
                'title': 'Chart Without Data',
                'metrics': ['metric3']
            },
            'chart_with_partial_data': {
                'title': 'Chart With Partial Data',
                'metrics': ['metric1', 'metric4']
            },
            'chart_with_empty_data': {
                'title': 'Chart With Empty Data',
                'metrics': ['metric5']
            }
        }
        
        data = {
            'instance-1': {
                'metric1': [{'datetime': '2024-01-01 00:00:00', 'value': 10}],
                'metric2': [{'datetime': '2024-01-01 00:00:00', 'value': 20}],
                'metric5': []
            }
        }

        filtered_charts = self.service._filter_charts_with_data(data, charts_config)

        self.assertIn('chart_with_data', filtered_charts)
        self.assertIn('chart_with_partial_data', filtered_charts)
        self.assertNotIn('chart_without_data', filtered_charts)
        self.assertNotIn('chart_with_empty_data', filtered_charts)
        self.assertEqual(len(filtered_charts), 2)


if __name__ == '__main__':
    unittest.main()
