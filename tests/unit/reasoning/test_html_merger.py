"""
测试HTML合并器功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../src'))

from deep_diagnose.core.reasoning.agents.html_merger import HtmlReportMergerAgent, HtmlUtils


class TestHTMLMerger:
    """测试HTML合并器功能"""
    
    def test_html_utils_clean_content(self):
        """测试HTML内容清理功能"""
        # 测试正常内容
        content = "  <div>测试内容</div>  "
        cleaned = HtmlUtils.clean_content(content)
        assert cleaned == "<div>测试内容</div>"
        
        # 测试空内容
        empty_cleaned = HtmlUtils.clean_content("")
        assert empty_cleaned == "<div>内容缺失</div>"
        
        # 测试代码块清理
        code_content = "```html\n<div>测试</div>\n```"
        code_cleaned = HtmlUtils.clean_content(code_content)
        assert code_cleaned == "<div>测试</div>"


if __name__ == "__main__":
    test = TestHTMLMerger()
    test.test_html_utils_clean_content()
    print("✅ HTML合并器测试通过")