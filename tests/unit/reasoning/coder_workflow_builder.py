"""
简化的Coder Agent Workflow Builder
只包含一个coder节点的workflow，用于专门的代码分析和处理任务
"""

from typing import Literal, Optional

from langchain_core.runnables import RunnableConfig
from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import StateGraph, START, END
from langgraph.types import Command

from deep_diagnose.core.reasoning.agents.coder import CoderAgent
from deep_diagnose.core.reasoning.workflow.types import ReasoningState

# 创建独立的coder实例
_standalone_coder = CoderAgent()


async def standalone_coder_node(state: ReasoningState, config: RunnableConfig) -> Command[Literal["coder", "__end__"]]:
    """独立编码员节点函数 - 用于单节点workflow"""
    return await _standalone_coder.execute(state, config)


def _build_coder_graph():
    """构建并返回只包含coder agent的简化状态图"""
    builder = StateGraph(ReasoningState)
    
    # 添加开始边：从START直接到coder
    builder.add_edge(START, "coder")
    
    # 添加coder节点（使用独立版本）
    builder.add_node("coder", standalone_coder_node)
    
    # coder节点会自动处理跳转逻辑（继续或结束）
    
    return builder


def build_coder_graph_with_memory():
    """构建并返回带内存的coder agent workflow图"""
    # 使用持久内存保存对话历史
    memory = MemorySaver()
    
    # 构建状态图
    builder = _build_coder_graph()
    return builder.compile(checkpointer=memory)


def build_coder_graph():
    """构建并返回不带内存的coder agent workflow图"""
    # 构建状态图
    builder = _build_coder_graph()
    return builder.compile()


# 创建默认的coder workflow实例
coder_graph = build_coder_graph()
coder_graph_with_memory = build_coder_graph_with_memory()