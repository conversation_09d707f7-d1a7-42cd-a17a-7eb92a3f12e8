import warnings
import sys
import shutil
from datetime import datetime
from pathlib import Path

import numpy as np
import pandas as pd

warnings.filterwarnings('ignore')

def main(keep_output=False):
    try:
        # 创建输出目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = Path(f"analysis_results_{timestamp}")
        output_dir.mkdir(exist_ok=True)
        charts_dir = output_dir / "charts"
        charts_dir.mkdir(exist_ok=True)
        data_dir = output_dir / "data"
        data_dir.mkdir(exist_ok=True)

        print(f"📁 创建输出目录: {output_dir}")

        # 生成示例年龄数据
        np.random.seed(42)
        # 创建符合现实分布的年龄数据（正态分布偏移）
        ages = np.concatenate([
            np.random.normal(25, 8, 500),  # 年轻人较多
            np.random.normal(45, 10, 300), # 中年人
            np.random.normal(65, 7, 100),  # 老年人较少
        ])
        ages = np.clip(ages, 1, 100)  # 限制在1-100岁之间
        ages = ages.astype(int)  # 转换为整数

        # 创建DataFrame
        df = pd.DataFrame({'age': ages})
        print(f"📊 成功生成年龄数据，共 {len(df)} 条记录")

        # 保存原始数据
        data_path = data_dir / "age_data.csv"
        df.to_csv(data_path, index=False)
        print(f"💾 原始数据已保存至: {data_path}")
        
        # 创建一个sample_data.csv文件在根目录（用于测试）
        sample_data_path = Path("sample_data.csv")
        df.to_csv(sample_data_path, index=False)
        print(f"💾 样本数据已保存至: {sample_data_path}")

        # 如果不保留输出，则在程序结束时清理
        if not keep_output:
            print("程序执行完毕，正在清理生成的文件...")
            # 清理根目录的sample_data.csv
            if sample_data_path.exists():
                sample_data_path.unlink()
                print(f"🗑️  已删除: {sample_data_path}")
            
            # 清理整个输出目录
            if output_dir.exists():
                shutil.rmtree(output_dir)
                print(f"🗑️  已删除目录: {output_dir}")
        
        print("✅ 程序执行完成")
        return True
        
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        return False

if __name__ == "__main__":
    # 检查命令行参数
    keep_output = "--keep" in sys.argv
    success = main(keep_output)
    sys.exit(0 if success else 1)