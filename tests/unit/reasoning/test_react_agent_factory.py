"""
测试React智能体工厂功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../src'))

from deep_diagnose.core.reasoning.agents.react_agent_factory import ToolResultAnalyzer


class TestReactAgentFactory:
    """测试React智能体工厂功能"""
    
    def test_tool_result_analyzer_is_truly_empty(self):
        """测试工具结果分析器的空值判断功能"""
        # 测试空值
        assert ToolResultAnalyzer.is_truly_empty(None) == True
        assert ToolResultAnalyzer.is_truly_empty("") == True
        assert ToolResultAnalyzer.is_truly_empty("   ") == True
        assert ToolResultAnalyzer.is_truly_empty([]) == True
        assert ToolResultAnalyzer.is_truly_empty({}) == True
        
        # 测试非空值
        assert ToolResultAnalyzer.is_truly_empty("test") == False
        assert ToolResultAnalyzer.is_truly_empty([1, 2]) == False
        assert ToolResultAnalyzer.is_truly_empty({"key": "value"}) == False
        assert ToolResultAnalyzer.is_truly_empty(0) == False
        assert ToolResultAnalyzer.is_truly_empty(False) == False


if __name__ == "__main__":
    test = TestReactAgentFactory()
    test.test_tool_result_analyzer_is_truly_empty()
    print("✅ React智能体工厂测试通过")