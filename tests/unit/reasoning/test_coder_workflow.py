"""
Coder Workflow单元测试
测试只包含coder agent的简化workflow
"""

import sys
import os
import pytest
import asyncio
from langchain_core.messages import HumanMessage
from langchain_core.runnables import RunnableConfig
from langfuse.callback import CallbackHandler

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../src'))

from deep_diagnose.common.config import get_config
from unit.reasoning.coder_workflow_builder import (
    build_coder_graph,
    build_coder_graph_with_memory,
    coder_graph
)
from deep_diagnose.core.reasoning.workflow.types import ReasoningState
from deep_diagnose.prompts.planner_model import Plan, Step, StepType


class TestCoderWorkflow:
    """
    Coder Workflow测试类
    - 修正了Langfuse回调的导入和使用
    - 通过辅助函数简化了测试用例的设置
    - 所有测试统一使用astream进行调用
    - 修正了Step和Plan模型字段缺失的问题
    - 修正了langfuse handler的shutdown调用
    """

    def _prepare_test_setup(self, trace_name: str, thread_id: str, user_id: str = "test_user"):
        """
        辅助函数：准备测试所需的Langfuse回调和RunnableConfig
        """
        request_id = f"{trace_name}_{os.urandom(4).hex()}"
        
        lf_config = get_config().observability.langfuse
        handler = CallbackHandler(
            public_key=lf_config.public_key,
            secret_key=lf_config.secret_key,
            host=lf_config.endpoint,
            trace_name=trace_name,
            session_id=thread_id,
            user_id=user_id
        )
        
        config = RunnableConfig(
            configurable={
                "thread_id": thread_id,
                "user_id": user_id,
                "request_id": request_id
            },
            callbacks=[handler],
            stream_mode=["messages", "updates"],
            subgraphs=True,
        )
        
        print(f"✓ Langfuse追踪已配置, trace_name: {trace_name}, request_id: {request_id}")
        return config, handler, request_id

    async def _get_result_from_astream(self, graph, initial_state, config):
        """辅助函数：从astream事件流中获取最终结果"""
        final_result = None
        event_count = 0
        
        # 确保状态对象包含必要的messages字段
        if hasattr(initial_state, 'messages') and not initial_state.messages:
            from langchain_core.messages import HumanMessage
            initial_state.messages = [HumanMessage(content="执行计划中的步骤")]
        
        async for agent, _, event_data in graph.astream(
            initial_state,
            config=config,
            stream_mode=["messages", "updates"],
            subgraphs=True,
        ):
            event_count += 1
            print(f"ℹ️ Event {event_count}: agent={agent}, event_data type: {type(event_data)}")
            
            # 保存最后的事件数据作为最终结果
            if event_data is not None:
                final_result = event_data
                
        print(f"ℹ️ Total events: {event_count}, Final result type: {type(final_result)}")
        return final_result

    def test_coder_graph_creation(self):
        """测试coder workflow图的创建"""
        assert build_coder_graph() is not None
        print("✓ 无内存版本coder graph创建成功")
        assert build_coder_graph_with_memory() is not None
        print("✓ 带内存版本coder graph创建成功")
        assert coder_graph is not None
        print("✓ 预定义coder graph实例可用")

    @pytest.mark.asyncio
    async def test_data_analysis_execution(self):
        """测试数据分析的coder执行"""
        print("\n=== 测试数据分析Coder执行 (astream with Langfuse) ===")
        config, handler, request_id = self._prepare_test_setup(
            trace_name="coder-data-analysis",
            thread_id="thread-data-analysis"
        )
        
        # 创建更复杂的数据分析任务
        analysis_task = """
        针对这个数据（sample_data.csv）做统计分析：
        1. 年龄数据分析：描述性统计、分布分析、年龄段分组统计
        2. Category输入分析：各类别的分布、占比、交叉分析
        3. 输出各种高级分析结果：
           - 年龄与收入的相关性分析
           - 年龄与分数的相关性分析
           - 各Category在不同年龄段的分布
           - 收入分布分析
           - 分数分布分析
           - 用ploty生成可视化图表
    
        """
        
        steps = [
            Step(
                step_order=1,
                title="综合数据分析与可视化",
                description="完整的数据分析流程：1) 数据加载与探索 - 加载CSV数据，查看数据结构和基本信息；2) 年龄数据分析 - 描述性统计、分布分析、年龄段分组统计；3) Category分析 - 各类别的分布、占比、交叉分析；4) 相关性和高级分析 - 相关性分析、分布分析和plotly可视化图表生成",
                step_type=StepType.PROCESSING
            )
        ]
        
        plan = Plan(
            has_enough_context=False,
            thought="执行数据统计分析任务",
            title="CSV数据统计分析",
            steps=steps
        )
        state = ReasoningState(
            messages=[HumanMessage(content=analysis_task)], 
            current_plan=plan, 
            request_id=request_id,
            observations=[],
            plan_iterations=0
        )
        
        try:
            result = await self._get_result_from_astream(coder_graph, state, config)
            
            assert result is not None, "未能从astream事件中获取最终结果"
            
            # 从结果中提取最终状态，可能在不同的键下
            final_state = None
            if isinstance(result, dict):
                # 查找包含ReasoningState的键
                for key, value in result.items():
                    if hasattr(value, 'current_plan') and hasattr(value, 'messages'):
                        final_state = value
                        break
                        
                # 如果没找到，尝试直接使用原始状态验证执行结果
                if not final_state:
                    final_state = state  # 使用原始状态
            
            # 验证是否有步骤被执行
            if final_state and hasattr(final_state, 'current_plan') and final_state.current_plan:
                if hasattr(final_state.current_plan, 'steps'):
                    completed_steps = [s for s in final_state.current_plan.steps if s.execution_res is not None]
                    print(f"✓ 找到 {len(completed_steps)} 个已完成的步骤")
                else:
                    print("✓ 工作流程已执行完成（未检测到步骤详情）")
            
            print("✓ Workflow astream执行成功")
            
        finally:
            handler.flush() # 修正：使用flush而不是shutdown



if __name__ == "__main__":
    """直接运行测试"""
    print("🧪 Coder Workflow测试开始\n")
    
    # 创建测试实例
    test_instance = TestCoderWorkflow()
    
    # 1. 图创建测试
    print("1. 测试图创建")
    test_instance.test_coder_graph_creation()
    
    # 2. 数据分析执行测试
    print("\n2. 测试数据分析执行")
    asyncio.run(test_instance.test_data_analysis_execution())
    


    
    print(f"\n🎉 所有测试完成！")
    print(f"\n=== Coder Workflow特点 ===")
    print(f"✅ 专注于数据分析和统计任务")
    print(f"✅ 支持复杂的多步骤数据处理")
    print(f"✅ 能够处理CSV数据加载和分析")
    print(f"✅ 支持相关性分析和可视化")
    print(f"✅ 集成langfuse追踪")
    print(f"✅ 简化的工作流程（START -> coder -> END）")