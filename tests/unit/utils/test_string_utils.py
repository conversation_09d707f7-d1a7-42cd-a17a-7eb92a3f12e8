import unittest
from src.deep_diagnose.common.utils.string_utils import (
    is_blank,
    contains_keywords,
    contains_other_characters,
    substring_before,
    substring_after,
    get_first_non_empty_line,
    replace_newlines_with_spaces
)


class TestStringUtils(unittest.TestCase):
    
    def test_is_blank(self):
        # 测试空字符串
        self.assertTrue(is_blank(None))
        self.assertTrue(is_blank(''))
        self.assertTrue(is_blank(' '))
        self.assertTrue(is_blank('\t'))
        self.assertTrue(is_blank('\n'))
        self.assertTrue(is_blank(' \t\n '))
        
        # 测试非空字符串
        self.assertFalse(is_blank('a'))
        self.assertFalse(is_blank(' a '))
        self.assertFalse(is_blank('hello'))
    
    def test_contains_keywords(self):
        # 测试正常情况
        self.assertEqual(contains_keywords('Hello World', ['world', 'test']), 'world')
        self.assertEqual(contains_keywords('Hello World', ['hello', 'test']), 'hello')
        self.assertIsNone(contains_keywords('Hello World', ['test', 'example']))
        
        # 测试大小写不敏感和下划线忽略
        self.assertEqual(contains_keywords('Hello_World_Test', ['world', 'test']), 'world')
        self.assertEqual(contains_keywords('hello_world', ['HELLO', 'TEST']), 'HELLO')
        
        # 测试边界情况
        self.assertIsNone(contains_keywords('', ['test']))
        self.assertIsNone(contains_keywords('hello', []))
        self.assertIsNone(contains_keywords(None, ['test']))
        self.assertIsNone(contains_keywords('   ', ['test']))
    
    def test_contains_other_characters(self):
        # 测试正常情况
        self.assertTrue(contains_other_characters('abc123!', set('abc')))
        self.assertFalse(contains_other_characters('abc', set('abc')))
        self.assertFalse(contains_other_characters('aaa', set('abc')))
        self.assertTrue(contains_other_characters('abc123', set('abc')))
        
        # 测试边界情况
        self.assertFalse(contains_other_characters('', set('abc')))
        self.assertTrue(contains_other_characters('abc', set()))
    
    def test_substring_before(self):
        # 测试正常情况
        self.assertEqual(substring_before('hello.world.test', '.'), 'hello')
        self.assertEqual(substring_before('hello.world.test', 'world'), 'hello.')
        self.assertEqual(substring_before('hello', 'xyz'), 'hello')
        
        # 测试边界情况
        self.assertEqual(substring_before('', '.'), '')
        self.assertEqual(substring_before('hello', ''), 'hello')
        self.assertEqual(substring_before(None, '.'), '')
        self.assertEqual(substring_before('hello', None), 'hello')
    
    def test_substring_after(self):
        # 测试正常情况
        self.assertEqual(substring_after('hello.world.test', '.'), 'world.test')
        self.assertEqual(substring_after('hello.world.test', 'world'), '.test')
        self.assertEqual(substring_after('hello', 'xyz'), 'hello')
        
        # 测试边界情况
        self.assertEqual(substring_after('', '.'), '')
        self.assertEqual(substring_after('hello', ''), 'hello')
        self.assertEqual(substring_after(None, '.'), '')
        self.assertEqual(substring_after('hello', None), 'hello')
    
    def test_get_first_non_empty_line(self):
        # 测试正常情况
        self.assertEqual(get_first_non_empty_line('\n\nhello\nworld'), 'hello')
        self.assertEqual(get_first_non_empty_line('  \n\t\n  hello  \nworld'), '  hello  ')
        self.assertEqual(get_first_non_empty_line('hello\n\nworld'), 'hello')
        
        # 测试边界情况
        self.assertEqual(get_first_non_empty_line(''), '')
        self.assertEqual(get_first_non_empty_line('\n\n\n'), '')
        self.assertEqual(get_first_non_empty_line(' \t \n'), '')
    
    def test_replace_newlines_with_spaces(self):
        # 测试正常情况
        self.assertEqual(replace_newlines_with_spaces('hello\nworld'), 'hello world')
        self.assertEqual(replace_newlines_with_spaces('hello\r\nworld'), 'hello world')
        self.assertEqual(replace_newlines_with_spaces('hello\n\n\nworld'), 'hello world')
        self.assertEqual(replace_newlines_with_spaces('hello\r\n\r\nworld'), 'hello world')
        
        # 测试边界情况
        self.assertEqual(replace_newlines_with_spaces(''), '')
        self.assertEqual(replace_newlines_with_spaces(None), '')
        self.assertEqual(replace_newlines_with_spaces('   '), '   ')
        self.assertEqual(replace_newlines_with_spaces('hello world'), 'hello world')


if __name__ == '__main__':
    unittest.main()