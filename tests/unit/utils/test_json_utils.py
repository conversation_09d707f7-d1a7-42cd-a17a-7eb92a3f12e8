import unittest
import json
from deep_diagnose.common.utils.json_utils import repair_json_output, extract_json_code_block, extract_items_from_incomplete_json


class TestJsonUtils(unittest.TestCase):
    
    def test_repair_json_output_with_valid_json(self):
        """测试repair_json_output处理有效JSON"""
        content = '{"key": "value"}'
        result = repair_json_output(content)
        self.assertEqual(result, '{"key": "value"}')
    
    def test_repair_json_output_with_json_code_block(self):
        """测试repair_json_output处理被代码块包裹的JSON"""
        content = "```json\n{\"key\": \"value\"}\n```"
        result = repair_json_output(content)
        self.assertEqual(result, '{"key": "value"}')
    
    def test_repair_json_output_with_ts_code_block(self):
        """测试repair_json_output处理被ts代码块包裹的JSON"""
        content = "```ts\n{\"key\": \"value\"}\n```"
        result = repair_json_output(content)
        self.assertEqual(result, '{"key": "value"}')
    
    def test_repair_json_output_with_malformed_json(self):
        """测试repair_json_output处理格式错误的JSON"""
        content = '{"key": "value", "missing": }'
        result = repair_json_output(content)
        # 修复后的JSON应该能被正确解析
        parsed = json.loads(result)
        self.assertIn("key", parsed)
    
    def test_repair_json_output_with_non_json_content(self):
        """测试repair_json_output处理非JSON内容"""
        content = "This is not JSON content"
        result = repair_json_output(content)
        self.assertEqual(result, "This is not JSON content")
    
    def test_extract_json_code_block_with_valid_block(self):
        """测试extract_json_code_block提取有效的JSON代码块"""
        text = "Some text\n```json\n{\"key\": \"value\"}\n```\nMore text"
        result = extract_json_code_block(text)
        self.assertEqual(result, "{\"key\": \"value\"}")
    
    def test_extract_json_code_block_without_json_block(self):
        """测试extract_json_code_block在没有JSON代码块时返回空字符串"""
        text = "Some text without JSON block"
        result = extract_json_code_block(text)
        self.assertEqual(result, "")
    
    def test_extract_json_code_block_with_multiple_lines(self):
        """测试extract_json_code_block提取多行JSON"""
        text = "Prefix\n```json\n{\n  \"key1\": \"value1\",\n  \"key2\": \"value2\"\n}\n```\nSuffix"
        result = extract_json_code_block(text)
        expected = "{\n  \"key1\": \"value1\",\n  \"key2\": \"value2\"\n}"
        self.assertEqual(result, expected)
    
    def test_extract_items_from_incomplete_json_with_valid_objects(self):
        """测试extract_items_from_incomplete_json提取有效的JSON对象"""
        json_str = '{"id": 1, "name": "test"}{"id": 2, "name": "test2"}'
        result, size = extract_items_from_incomplete_json(json_str)
        self.assertEqual(len(result), 2)
        self.assertEqual(result[0]["id"], 1)
        self.assertEqual(result[1]["id"], 2)
        self.assertEqual(size, len(json_str))
    
    def test_extract_items_from_incomplete_json_with_incomplete_objects(self):
        """测试extract_items_from_incomplete_json处理不完整的JSON对象"""
        json_str = '{"id": 1, "name": "test"}{"id": 2, "name":'
        result, size = extract_items_from_incomplete_json(json_str)
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]["id"], 1)
    
    def test_extract_items_from_incomplete_json_without_objects(self):
        """测试extract_items_from_incomplete_json在没有JSON对象时返回空列表"""
        json_str = "No JSON objects here"
        result, size = extract_items_from_incomplete_json(json_str)
        self.assertEqual(result, [])
        self.assertEqual(size, 0)


if __name__ == '__main__':
    unittest.main()