"""
Chat Request Processors 单元测试

测试同步和异步请求处理器的核心功能
"""

import asyncio
import json
import pytest
from unittest.mock import AsyncMock, MagicMock, patch, Mock
from typing import AsyncGenerator, Dict, Any, Optional, List

from deep_diagnose.core.events.base_event import BaseAgentOutputEvent, ErrorEvent, StatusEvent
from deep_diagnose.services.chat.request_processors import (
    RequestProcessor,
    SynchronousRequestProcessor,
    AsynchronousRequestProcessor,
    DEFAULT_POLL_INTERVAL,
    DEFAULT_MAX_WAIT_TIME
)
from deep_diagnose.storage.redis_client import RedisClient


class MockAgentOutputEvent(BaseAgentOutputEvent):
    """模拟的Agent输出事件类"""

    def __init__(self, content: str = "", result: str = "", error: Optional[str] = None, 
                 urls: Optional[List[Dict]] = None, status: str = "processing", finished: bool = False):
        self.content = content
        self.result = result
        self.error = error
        self.urls = urls or []
        self.status = status
        self.finished = finished

    def to_sse_format(self) -> str:
        """转换为SSE格式"""
        data = {
            "content": self.content,
            "result": self.result,
            "status": self.status,
            "finished": self.finished
        }
        if self.error:
            data["error"] = self.error
        if self.urls:
            data["urls"] = self.urls
        return json.dumps(data, ensure_ascii=False)

    def to_persist_format(self) -> Dict[str, Any]:
        """转换为持久化格式"""
        return {
            "message": self.result or self.content,
            "status": self.status,
            "ext": {"urls": self.urls, "error": self.error}
        }

    def parse(self, raw_event: str) -> bool:
        """解析原始事件并更新自身状态"""
        return False

    @classmethod
    def from_sse_format(cls, event_data: str) -> Optional['MockAgentOutputEvent']:
        """从SSE格式创建事件对象"""
        try:
            parsed = json.loads(event_data)
            return cls(
                content=parsed.get("content", ""),
                result=parsed.get("result", ""),
                error=parsed.get("error"),
                urls=parsed.get("urls", []),
                status=parsed.get("status", "processing"),
                finished=parsed.get("finished", False)
            )
        except (json.JSONDecodeError, Exception):
            return None

    def _is_final_event(self, event_data: str) -> bool:
        """判断是否为最终事件"""
        try:
            data = json.loads(event_data)
            return (
                bool(data.get("error")) or
                data.get("finished", False) or
                data.get("status", "").lower() in ["completed", "failed", "finished", "done"]
            )
        except Exception:
            return True


class MockContext:
    """模拟的请求上下文"""

    def __init__(self, question: str = "测试问题", user_id: str = "test_user", 
                 agent: str = "TestAgent", request_id: str = "test_request_123"):
        self.question = question
        self.user_id = user_id
        self.agent = agent
        self.request_id = request_id


class MockSessionInfo:
    """模拟的会话信息"""

    def __init__(self, session_id: str = "test_session", message_id: int = 1, messages: Optional[List] = None):
        self.session_id = session_id
        self.message_id = message_id
        self.messages = messages or []


class MockAgent:
    """模拟的Agent类"""

    def __init__(self, events: List[MockAgentOutputEvent], should_error: bool = False):
        self.events = events
        self.should_error = should_error

    async def astream(self, question: str, messages: List, **kwargs) -> AsyncGenerator[MockAgentOutputEvent, None]:
        """模拟异步流式处理"""
        if self.should_error:
            raise Exception("模拟Agent执行错误")
        
        for event in self.events:
            yield event
            await asyncio.sleep(0.01)  # 模拟处理时间


class TestRequestProcessorBase:
    """请求处理器基础测试类"""

    @pytest.fixture
    def mock_redis_client(self):
        """模拟Redis客户端"""
        redis_mock = Mock(spec=RedisClient)
        redis_mock.publish_async = AsyncMock()
        redis_mock.set_cache_async = AsyncMock()
        redis_mock.get_cache_async = AsyncMock(return_value=None)
        redis_mock.get_cache = Mock(return_value=None)
        return redis_mock

    @pytest.fixture
    def mock_context(self):
        """模拟请求上下文"""
        return MockContext()

    @pytest.fixture
    def sample_events(self):
        """示例事件列表"""
        return [
            MockAgentOutputEvent(content="开始处理", status="started"),
            MockAgentOutputEvent(content="处理中", result="中间结果"),
            MockAgentOutputEvent(
                content="处理完成", 
                result="最终结果", 
                status="completed", 
                finished=True,
                urls=[{"url": "http://example.com/report", "title": "报告链接"}]
            )
        ]


class TestSynchronousRequestProcessor(TestRequestProcessorBase):
    """同步请求处理器测试"""

    @pytest.fixture
    def sync_processor(self, mock_redis_client):
        """创建同步处理器实例"""
        return SynchronousRequestProcessor(mock_redis_client)

    @pytest.mark.asyncio
    async def test_process_success(self, sync_processor, mock_context, sample_events):
        """测试同步处理器成功执行"""
        mock_agent = MockAgent(sample_events)

        with patch('deep_diagnose.services.chat.request_processors.SessionManager') as mock_session_mgr, \
             patch('deep_diagnose.services.chat.request_processors.AgentManager') as mock_agent_mgr:

            # 设置Mock
            mock_session_info = MockSessionInfo()
            mock_session_mgr.setup_session = AsyncMock(return_value=mock_session_info)
            mock_session_mgr.persist_ai_response = AsyncMock()
            mock_agent_mgr.create_agent = Mock(return_value=mock_agent)
            mock_agent_mgr.prepare_agent_kwargs = Mock(return_value={})

            # 执行处理
            events_received = []
            async for event in sync_processor.process(mock_context):
                events_received.append(event)

            # 验证结果
            assert len(events_received) == len(sample_events)
            assert events_received[0].content == "开始处理"
            assert events_received[-1].finished == True

            # 验证调用
            mock_session_mgr.setup_session.assert_called_once_with(mock_context)
            mock_agent_mgr.create_agent.assert_called_once()
            mock_session_mgr.persist_ai_response.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_agent_error(self, sync_processor, mock_context):
        """测试Agent执行错误处理"""
        mock_agent = MockAgent([], should_error=True)

        with patch('deep_diagnose.services.chat.request_processors.SessionManager') as mock_session_mgr, \
             patch('deep_diagnose.services.chat.request_processors.AgentManager') as mock_agent_mgr:

            mock_session_info = MockSessionInfo()
            mock_session_mgr.setup_session = AsyncMock(return_value=mock_session_info)
            mock_agent_mgr.create_agent = Mock(return_value=mock_agent)
            mock_agent_mgr.prepare_agent_kwargs = Mock(return_value={})

            # 执行处理
            events_received = []
            async for event in sync_processor.process(mock_context):
                events_received.append(event)

            # 验证错误事件
            assert len(events_received) == 1
            assert isinstance(events_received[0], ErrorEvent)
            assert "同步请求执行失败" in events_received[0].error

    @pytest.mark.asyncio
    async def test_process_cancelled(self, sync_processor, mock_context, sample_events):
        """测试请求被取消的处理"""
        mock_agent = MockAgent(sample_events)

        with patch('deep_diagnose.services.chat.request_processors.SessionManager') as mock_session_mgr, \
             patch('deep_diagnose.services.chat.request_processors.AgentManager') as mock_agent_mgr:

            mock_session_info = MockSessionInfo()
            mock_session_mgr.setup_session = AsyncMock(return_value=mock_session_info)
            mock_agent_mgr.create_agent = Mock(return_value=mock_agent)
            mock_agent_mgr.prepare_agent_kwargs = Mock(return_value={})

            # 模拟取消
            async def cancelled_astream(*args, **kwargs):
                yield sample_events[0]
                raise asyncio.CancelledError()

            mock_agent.astream = cancelled_astream

            # 执行处理并验证取消
            events_received = []
            with pytest.raises(asyncio.CancelledError):
                async for event in sync_processor.process(mock_context):
                    events_received.append(event)

            # 验证收到了部分事件
            assert len(events_received) == 1


class TestAsynchronousRequestProcessor(TestRequestProcessorBase):
    """异步请求处理器测试"""

    @pytest.fixture
    def async_processor(self, mock_redis_client):
        """创建异步处理器实例"""
        return AsynchronousRequestProcessor(mock_redis_client)

    @pytest.mark.asyncio
    async def test_process_with_pubsub_success(self, async_processor, mock_context):
        """测试通过PubSub成功处理异步请求"""
        
        # 创建模拟的PubSub流
        mock_pubsub_stream = Mock()
        mock_pubsub_stream.close = AsyncMock()
        
        async def mock_aiter_messages(timeout):
            messages = [
                '{"seq": 1, "event": {"content": "开始处理", "status": "started"}}',
                '{"seq": 2, "event": {"content": "处理完成", "status": "completed", "finished": true}}'
            ]
            for msg in messages:
                yield msg
        
        mock_pubsub_stream.aiter_messages = mock_aiter_messages
        
        with patch('deep_diagnose.services.chat.request_processors.SessionManager') as mock_session_mgr, \
             patch('deep_diagnose.services.chat.request_processors.ChatEventBus') as mock_event_bus_class, \
             patch('asyncio.create_task') as mock_create_task:

            # 设置Mock
            mock_session_info = MockSessionInfo()
            mock_session_mgr.setup_session = AsyncMock(return_value=mock_session_info)
            mock_session_mgr.create_ai_message_placeholder = AsyncMock(return_value=123)
            
            mock_event_bus = Mock()
            mock_event_bus_class.return_value = mock_event_bus
            mock_event_bus_class.channel_name = Mock(return_value="test_channel")
            mock_event_bus.subscribe = AsyncMock(return_value=mock_pubsub_stream)

            # 执行处理
            events_received = []
            async for event in async_processor.process(mock_context):
                events_received.append(event)

            # 验证结果
            assert len(events_received) >= 1
            mock_session_mgr.setup_session.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_event_from_data_valid_json(self, async_processor):
        """测试从有效JSON创建事件对象"""
        event_data = '{"content": "测试内容", "status": "processing", "finished": false}'
        
        with patch('deep_diagnose.core.events.base_event.BaseAgentOutputEvent.from_sse_format') as mock_from_sse:
            mock_event = MockAgentOutputEvent(content="测试内容", status="processing")
            mock_from_sse.return_value = mock_event
            
            result = async_processor._create_event_from_data(event_data)
            assert result == mock_event

    @pytest.mark.asyncio
    async def test_is_final_event_cases(self, async_processor):
        """测试最终事件判断的各种情况"""
        # 测试错误事件
        error_event = '{"error": "处理失败", "status": "failed"}'
        assert async_processor._is_final_event(error_event) is True

        # 测试完成事件
        finished_event = '{"finished": true, "status": "completed"}'
        assert async_processor._is_final_event(finished_event) is True

        # 测试处理中事件
        processing_event = '{"status": "processing", "finished": false}'
        assert async_processor._is_final_event(processing_event) is False

        # 测试无效JSON
        invalid_json = 'invalid json'
        assert async_processor._is_final_event(invalid_json) is True

    @pytest.mark.asyncio
    async def test_send_dingtalk_notification_success(self, async_processor, mock_context):
        """测试发送钉钉通知成功"""
        mock_session_info = MockSessionInfo()
        final_event = MockAgentOutputEvent(
            result="测试结果",
            urls=[{"url": "http://example.com/report", "title": "报告"}]
        )

        with patch('deep_diagnose.services.chat.request_processors.DingTalkService') as mock_dingtalk:
            mock_dingtalk_service = Mock()
            mock_dingtalk_service.send_private_card_message = AsyncMock()
            mock_dingtalk.return_value = mock_dingtalk_service

            # 执行发送通知
            await async_processor._send_dingtalk_notification(mock_context, mock_session_info, final_event)

            # 验证调用
            mock_dingtalk_service.send_private_card_message.assert_called_once()

    @pytest.mark.asyncio
    async def test_send_dingtalk_notification_no_url(self, async_processor, mock_context):
        """测试没有URL时不发送钉钉通知"""
        mock_session_info = MockSessionInfo()
        final_event = MockAgentOutputEvent(result="测试结果", urls=[])

        with patch('deep_diagnose.services.chat.request_processors.DingTalkService') as mock_dingtalk:
            mock_dingtalk_service = Mock()
            mock_dingtalk_service.send_private_card_message = AsyncMock()
            mock_dingtalk.return_value = mock_dingtalk_service

            # 执行发送通知
            await async_processor._send_dingtalk_notification(mock_context, mock_session_info, final_event)

            # 验证不发送
            mock_dingtalk_service.send_private_card_message.assert_not_called()


class TestRequestProcessorIntegration:
    """请求处理器集成测试"""

    @pytest.mark.asyncio
    async def test_end_to_end_sync_processing(self):
        """端到端同步处理测试"""
        mock_redis = Mock(spec=RedisClient)
        processor = SynchronousRequestProcessor(mock_redis)
        context = MockContext()
        
        test_events = [
            MockAgentOutputEvent(content="开始", status="started"),
            MockAgentOutputEvent(content="完成", status="completed", finished=True)
        ]
        mock_agent = MockAgent(test_events)

        with patch('deep_diagnose.services.chat.request_processors.SessionManager') as mock_session_mgr, \
             patch('deep_diagnose.services.chat.request_processors.AgentManager') as mock_agent_mgr:

            mock_session_mgr.setup_session = AsyncMock(return_value=MockSessionInfo())
            mock_session_mgr.persist_ai_response = AsyncMock()
            mock_agent_mgr.create_agent = Mock(return_value=mock_agent)
            mock_agent_mgr.prepare_agent_kwargs = Mock(return_value={})

            # 执行完整流程
            all_events = []
            async for event in processor.process(context):
                all_events.append(event)

            # 验证完整性
            assert len(all_events) == 2
            assert all_events[0].status == "started"
            assert all_events[1].finished == True

    @pytest.mark.asyncio
    async def test_processor_constants(self):
        """测试处理器常量配置"""
        assert DEFAULT_POLL_INTERVAL == 0.1
        assert DEFAULT_MAX_WAIT_TIME == 3600


if __name__ == "__main__":
    pytest.main([__file__, "-v"])