"""
测试MCP服务的description拆解功能

专注于测试Tool模型的新字段和_parse_description方法
"""

import pytest
from deep_diagnose.api.models.mcp import Tool
from deep_diagnose.services.mcp.mcp_service import MCPService


class TestMCPServiceDescriptionParser:
    """测试MCP服务描述解析功能"""
    
    def test_tool_model_with_new_fields(self):
        """测试Tool模型的新字段"""
        tool = Tool(
            name="test_tool",
            description="测试工具描述",
            args_schema={"param1": "str"},
            enabled=True,
            args="参数说明",
            results="返回结果说明"
        )
        
        assert tool.name == "test_tool"
        assert tool.description == "测试工具描述"
        assert tool.args_schema == {"param1": "str"}
        assert tool.enabled is True
        assert tool.args == "参数说明"
        assert tool.results == "返回结果说明"
        
        print(f"Tool模型测试通过: {tool}")
    
    def test_parse_complete_description(self):
        """测试完整description解析（包含Args和Returns）"""
        service = MCPService()
        
        full_description = """获取虚拟化vmcore分析结果，最大支持30天内的分析结果查询

Args:
    ncIp (str): 需要查询的物理服务器（NC）的IP地址。
    startTime (str): 分析范围开始时间，格式为yyyy-MM-dd HH:mm:ss
    endTime (str): 分析范围结束时间，格式为yyyy-MM-dd HH:mm:ss

Returns:
    dict: 虚拟化vmcore分析结果，列表中每个元素是个字典，含义如下：
        - analysisTime: 分析时间
        - category: 宕机一级原因分类，枚举值：VIRT、KERNEL、HARDWARE、OTHER"""
        
        description, args, results = service._parse_description(full_description)
        
        assert description == "获取虚拟化vmcore分析结果，最大支持30天内的分析结果查询"
        assert args is not None
        assert "ncIp (str)" in args
        assert "startTime (str)" in args
        assert "endTime (str)" in args
        assert results is not None
        assert "dict: 虚拟化vmcore分析结果" in results
        
        print(f"完整description解析测试通过:")
        print(f"  description: '{description}'")
        print(f"  args: '{args[:50]}...'")
        print(f"  results: '{results[:50]}...'")
    
    def test_parse_simple_description(self):
        """测试简单description解析（无Args和Returns）"""
        service = MCPService()
        
        simple_description = "这是一个简单的工具描述"
        description, args, results = service._parse_description(simple_description)
        
        assert description == simple_description
        assert args is None
        assert results is None
        
        print(f"简单description解析测试通过: '{description}'")
    
    def test_parse_args_only_description(self):
        """测试只有Args的description解析"""
        service = MCPService()
        
        args_only_description = """获取服务器状态信息

Args:
    server_id (str): 服务器ID
    timeout (int): 超时时间，单位秒"""
        
        description, args, results = service._parse_description(args_only_description)
        
        assert description == "获取服务器状态信息"
        assert args is not None
        assert "server_id (str)" in args
        assert "timeout (int)" in args
        assert results is None
        
        print(f"Args-only description解析测试通过:")
        print(f"  description: '{description}'")
        print(f"  args: '{args}'")
    
    def test_parse_returns_only_description(self):
        """测试只有Returns的description解析"""
        service = MCPService()
        
        returns_only_description = """检查系统健康状态

Returns:
    bool: 系统是否健康"""
        
        description, args, results = service._parse_description(returns_only_description)
        
        assert description == "检查系统健康状态"
        assert args is None
        assert results is not None
        assert "bool: 系统是否健康" in results
        
        print(f"Returns-only description解析测试通过:")
        print(f"  description: '{description}'")
        print(f"  results: '{results}'")
    
    def test_parse_empty_description(self):
        """测试空description解析"""
        service = MCPService()
        
        description, args, results = service._parse_description("")
        
        assert description == ""
        assert args is None
        assert results is None
        
        print("空description解析测试通过")
    
    def test_parse_none_description(self):
        """测试None description解析"""
        service = MCPService()
        
        description, args, results = service._parse_description(None)
        
        assert description is None
        assert args is None
        assert results is None
        
        print("None description解析测试通过")


if __name__ == "__main__":
    test = TestMCPServiceDescriptionParser()
    
    print("=== 开始测试Tool模型和description解析功能 ===\n")
    
    test.test_tool_model_with_new_fields()
    print()
    
    test.test_parse_complete_description()
    print()
    
    test.test_parse_simple_description()
    print()
    
    test.test_parse_args_only_description()
    print()
    
    test.test_parse_returns_only_description()
    print()
    
    test.test_parse_empty_description()
    print()
    
    test.test_parse_none_description()
    print()
    
    print("=== 所有测试通过！ ===")