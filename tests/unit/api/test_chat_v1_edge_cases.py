"""
Chat V1 API 边界条件单元测试
"""

from unittest.mock import AsyncMock

import pytest
from fastapi.testclient import TestClient

from deep_diagnose.api.models.chat import ChatRequest
from deep_diagnose.api.models.user import UserModel
from deep_diagnose.api.route.v1.chat import router
from deep_diagnose.core.events.base_event import BaseAgentOutputEvent
from deep_diagnose.domain.chat.models import MessageType

# 创建测试客户端
client = TestClient(router)


# 模拟事件数据
class MockAgentEvent(BaseAgentOutputEvent):
    def __init__(self, content="", error=None, urls=None, result=None):
        self.content = content
        self.error = error
        self.urls = urls or []
        self.result = result or ""
    
    def to_sse_format(self):
        return {
            "content": self.content,
            "error": self.error,
            "urls": self.urls,
            "result": self.result
        }
    
    @classmethod
    def parse(cls, data):
        # 简单实现parse方法以满足抽象类要求
        return cls(
            content=data.get("content", ""),
            error=data.get("error"),
            urls=data.get("urls", []),
            result=data.get("result", "")
        )


class TestChatV1APIEdgeCases:
    """Chat V1 API 边界条件测试"""
    
    @pytest.fixture
    def mock_user(self):
        """模拟用户信息"""
        return UserModel(
            user_id="test_user", 
            user_name="test_user", 
            user_type="test_user_type", 
            pop_user="test_pop_user", 
            access_key="test_access_key"
        )
    
    @pytest.fixture
    def mock_chat_service(self):
        """模拟聊天服务"""
        service = AsyncMock()
        service.current_session_id = "test_session_id"
        return service
    
    @pytest.mark.asyncio
    async def test_chat_v1_minimal_question_length(self, mock_user, mock_chat_service):
        """测试 Chat V1 API 最小问题长度"""
        # 模拟事件流
        async def mock_chat_generator(*args, **kwargs):
            yield MockAgentEvent(content="最小问题响应")
            yield MockAgentEvent(result="最终结果")
        
        mock_chat_service.chat = mock_chat_generator
        
        # 创建 Chat V1 请求（使用最小长度问题）
        request = ChatRequest(
            question="?",  # 最小长度1个字符
            agent="ReasoningAgent",
            user_id=mock_user.user_id,
            session_id=None,
            additional_info={},
            question_type=MessageType.HUMAN_QUERY,
        )
        
        # 导入实际的chat_v1函数来测试
        from deep_diagnose.api.route.v1.chat import chat_v1
        
        # 调用接口
        response = await chat_v1(request, mock_user, mock_chat_service)
        
        # 验证响应是StreamingResponse
        assert response.media_type == "text/event-stream"
    
    @pytest.mark.asyncio
    async def test_chat_v1_maximal_question_length(self, mock_user, mock_chat_service):
        """测试 Chat V1 API 最大问题长度"""
        # 模拟事件流
        async def mock_chat_generator(*args, **kwargs):
            yield MockAgentEvent(content="最大问题响应")
            yield MockAgentEvent(result="最终结果")
        
        mock_chat_service.chat = mock_chat_generator
        
        # 创建 Chat V1 请求（使用最大长度问题）
        request = ChatRequest(
            question="x" * 10000,  # 最大长度10000个字符
            agent="ReasoningAgent",
            user_id=mock_user.user_id,
            session_id=None,
            additional_info={},
            question_type=MessageType.HUMAN_QUERY,
        )
        
        # 导入实际的chat_v1函数来测试
        from deep_diagnose.api.route.v1.chat import chat_v1
        
        # 调用接口
        response = await chat_v1(request, mock_user, mock_chat_service)
        
        # 验证响应是StreamingResponse
        assert response.media_type == "text/event-stream"
    
    @pytest.mark.asyncio
    async def test_chat_v1_empty_additional_info(self, mock_user, mock_chat_service):
        """测试 Chat V1 API 空附加信息"""
        # 模拟事件流
        async def mock_chat_generator(*args, **kwargs):
            yield MockAgentEvent(content="空附加信息响应")
            yield MockAgentEvent(result="最终结果")
        
        mock_chat_service.chat = mock_chat_generator
        
        # 创建 Chat V1 请求（空附加信息）
        request = ChatRequest(
            question="测试问题",
            agent="ReasoningAgent",
            user_id=mock_user.user_id,
            session_id=None,
            additional_info={},  # 空字典
            question_type=MessageType.HUMAN_QUERY,
        )
        
        # 导入实际的chat_v1函数来测试
        from deep_diagnose.api.route.v1.chat import chat_v1
        
        # 调用接口
        response = await chat_v1(request, mock_user, mock_chat_service)
        
        # 验证响应是StreamingResponse
        assert response.media_type == "text/event-stream"
    
    @pytest.mark.asyncio
    async def test_chat_v1_complex_additional_info(self, mock_user, mock_chat_service):
        """测试 Chat V1 API 复杂附加信息"""
        # 模拟事件流
        async def mock_chat_generator(*args, **kwargs):
            yield MockAgentEvent(content="复杂附加信息响应")
            yield MockAgentEvent(result="最终结果")
        
        mock_chat_service.chat = mock_chat_generator
        
        # 创建 Chat V1 请求（复杂附加信息）
        request = ChatRequest(
            question="测试问题",
            agent="ReasoningAgent",
            user_id=mock_user.user_id,
            session_id=None,
            additional_info={
                "nested": {
                    "data": ["value1", "value2"],
                    "number": 42,
                    "boolean": True
                },
                "list": [1, 2, 3],
                "string": "复杂字符串"
            },
            question_type=MessageType.HUMAN_QUERY,
        )
        
        # 导入实际的chat_v1函数来测试
        from deep_diagnose.api.route.v1.chat import chat_v1
        
        # 调用接口
        response = await chat_v1(request, mock_user, mock_chat_service)
        
        # 验证响应是StreamingResponse
        assert response.media_type == "text/event-stream"
    
    @pytest.mark.asyncio
    async def test_chat_v1_all_question_types(self, mock_user, mock_chat_service):
        """测试 Chat V1 API 所有问题类型"""
        # 模拟事件流
        async def mock_chat_generator(*args, **kwargs):
            yield MockAgentEvent(content="问题类型响应")
            yield MockAgentEvent(result="最终结果")
        
        mock_chat_service.chat = mock_chat_generator
        
        # 测试所有三种问题类型
        question_types = [
            MessageType.HUMAN_QUERY,
            MessageType.AUTO_QUERY,
            MessageType.AI_RESPONSE
        ]
        
        for question_type in question_types:
            # 创建 Chat V1 请求
            request = ChatRequest(
                question="测试问题",
                agent="ReasoningAgent",
                user_id=mock_user.user_id,
                session_id=None,
                additional_info={},
                question_type=question_type,
            )
            
            # 导入实际的chat_v1函数来测试
            from deep_diagnose.api.route.v1.chat import chat_v1
            
            # 调用接口
            response = await chat_v1(request, mock_user, mock_chat_service)
            
            # 验证响应是StreamingResponse
            assert response.media_type == "text/event-stream"
    
    @pytest.mark.asyncio
    async def test_chat_v1_special_characters_in_question(self, mock_user, mock_chat_service):
        """测试 Chat V1 API 问题中的特殊字符"""
        # 模拟事件流
        async def mock_chat_generator(*args, **kwargs):
            yield MockAgentEvent(content="特殊字符响应")
            yield MockAgentEvent(result="最终结果")
        
        mock_chat_service.chat = mock_chat_generator
        
        # 创建 Chat V1 请求（包含特殊字符的问题）
        request = ChatRequest(
            question="测试问题包含特殊字符: !@#$%^&*()_+-=[]{}|;':\",./<>? 中文测试 测试",
            agent="ReasoningAgent",
            user_id=mock_user.user_id,
            session_id=None,
            additional_info={},
            question_type=MessageType.HUMAN_QUERY,
        )
        
        # 导入实际的chat_v1函数来测试
        from deep_diagnose.api.route.v1.chat import chat_v1
        
        # 调用接口
        response = await chat_v1(request, mock_user, mock_chat_service)
        
        # 验证响应是StreamingResponse
        assert response.media_type == "text/event-stream"
    
    @pytest.mark.asyncio
    async def test_chat_v1_unicode_characters_in_question(self, mock_user, mock_chat_service):
        """测试 Chat V1 API 问题中的Unicode字符"""
        # 模拟事件流
        async def mock_chat_generator(*args, **kwargs):
            yield MockAgentEvent(content="Unicode响应")
            yield MockAgentEvent(result="最终结果")
        
        mock_chat_service.chat = mock_chat_generator
        
        # 创建 Chat V1 请求（包含Unicode字符的问题）
        request = ChatRequest(
            question="测试问题包含Unicode字符: 你好世界 🌍 αβγ δεζ ΑΒΓ ΔΕΖ",
            agent="ReasoningAgent",
            user_id=mock_user.user_id,
            session_id=None,
            additional_info={},
            question_type=MessageType.HUMAN_QUERY,
        )
        
        # 导入实际的chat_v1函数来测试
        from deep_diagnose.api.route.v1.chat import chat_v1
        
        # 调用接口
        response = await chat_v1(request, mock_user, mock_chat_service)
        
        # 验证响应是StreamingResponse
        assert response.media_type == "text/event-stream"
    
    @pytest.mark.asyncio
    async def test_chat_v1_empty_event_stream(self, mock_user, mock_chat_service):
        """测试 Chat V1 API 空事件流"""
        # 模拟空事件流
        async def mock_chat_generator(*args, **kwargs):
            # 不产生任何事件
            return
            yield  # 使函数成为生成器
        
        mock_chat_service.chat = mock_chat_generator
        
        # 创建 Chat V1 请求
        request = ChatRequest(
            question="测试问题",
            agent="ReasoningAgent",
            user_id=mock_user.user_id,
            session_id=None,
            additional_info={},
            question_type=MessageType.HUMAN_QUERY,
        )
        
        # 导入实际的chat_v1函数来测试
        from deep_diagnose.api.route.v1.chat import chat_v1
        
        # 调用接口
        response = await chat_v1(request, mock_user, mock_chat_service)
        
        # 验证响应是StreamingResponse
        assert response.media_type == "text/event-stream"
    
    @pytest.mark.asyncio
    async def test_chat_v1_single_event_stream(self, mock_user, mock_chat_service):
        """测试 Chat V1 API 单一事件流"""
        # 模拟单一事件流
        async def mock_chat_generator(*args, **kwargs):
            yield MockAgentEvent(content="单一事件", result="最终结果")
        
        mock_chat_service.chat = mock_chat_generator
        
        # 创建 Chat V1 请求
        request = ChatRequest(
            question="测试问题",
            agent="ReasoningAgent",
            user_id=mock_user.user_id,
            session_id=None,
            additional_info={},
            question_type=MessageType.HUMAN_QUERY,
        )
        
        # 导入实际的chat_v1函数来测试
        from deep_diagnose.api.route.v1.chat import chat_v1
        
        # 调用接口
        response = await chat_v1(request, mock_user, mock_chat_service)
        
        # 验证响应是StreamingResponse
        assert response.media_type == "text/event-stream"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])