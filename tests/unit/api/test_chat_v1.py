"""
Chat V1 API 单元测试
"""

import pytest
from unittest.mock import AsyncMock, Mock

from deep_diagnose.api.models.chat import ChatRequest
from deep_diagnose.api.models.user import UserModel
from deep_diagnose.core.events.base_event import BaseAgentOutputEvent
from deep_diagnose.domain.chat.models import MessageType


class MockAgentEvent(BaseAgentOutputEvent):
    """模拟智能体事件"""
    
    def __init__(self, content="", error=None, urls=None, result=None):
        self.content = content
        self.error = error
        self.urls = urls or []
        self.result = result or ""
    
    def to_sse_format(self):
        """转换为SSE格式"""
        return {
            "content": self.content,
            "error": self.error,
            "urls": self.urls,
            "result": self.result
        }
    
    @classmethod
    def parse(cls, data):
        """解析数据"""
        return cls(
            content=data.get("content", ""),
            error=data.get("error"),
            urls=data.get("urls", []),
            result=data.get("result", "")
        )


class TestChatV1API:
    """Chat V1 API 单元测试类"""
    
    @pytest.fixture
    def mock_user(self):
        """模拟用户信息"""
        return UserModel(
            user_id="test_user", 
            user_name="test_user", 
            user_type="test_user_type", 
            pop_user="test_pop_user", 
            access_key="test_access_key"
        )
    
    @pytest.fixture
    def mock_chat_service(self):
        """模拟聊天服务"""
        service = AsyncMock()
        service.current_session_id = "test_session_id"
        return service
    
    @pytest.mark.asyncio
    async def test_chat_v1_simple(self, mock_user, mock_chat_service):
        """测试 Chat V1 API 基本功能"""
        
        # 创建 Chat V1 请求
        request = ChatRequest(
            question="测试问题",
            agent="InspectAgent",
            user_id=mock_user.user_id,
            session_id="",
            additional_info={},
            question_type=MessageType.HUMAN_QUERY,
        )
        
        # 验证请求对象创建成功
        assert request.question == "测试问题"
        assert request.agent == "InspectAgent"
        assert request.user_id == mock_user.user_id
        assert request.question_type == MessageType.HUMAN_QUERY
    
    @pytest.mark.asyncio
    async def test_chat_v1_error_handling_simple(self, mock_user, mock_chat_service):
        """测试 Chat V1 API 错误处理"""
        
        # 创建带有错误的模拟事件
        error_event = MockAgentEvent(error="测试错误")
        
        # 验证错误事件正确创建
        assert error_event.error == "测试错误"
        assert "error" in error_event.to_sse_format()
        assert error_event.to_sse_format()["error"] == "测试错误"
    
    def test_mock_agent_event_creation(self):
        """测试模拟事件创建"""
        event = MockAgentEvent(
            content="测试内容",
            result="测试结果",
            urls=["http://test.com"]
        )
        
        assert event.content == "测试内容"
        assert event.result == "测试结果"
        assert event.urls == ["http://test.com"]
        assert event.error is None
    
    def test_mock_agent_event_parse(self):
        """测试模拟事件解析"""
        data = {
            "content": "解析内容",
            "error": "解析错误",
            "urls": ["http://parse.com"],
            "result": "解析结果"
        }
        
        event = MockAgentEvent.parse(data)
        
        assert event.content == "解析内容"
        assert event.error == "解析错误"
        assert event.urls == ["http://parse.com"]
        assert event.result == "解析结果"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])