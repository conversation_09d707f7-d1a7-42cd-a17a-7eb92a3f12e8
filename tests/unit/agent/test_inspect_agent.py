"""
Unit tests for InspectAgent
"""

import asyncio
import pytest
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch, MagicMock
import json
from datetime import datetime, timedelta

from deep_diagnose.core.interactive.agents.inspect import InspectAgent
from deep_diagnose.core.interactive.types.state import InspectState
from deep_diagnose.core.events.inspect_event import InspectEvent
from deep_diagnose.common.utils.machine_utils import MachineIdType
from deep_diagnose.tools.mcp.mcp_tool_manager import MCPT<PERSON>Manager
from deep_diagnose.storage.redis_client import RedisClient


class TestInspectAgent:
    """Test cases for InspectAgent"""

    def test_inspect_agent_initialization(self):
        """Test InspectAgent initialization"""
        agent = InspectAgent({})
        assert isinstance(agent, InspectAgent)
        # 验证核心属性存在
        assert hasattr(agent, "_langfuse_handler")

    @pytest.mark.asyncio
    async def test_astream_method_exists(self):
        """Test that astream method exists and is callable"""
        agent = InspectAgent({})
        assert hasattr(agent, "astream")
        assert callable(getattr(agent, "astream"))


# Test data that was originally in the _main function of inspect.py
# These are kept as test data constants for reference
TEST_CASES = [
    {
        "machine_id": "i-bp1fz27ong6p6w693vn5",
        "description": "ECS Instance ID 测试",
        "start_time": "2025-07-28 00:00:00",
        "end_time": "2025-07-30 23:59:59",
    },
    {
        "machine_id": "i-wz97reb1ywapz0c4luac",
        "description": "ECS Instance ID 测试 2",
        "start_time": "2025-07-28 00:00:00",
        "end_time": "2025-07-30 23:59:59",
    },
    {
        "machine_id": "***********",
        "description": "NC IP Address 测试",
        "start_time": "2025-07-28 00:00:00",
        "end_time": "2025-07-30 23:59:59",
    },
]


class TestInspectAgentTestData:
    """Test data validation for InspectAgent"""

    def test_test_cases_exist(self):
        """Test that test cases are defined"""
        assert len(TEST_CASES) == 3
        assert TEST_CASES[0]["machine_id"] == "i-bp1fz27ong6p6w693vn5"
        assert TEST_CASES[1]["machine_id"] == "i-wz97reb1ywapz0c4luac"
        assert TEST_CASES[2]["machine_id"] == "***********"

    def test_test_case_structure(self):
        """Test that test cases have the correct structure"""
        for i, test_case in enumerate(TEST_CASES):
            assert "machine_id" in test_case
            assert "description" in test_case
            assert "start_time" in test_case
            assert "end_time" in test_case
            assert test_case["start_time"] == "2025-07-28 00:00:00"
            assert test_case["end_time"] == "2025-07-30 23:59:59"


if __name__ == "__main__":
    pytest.main([__file__])
