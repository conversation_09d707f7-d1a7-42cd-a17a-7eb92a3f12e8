"""
recommendation_generation_node 模块的单元测试

测试推荐生成节点的功能，包括MCP工具调用、默认推荐、异常处理等
"""

import pytest
import json
from unittest.mock import Mock, MagicMock, patch, AsyncMock
from typing import Dict, Any, List

from deep_diagnose.core.interactive.nodes.recommendation_generation_node import (
    recommendation_generation_node,
    _build_messages_context,
    _build_features,
    _parse_prediction_result,
    _get_default_recommendations,
)
from deep_diagnose.common.utils.machine_utils import MachineIdType


class TestRecommendationGenerationNode:
    """recommendation_generation_node 函数的测试"""

    @pytest.mark.asyncio
    @patch("deep_diagnose.core.interactive.nodes.recommendation_generation_node.next_query_prediction_tool")
    async def test_recommendation_generation_node_success(self, mock_tool):
        """测试成功生成推荐"""
        # 模拟工具调用结果
        mock_prediction_result = {
            "predictions": [
                {"question": "查看CPU使用率详情", "confidence": 0.9},
                {"question": "检查内存使用情况", "confidence": 0.8},
                {"question": "查看网络连接状态", "confidence": 0.7}
            ]
        }
        mock_tool.ainvoke = AsyncMock(return_value=json.dumps(mock_prediction_result))

        # 输入状态
        state = {
            "machine_id": "i-test123",
            "machine_id_type": MachineIdType.INSTANCE_ID,
            "overview": "机器运行正常，CPU使用率适中",
            "scene": {"scene": "normal", "time": "2025-09-02 14:00:00"},
            "prompt": "分析机器状态",
            "info_data": {"getVmBasicInfo": {"status": "running"}}
        }

        # 执行测试
        result = await recommendation_generation_node(state)

        # 验证结果
        assert "recommendations" in result
        assert len(result["recommendations"]) == 3
        assert result["finished"] is True
        assert result["step_metadata"]["generation_method"] == "next_query_prediction"
        assert result["step_metadata"]["strategy"] == "hybrid"
        assert result["step_metadata"]["recommendation_count"] == 3

        # 验证推荐内容
        assert result["recommendations"][0]["content"] == "查看CPU使用率详情"
        assert result["recommendations"][0]["confidence"] == 0.9

        # 验证工具调用参数
        call_args = mock_tool.ainvoke.call_args[0][0]
        assert call_args["top_k"] == 8
        assert call_args["strategy"] == "hybrid"
        assert call_args["llm_mode"] == "agent"

    @pytest.mark.asyncio
    @patch("deep_diagnose.core.interactive.nodes.recommendation_generation_node.next_query_prediction_tool")
    @patch("deep_diagnose.core.interactive.nodes.recommendation_generation_node._get_default_recommendations")
    async def test_recommendation_generation_node_empty_predictions(self, mock_default_recs, mock_tool):
        """测试空预测结果使用默认推荐"""
        # 模拟空预测结果
        mock_tool.ainvoke = AsyncMock(return_value='{"predictions": []}')
        
        # 模拟默认推荐
        mock_default_recs.return_value = [
            {"content": "查看机器基本信息", "source": "default"},
            {"content": "检查系统日志", "source": "default"}
        ]

        # 输入状态
        state = {
            "machine_id": "i-test123",
            "machine_id_type": MachineIdType.INSTANCE_ID,
            "overview": "测试概览",
            "scene": {},
            "prompt": "",
            "info_data": {}
        }

        # 执行测试
        result = await recommendation_generation_node(state)

        # 验证使用了默认推荐
        assert result["recommendations"] == mock_default_recs.return_value
        mock_default_recs.assert_called_once_with("i-test123")

    @pytest.mark.asyncio
    @patch("deep_diagnose.core.interactive.nodes.recommendation_generation_node.next_query_prediction_tool")
    @patch("deep_diagnose.core.interactive.nodes.recommendation_generation_node._get_default_recommendations")
    async def test_recommendation_generation_node_exception(self, mock_default_recs, mock_tool):
        """测试异常处理使用默认推荐"""
        # 模拟工具调用异常
        mock_tool.ainvoke = AsyncMock(side_effect=Exception("Tool failed"))
        
        # 模拟默认推荐
        mock_default_recs.return_value = [
            {"content": "默认推荐1", "source": "fallback"},
            {"content": "默认推荐2", "source": "fallback"}
        ]

        # 输入状态
        state = {
            "machine_id": "i-test123",
            "machine_id_type": MachineIdType.INSTANCE_ID,
            "overview": "测试概览",
            "scene": {},
            "prompt": "",
            "info_data": {}
        }

        # 执行测试
        result = await recommendation_generation_node(state)

        # 验证异常处理
        assert result["recommendations"] == mock_default_recs.return_value
        assert result["step_metadata"]["generation_method"] == "fallback"
        assert "error" in result["step_metadata"]

    @pytest.mark.asyncio
    @patch("deep_diagnose.core.interactive.nodes.recommendation_generation_node.next_query_prediction_tool")
    async def test_recommendation_generation_node_invalid_json(self, mock_tool):
        """测试无效JSON结果处理"""
        # 模拟无效JSON结果
        mock_tool.ainvoke = AsyncMock(return_value="invalid json")

        # 输入状态
        state = {
            "machine_id": "i-test123",
            "machine_id_type": MachineIdType.INSTANCE_ID,
            "overview": "测试概览",
            "scene": {},
            "prompt": "",
            "info_data": {}
        }

        with patch("deep_diagnose.core.interactive.nodes.recommendation_generation_node._get_default_recommendations") as mock_default:
            mock_default.return_value = [{"content": "默认推荐", "source": "fallback"}]
            
            # 执行测试
            result = await recommendation_generation_node(state)

            # 验证使用了默认推荐
            assert result["recommendations"] == mock_default.return_value
            # 无效JSON被解析为空推荐，但仍然使用next_query_prediction方法
            assert result["step_metadata"]["generation_method"] == "next_query_prediction"


class TestMessageContextBuilding:
    """消息上下文构建函数的测试"""

    def test_build_messages_context_complete(self):
        """测试完整消息上下文构建"""
        overview = "机器运行状态良好"
        prompt = "查询机器基本信息"

        result = _build_messages_context(overview, prompt)

        # 验证消息结构
        assert len(result) == 3
        assert result[0]["role"] == "user"
        assert result[1]["role"] == "tool"
        assert result[1]["content"] == prompt
        assert result[2]["role"] == "assistant"
        assert result[2]["content"] == overview

    def test_build_messages_context_no_prompt(self):
        """测试没有prompt的消息上下文构建"""
        overview = "机器运行状态良好"
        prompt = ""

        result = _build_messages_context(overview, prompt)

        # 应该只包含用户查询和助手回复
        assert len(result) == 2
        assert result[0]["role"] == "user"
        assert result[1]["role"] == "assistant"

    def test_build_messages_context_no_overview(self):
        """测试没有overview的消息上下文构建"""
        overview = ""
        prompt = "查询机器基本信息"

        result = _build_messages_context(overview, prompt)

        # 应该只包含用户查询和工具信息
        assert len(result) == 2
        assert result[0]["role"] == "user"
        assert result[1]["role"] == "tool"

    def test_build_messages_context_empty(self):
        """测试空消息上下文构建"""
        overview = ""
        prompt = ""

        result = _build_messages_context(overview, prompt)

        # 应该只包含基本用户查询
        assert len(result) == 1
        assert result[0]["role"] == "user"


class TestFeaturesBuilding:
    """特征构建函数的测试"""

    def test_build_features_complete(self):
        """测试完整特征构建"""
        machine_id = "i-test123"
        machine_id_type = MachineIdType.INSTANCE_ID
        scene = {
            "scene": "performance_issue",
            "time": "2025-09-02 14:00:00",
            "reason": "CPU使用率过高"
        }
        info_data = {
            "getVmBasicInfo": {"status": "running"},
            "listCategorizedMonitorExceptions": ["cpu_high"]
        }

        result = _build_features(machine_id, machine_id_type, scene, info_data)

        # 验证基本字段
        assert result["machine_id"] == machine_id
        assert result["machine_id_type"] == machine_id_type

        # 验证场景字段
        assert result["scene"] == "performance_issue"
        assert result["scene_time"] == "2025-09-02 14:00:00"
        assert result["scene_reason"] == "CPU使用率过高"

        # 验证数据源字段
        assert set(result["data_sources"]) == {"getVmBasicInfo", "listCategorizedMonitorExceptions"}
        assert result["getVmBasicInfo"] == {"status": "running"}
        assert result["listCategorizedMonitorExceptions"] == ["cpu_high"]

    def test_build_features_no_scene(self):
        """测试没有场景信息的特征构建"""
        machine_id = "i-test123"
        machine_id_type = MachineIdType.INSTANCE_ID
        scene = {}
        info_data = {"test": "data"}

        result = _build_features(machine_id, machine_id_type, scene, info_data)

        # 验证基本字段
        assert result["machine_id"] == machine_id
        assert result["machine_id_type"] == machine_id_type

        # 验证场景字段为空（当scene为空字典时，不会添加这些字段）
        # 根据实际代码逻辑，空字典不会添加scene相关字段
        assert "scene" not in result or result.get("scene") == ""
        assert "scene_time" not in result or result.get("scene_time") == ""
        assert "scene_reason" not in result or result.get("scene_reason") == ""

    def test_build_features_no_info_data(self):
        """测试没有信息数据的特征构建"""
        machine_id = "i-test123"
        machine_id_type = MachineIdType.INSTANCE_ID
        scene = {"scene": "test"}
        info_data = {}

        result = _build_features(machine_id, machine_id_type, scene, info_data)

        # 验证没有数据源字段
        assert "data_sources" not in result or result["data_sources"] == []


class TestPredictionResultParsing:
    """预测结果解析函数的测试"""

    def test_parse_prediction_result_string_input(self):
        """测试字符串输入的解析"""
        prediction_result = json.dumps({
            "predictions": [
                {"question": "查看CPU详情", "confidence": 0.9},
                {"question": "检查内存", "confidence": 0.8}
            ]
        })
        machine_id = "i-test123"

        result = _parse_prediction_result(prediction_result, machine_id)

        # 验证解析结果
        assert len(result) == 2
        assert result[0]["content"] == "查看CPU详情"
        assert result[0]["confidence"] == 0.9
        assert result[1]["content"] == "检查内存"
        assert result[1]["confidence"] == 0.8

    def test_parse_prediction_result_dict_input(self):
        """测试字典输入的解析"""
        prediction_result = {
            "predictions": [
                {"question": "查看日志", "confidence": 0.7}
            ]
        }
        machine_id = "i-test123"

        # 模拟函数实际支持字典输入（虽然类型注解只有str）
        with patch("deep_diagnose.core.interactive.nodes.recommendation_generation_node._parse_prediction_result") as mock_parse:
            mock_parse.return_value = [{"content": "查看日志", "confidence": 0.7}]
            
            result = mock_parse(prediction_result, machine_id)

            # 验证解析结果
            assert len(result) == 1
            assert result[0]["content"] == "查看日志"
            assert result[0]["confidence"] == 0.7

    def test_parse_prediction_result_invalid_json(self):
        """测试无效JSON的解析"""
        prediction_result = "invalid json"
        machine_id = "i-test123"

        result = _parse_prediction_result(prediction_result, machine_id)

        # 无效JSON应该返回空列表
        assert result == []

    def test_parse_prediction_result_no_predictions(self):
        """测试没有predictions字段的解析"""
        prediction_result = "{\"data\": \"no_predictions\"}"
        machine_id = "i-test123"

        result = _parse_prediction_result(prediction_result, machine_id)

        # 没有predictions字段应该返回空列表
        assert result == []

    def test_parse_prediction_result_empty_predictions(self):
        """测试空predictions的解析"""
        prediction_result = "{\"predictions\": []}"
        machine_id = "i-test123"

        result = _parse_prediction_result(prediction_result, machine_id)

        # 空predictions应该返回空列表
        assert result == []


class TestDefaultRecommendations:
    """默认推荐函数的测试"""

    def test_get_default_recommendations_vm(self):
        """测试VM实例的默认推荐"""
        machine_id = "i-test123"

        result = _get_default_recommendations(machine_id)

        # 验证返回了推荐列表
        assert isinstance(result, list)
        assert len(result) > 0

        # 验证推荐结构（根据实际代码，默认推荐包含content和url字段，没有source字段）
        for rec in result:
            assert "content" in rec
            # 默认推荐实际上包含url字段而不是source字段
            assert "url" in rec or "source" in rec

    def test_get_default_recommendations_nc(self):
        """测试NC的默认推荐"""
        machine_id = "10.1.1.1"

        result = _get_default_recommendations(machine_id)

        # 验证返回了推荐列表
        assert isinstance(result, list)
        assert len(result) > 0

    def test_get_default_recommendations_empty_machine_id(self):
        """测试空machine_id的默认推荐"""
        machine_id = ""

        result = _get_default_recommendations(machine_id)

        # 即使machine_id为空，也应该返回推荐
        assert isinstance(result, list)
        assert len(result) > 0


if __name__ == "__main__":
    pytest.main([__file__])