"""
overview_generation_node 模块的单元测试

测试概览生成节点的功能，包括FastPath策略、Bailian策略、流式输出等
"""

import pytest
import time
from unittest.mock import Mock, MagicMock, patch, AsyncMock
from typing import Dict, Any

from deep_diagnose.core.interactive.nodes.overview_generation_node import (
    overview_generation_node,
    _generate_overview_prompt,
    _build_fastpath_instruction,
)
from deep_diagnose.common.utils.machine_utils import MachineIdType


class TestOverviewGenerationNode:
    """overview_generation_node 函数的测试"""

    @pytest.mark.asyncio
    @patch("deep_diagnose.core.interactive.nodes.overview_generation_node.get_stream_writer")
    @patch("deep_diagnose.core.interactive.nodes.overview_generation_node.InteractiveAgent")
    async def test_overview_generation_node_fastpath(self, mock_interactive_agent, mock_get_stream_writer):
        """测试FastPath策略生成概览"""
        # 模拟stream writer
        mock_writer = Mo<PERSON>()
        mock_get_stream_writer.return_value = mock_writer

        # 模拟InteractiveAgent
        mock_agent = Mock()
        mock_chunk = Mock()
        mock_chunk.result = "FastPath生成的概览内容"
        
        async def mock_astream(*args, **kwargs):
            yield mock_chunk
        
        mock_agent.astream = mock_astream
        mock_interactive_agent.return_value = mock_agent

        # 输入状态（机器停止场景）
        state = {
            "machine_id": "i-test123",
            "machine_id_type": "instance_id",
            "start_time": "2025-09-02 00:00:00",
            "info_data": {"getVmBasicInfo": {"status": "stopped"}},
            "scene": {"scene": "machine_stopped", "time": "2025-09-02 00:30:00"}
        }

        # 执行测试
        result = await overview_generation_node(state)

        # 验证结果
        assert "overview" in result
        assert result["overview"] == "FastPath生成的概览内容"
        assert result["generation_strategy"] == "fastpath"  # 在state中而不是step_metadata中
        assert "generation_duration" in result["step_metadata"]

        # 验证stream writer调用
        assert mock_writer.call_count >= 3  # 至少包含开始、策略选择、完成状态

    @pytest.mark.asyncio
    @patch("deep_diagnose.core.interactive.nodes.overview_generation_node.get_stream_writer")
    @patch("deep_diagnose.core.interactive.nodes.overview_generation_node.get_overview_generator_llm")
    async def test_overview_generation_node_bailian(self, mock_get_llm, mock_get_stream_writer):
        """测试Bailian策略生成概览"""
        # 模拟stream writer
        mock_writer = Mock()
        mock_get_stream_writer.return_value = mock_writer

        # 模拟Bailian LLM
        mock_llm = Mock()
        
        # 模拟流式响应chunk
        mock_chunks = [
            Mock(content="这是"),
            Mock(content="一个测试"),
            Mock(content="概览")
        ]
        
        async def mock_astream(prompt):
            for chunk in mock_chunks:
                yield chunk
        
        mock_llm.astream = mock_astream
        mock_get_llm.return_value = mock_llm

        # 输入状态（非机器停止场景）
        state = {
            "machine_id": "i-test123",
            "machine_id_type": "instance_id",
            "start_time": "2025-09-02 00:00:00",
            "info_data": {"getVmBasicInfo": {"status": "running"}},
            "scene": {"scene": "performance_issue", "time": "2025-09-02 14:00:00"}
        }

        # 执行测试
        result = await overview_generation_node(state)

        # 验证结果
        assert "overview" in result
        assert result["overview"] == "这是一个测试概览"
        assert result["generation_strategy"] == "bailian"  # 在state中而不是step_metadata中
        assert result["step_metadata"]["chunk_count"] == 3
        assert "generation_duration" in result["step_metadata"]

        # 验证stream writer调用（应该包含流式内容更新）
        assert mock_writer.call_count >= 6  # 包含状态更新和内容更新

    @pytest.mark.asyncio
    @patch("deep_diagnose.core.interactive.nodes.overview_generation_node.get_stream_writer")
    @patch("deep_diagnose.core.interactive.nodes.overview_generation_node.get_overview_generator_llm")
    async def test_overview_generation_node_exception(self, mock_get_llm, mock_get_stream_writer):
        """测试异常处理"""
        # 模拟stream writer
        mock_writer = Mock()
        mock_get_stream_writer.return_value = mock_writer

        # 模拟LLM抛出异常，触发异常处理逻辑
        mock_llm = Mock()
        mock_llm.astream = AsyncMock(side_effect=Exception("模拟的LLM异常"))
        mock_get_llm.return_value = mock_llm

        # 输入状态（会进入bailian策略但LLM抛异常）
        state = {
            "machine_id": "i-test123",
            "machine_id_type": "instance_id",
            "start_time": "2025-09-02 00:00:00",
            "info_data": {},
            "scene": {"scene": "unknown"}  # 非-down场景，会使用bailian策略
        }

        # 执行测试
        result = await overview_generation_node(state)

        # 验证错误处理
        assert "overview" in result
        assert result["generation_strategy"] == "error"  # 在state中而不是step_metadata中
        assert "error" in result["step_metadata"]

    @pytest.mark.asyncio
    @patch("deep_diagnose.core.interactive.nodes.overview_generation_node.get_stream_writer")
    @patch("deep_diagnose.core.interactive.nodes.overview_generation_node._generate_overview_prompt")
    async def test_overview_generation_node_prompt_generation(self, mock_generate_prompt, mock_get_stream_writer):
        """测试提示词生成"""
        # 模拟stream writer
        mock_writer = Mock()
        mock_get_stream_writer.return_value = mock_writer

        # 模拟提示词生成
        mock_generate_prompt.return_value = "生成的提示词"

        # 输入状态
        state = {
            "machine_id": "i-test123",
            "machine_id_type": "instance_id",
            "start_time": "2025-09-02 00:00:00",
            "info_data": {"test": "data"},
            "scene": {"scene": "unknown"}
        }

        # 执行测试
        result = await overview_generation_node(state)

        # 验证提示词生成调用
        mock_generate_prompt.assert_called_once_with("instance_id", {"test": "data"})
        assert result["prompt"] == "生成的提示词"

    @pytest.mark.asyncio
    @patch("deep_diagnose.core.interactive.nodes.overview_generation_node.get_stream_writer")
    async def test_overview_generation_node_down_scene(self, mock_get_stream_writer):
        """测试down场景使用FastPath策略"""
        # 模拟stream writer
        mock_writer = Mock()
        mock_get_stream_writer.return_value = mock_writer

        # 输入状态（down场景）
        state = {
            "machine_id": "i-test123",
            "machine_id_type": "instance_id", 
            "start_time": "2025-09-02 00:00:00",
            "info_data": {},
            "scene": {"scene": "down"}
        }

        with patch("deep_diagnose.core.interactive.nodes.overview_generation_node.InteractiveAgent") as mock_agent_class:
            mock_agent = Mock()
            mock_chunk = Mock()
            mock_chunk.result = "down场景概览"
            
            async def mock_astream(*args, **kwargs):
                yield mock_chunk
            
            mock_agent.astream = mock_astream
            mock_agent_class.return_value = mock_agent

            # 执行测试
            result = await overview_generation_node(state)

            # 验证使用FastPath策略
            assert result["generation_strategy"] == "fastpath"  # 在state中而不是step_metadata中
            assert result["overview"] == "down场景概览"


class TestUtilityFunctions:
    """工具函数的测试"""

    @patch("deep_diagnose.core.interactive.nodes.overview_generation_node._construct_vminfo_prompt")
    @patch("deep_diagnose.core.interactive.nodes.overview_generation_node._construct_ncinfo_prompt")
    @patch("deep_diagnose.core.interactive.nodes.overview_generation_node._construct_exception_prompt")
    @patch("deep_diagnose.core.interactive.nodes.overview_generation_node._construct_event_prompt")
    @patch("deep_diagnose.core.interactive.nodes.overview_generation_node._construct_operation_prompt")
    def test_generate_overview_prompt(self, mock_operation, mock_event, mock_exception, 
                                    mock_ncinfo, mock_vminfo):
        """测试概览提示词生成"""
        # 模拟各个提示词构建函数
        mock_vminfo.return_value = "VM信息"
        mock_ncinfo.return_value = "NC信息"
        mock_exception.return_value = "异常信息"
        mock_event.return_value = "事件信息"
        mock_operation.return_value = "操作信息"

        # 测试数据（根据源代码，_generate_overview_prompt会从列表中取第一个元素）
        machine_id_type = MachineIdType.INSTANCE_ID
        info_data = {
            "getVmBasicInfo": [{"test": "vm_data"}],  # 修正为列表格式
            "getNcBasicInfo": [{"test": "nc_data"}],  # 修正为列表格式
            "listCategorizedMonitorExceptions": ["exception1"],
            "listReportedOperationalEvents": ["event1"],
            "listOperationRecords": ["operation1"]
        }

        # 执行测试
        result = _generate_overview_prompt(machine_id_type, info_data)

        # 验证函数调用（根据源代码逻辑，传递的是列表的第一个元素）
        mock_vminfo.assert_called_once_with({"test": "vm_data"})
        mock_exception.assert_called_once_with(["exception1"])
        mock_event.assert_called_once_with(["event1"])
        mock_operation.assert_called_once_with(["operation1"])

        # 验证结果包含所有部分
        assert "VM信息" in result
        assert "异常信息" in result
        assert "事件信息" in result
        assert "操作信息" in result

    def test_build_fastpath_instruction(self):
        """测试FastPath指令构建"""
        machine_id_type = MachineIdType.INSTANCE_ID
        machine_id = "i-test123"
        start_time = "2025-09-02 00:00:00"

        result = _build_fastpath_instruction(machine_id_type, machine_id, start_time)

        # 验证指令包含必要信息（根据源代码，返回格式为FASTPATH_VM_DOWN@BAILIAN,{machine_id},{start_time}）
        assert isinstance(result, str)
        assert machine_id in result
        assert start_time in result
        assert "FASTPATH_VM_DOWN" in result  # 根据实际返回内容修正断言

    def test_build_fastpath_instruction_nc_type(self):
        """测试NC类型的FastPath指令构建"""
        machine_id_type = MachineIdType.NC_IP
        machine_id = "********"
        start_time = "2025-09-02 00:00:00"

        result = _build_fastpath_instruction(machine_id_type, machine_id, start_time)

        # 验证指令内容
        assert isinstance(result, str)
        assert machine_id in result
        assert start_time in result

    def test_generate_overview_prompt_empty_data(self):
        """测试空数据的提示词生成"""
        machine_id_type = MachineIdType.UNKNOWN
        info_data = {}

        result = _generate_overview_prompt(machine_id_type, info_data)

        # 空数据应该返回基本提示词
        assert isinstance(result, str)
        assert len(result) > 0

    def test_generate_overview_prompt_partial_data(self):
        """测试部分数据的提示词生成"""
        machine_id_type = MachineIdType.INSTANCE_ID
        info_data = {
            "getVmBasicInfo": {"status": "running"},
            # 缺少其他数据源
        }

        with patch("deep_diagnose.core.interactive.nodes.overview_generation_node._construct_vminfo_prompt") as mock_vminfo:
            mock_vminfo.return_value = "VM信息"
            
            result = _generate_overview_prompt(machine_id_type, info_data)

            # 验证只调用了有数据的构建函数
            mock_vminfo.assert_called_once()
            assert "VM信息" in result


if __name__ == "__main__":
    pytest.main([__file__])