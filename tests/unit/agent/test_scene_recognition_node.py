"""
scene_recognition_node 模块的单元测试

测试场景识别节点的功能，包括正常识别、异常处理等
"""

import pytest
from unittest.mock import Mock, patch
from typing import Dict, Any

from deep_diagnose.core.interactive.nodes.scene_recognition_node import scene_recognition_node


class TestSceneRecognitionNode:
    """scene_recognition_node 函数的测试"""

    @pytest.mark.asyncio
    @patch("deep_diagnose.core.interactive.nodes.scene_recognition_node.recognize_scene")
    async def test_scene_recognition_node_success(self, mock_recognize_scene):
        """测试成功场景识别"""
        # 模拟场景识别结果
        mock_scene_result = {
            "scene": "machine_stopped",
            "time": "2025-09-02 00:30:00",
            "reason": "实例已停止运行"
        }
        mock_recognize_scene.return_value = mock_scene_result

        # 输入状态
        state = {
            "machine_id": "i-test123",
            "info_data": {
                "getVmBasicInfo": {"status": "stopped"},
                "listCategorizedMonitorExceptions": []
            }
        }

        # 执行测试
        result = await scene_recognition_node(state)

        # 验证结果
        assert "scene" in result
        assert result["scene"]["scene"] == "machine_stopped"
        assert result["scene"]["time"] == "2025-09-02 00:30:00"
        assert result["scene"]["reason"] == "实例已停止运行"
        
        # 验证步骤元数据
        assert result["step_metadata"]["scene_identified"] is True
        assert result["step_metadata"]["scene_type"] == "machine_stopped"
        assert result["step_metadata"]["recognition_method"] == "legacy_compatible"

        # 验证调用参数
        mock_recognize_scene.assert_called_once_with(state["info_data"])

    @pytest.mark.asyncio
    @patch("deep_diagnose.core.interactive.nodes.scene_recognition_node.recognize_scene")
    async def test_scene_recognition_node_no_scene(self, mock_recognize_scene):
        """测试无法识别场景的情况"""
        # 模拟无场景识别结果
        mock_recognize_scene.return_value = None

        # 输入状态
        state = {
            "machine_id": "i-test123",
            "info_data": {}
        }

        # 执行测试
        result = await scene_recognition_node(state)

        # 验证结果
        assert "scene" in result
        assert result["scene"]["scene"] == "unknown"
        assert result["scene"]["time"] == ""
        assert result["scene"]["reason"] == "无法识别场景类型"
        
        # 验证步骤元数据
        assert result["step_metadata"]["scene_identified"] is False
        assert result["step_metadata"]["scene_type"] == "unknown"

    @pytest.mark.asyncio
    @patch("deep_diagnose.core.interactive.nodes.scene_recognition_node.recognize_scene")
    async def test_scene_recognition_node_empty_scene_result(self, mock_recognize_scene):
        """测试空场景识别结果"""
        # 模拟空场景识别结果
        mock_recognize_scene.return_value = {}

        # 输入状态
        state = {
            "machine_id": "i-test123",
            "info_data": {"some": "data"}
        }

        # 执行测试
        result = await scene_recognition_node(state)

        # 验证结果
        assert result["scene"]["scene"] == "unknown"
        assert result["scene"]["time"] == ""
        assert result["scene"]["reason"] == "无法识别场景类型"  # 空字典会被当作None处理

    @pytest.mark.asyncio
    @patch("deep_diagnose.core.interactive.nodes.scene_recognition_node.recognize_scene")
    async def test_scene_recognition_node_partial_scene_result(self, mock_recognize_scene):
        """测试部分场景识别结果"""
        # 模拟部分场景识别结果
        mock_scene_result = {
            "scene": "network_issue"
            # 缺少 time 和 reason
        }
        mock_recognize_scene.return_value = mock_scene_result

        # 输入状态
        state = {
            "machine_id": "i-test123",
            "info_data": {"network": "error"}
        }

        # 执行测试
        result = await scene_recognition_node(state)

        # 验证结果
        assert result["scene"]["scene"] == "network_issue"
        assert result["scene"]["time"] == ""  # 默认值
        assert result["scene"]["reason"] == ""  # 默认值
        
        # 验证步骤元数据
        assert result["step_metadata"]["scene_identified"] is True
        assert result["step_metadata"]["scene_type"] == "network_issue"

    @pytest.mark.asyncio
    @patch("deep_diagnose.core.interactive.nodes.scene_recognition_node.recognize_scene")
    async def test_scene_recognition_node_exception(self, mock_recognize_scene):
        """测试场景识别异常处理"""
        # 模拟场景识别异常
        mock_recognize_scene.side_effect = Exception("Scene recognition failed")

        # 输入状态
        state = {
            "machine_id": "i-test123",
            "info_data": {"test": "data"}
        }

        # 执行测试
        result = await scene_recognition_node(state)

        # 验证错误处理
        assert result["scene"]["scene"] == "error"
        assert result["scene"]["confidence"] == 0.0
        assert "识别异常" in result["scene"]["reason"]
        
        # 验证步骤元数据
        assert result["step_metadata"]["scene_identified"] is False
        assert "error" in result["step_metadata"]
        assert result["step_metadata"]["recognition_method"] == "error_fallback"

    @pytest.mark.asyncio
    async def test_scene_recognition_node_missing_info_data(self):
        """测试缺少info_data的情况"""
        # 输入状态（缺少info_data）
        state = {
            "machine_id": "i-test123"
        }

        # 执行测试
        result = await scene_recognition_node(state)

        # 验证结果（应该使用空字典作为默认值）
        assert "scene" in result
        assert "step_metadata" in result
        assert result["step_metadata"]["data_sources"] == []

    @pytest.mark.asyncio
    async def test_scene_recognition_node_missing_machine_id(self):
        """测试缺少machine_id的情况"""
        # 输入状态（缺少machine_id）
        state = {
            "info_data": {"test": "data"}
        }

        # 执行测试
        result = await scene_recognition_node(state)

        # 验证结果（应该使用空字符串作为默认值）
        assert "scene" in result
        assert "step_metadata" in result

    @pytest.mark.asyncio
    @patch("deep_diagnose.core.interactive.nodes.scene_recognition_node.recognize_scene")
    async def test_scene_recognition_node_data_sources_metadata(self, mock_recognize_scene):
        """测试数据源元数据的正确性"""
        mock_recognize_scene.return_value = {"scene": "test"}

        # 输入状态
        state = {
            "machine_id": "i-test123",
            "info_data": {
                "getVmBasicInfo": {},
                "listCategorizedMonitorExceptions": [],
                "listOperationRecords": []
            }
        }

        # 执行测试
        result = await scene_recognition_node(state)

        # 验证数据源元数据
        expected_sources = ["getVmBasicInfo", "listCategorizedMonitorExceptions", "listOperationRecords"]
        assert set(result["step_metadata"]["data_sources"]) == set(expected_sources)

    @pytest.mark.asyncio
    @patch("deep_diagnose.core.interactive.nodes.scene_recognition_node.recognize_scene")
    async def test_scene_recognition_node_complex_scene_result(self, mock_recognize_scene):
        """测试复杂场景识别结果"""
        # 模拟复杂场景识别结果
        mock_scene_result = {
            "scene": "performance_degradation",
            "time": "2025-09-02 14:25:30",
            "reason": "CPU使用率持续高于90%，内存使用率达到95%",
            "confidence": 0.85,
            "extra_field": "should_be_ignored"  # 额外字段应该被忽略
        }
        mock_recognize_scene.return_value = mock_scene_result

        # 输入状态
        state = {
            "machine_id": "i-test123",
            "info_data": {"monitoring": "data"}
        }

        # 执行测试
        result = await scene_recognition_node(state)

        # 验证结果（只包含预期字段）
        assert result["scene"]["scene"] == "performance_degradation"
        assert result["scene"]["time"] == "2025-09-02 14:25:30"
        assert result["scene"]["reason"] == "CPU使用率持续高于90%，内存使用率达到95%"
        
        # 验证不包含额外字段
        assert "confidence" not in result["scene"]
        assert "extra_field" not in result["scene"]


if __name__ == "__main__":
    pytest.main([__file__])