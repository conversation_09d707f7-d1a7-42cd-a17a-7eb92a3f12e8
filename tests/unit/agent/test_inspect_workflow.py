"""
inspect_workflow 模块的单元测试

测试 FilteredLangfuseCallback、工作流创建、事件转换等功能
"""

import pytest
import asyncio
from unittest.mock import Mock, MagicMock, patch, AsyncMock
from typing import Dict, Any, Optional, AsyncGenerator

from deep_diagnose.core.interactive.workflows.inspect_workflow import (
    FilteredLangfuseCallback,
    create_inspect_workflow,
    create_inspect_workflow_with_memory,
    create_langfuse_handler,
    stream_inspect_workflow,
    _convert_workflow_event_to_inspect_event,
    _process_custom_stream_event,
    _process_updates_stream_event,
    _extract_recommendation_summary,
    _extract_data_summary,
    _extract_time_range,
    _get_step_progress_message,
)
from deep_diagnose.core.events.inspect_event import InspectEvent
from deep_diagnose.common.utils.machine_utils import MachineIdType


class TestFilteredLangfuseCallback:
    """FilteredLangfuseCallback 类的测试"""

    @patch("deep_diagnose.core.interactive.workflows.inspect_workflow.LangchainCallbackHandler.__init__")
    def test_init(self, mock_init):
        """测试 FilteredLangfuseCallback 初始化"""
        mock_init.return_value = None
        callback = FilteredLangfuseCallback(
            public_key="test_key",
            secret_key="test_secret",
            host="http://test.com"
        )
        assert callback is not None

    @patch("deep_diagnose.core.interactive.workflows.inspect_workflow.LangchainCallbackHandler.on_chain_start")
    def test_on_chain_start_success(self, mock_super_method):
        """测试 on_chain_start 成功场景"""
        callback = FilteredLangfuseCallback.__new__(FilteredLangfuseCallback)
        callback._filter_data = Mock(return_value={"filtered": "data"})
        
        serialized = {"test": "serialized"}
        inputs = {"test": "inputs"}
        
        callback.on_chain_start(serialized, inputs)
        
        callback._filter_data.assert_called_once_with(inputs)
        mock_super_method.assert_called_once_with(serialized, {"filtered": "data"})

    @patch("deep_diagnose.core.interactive.workflows.inspect_workflow.LangchainCallbackHandler.on_chain_start")
    @patch("deep_diagnose.core.interactive.workflows.inspect_workflow.logger")
    def test_on_chain_start_exception(self, mock_logger, mock_super_method):
        """测试 on_chain_start 异常场景"""
        callback = FilteredLangfuseCallback.__new__(FilteredLangfuseCallback)
        callback._filter_data = Mock(side_effect=Exception("Test exception"))
        
        serialized = {"test": "serialized"}
        inputs = {"test": "inputs"}
        
        callback.on_chain_start(serialized, inputs)
        
        mock_logger.warning.assert_called_once()
        mock_super_method.assert_called_with(serialized, {"filtered": True})

    def test_filter_data_dict(self):
        """测试 _filter_data 处理字典"""
        callback = FilteredLangfuseCallback.__new__(FilteredLangfuseCallback)
        callback._filter_dict = Mock(return_value={"filtered": "dict"})
        
        data = {"test": "data"}
        result = callback._filter_data(data)
        
        callback._filter_dict.assert_called_once_with(data)
        assert result == {"filtered": "dict"}

    def test_filter_data_list(self):
        """测试 _filter_data 处理列表"""
        callback = FilteredLangfuseCallback.__new__(FilteredLangfuseCallback)
        
        data = ["item1", "item2"]
        result = callback._filter_data(data)
        
        assert result == ["item1", "item2"]

    def test_filter_data_none(self):
        """测试 _filter_data 处理 None"""
        callback = FilteredLangfuseCallback.__new__(FilteredLangfuseCallback)
        
        result = callback._filter_data(None)
        
        assert result is None

    def test_filter_dict_skip_fields(self):
        """测试 _filter_dict 跳过特定字段"""
        callback = FilteredLangfuseCallback.__new__(FilteredLangfuseCallback)
        callback._filter_data = Mock(side_effect=lambda x: x)
        
        data = {
            "normal_field": "value",
            "langfuse_handler": "should_skip",
            "callbacks": "should_skip",
            "handler": "should_skip"
        }
        
        result = callback._filter_dict(data)
        
        assert "normal_field" in result
        assert "langfuse_handler" not in result
        assert "callbacks" not in result
        assert "handler" not in result

    def test_filter_dict_enum_handling(self):
        """测试 _filter_dict 处理枚举类型"""
        callback = FilteredLangfuseCallback.__new__(FilteredLangfuseCallback)
        
        # 模拟枚举对象
        mock_enum = Mock()
        mock_enum.value = "enum_value"
        mock_enum.name = "ENUM_NAME"
        
        data = {"enum_field": mock_enum}
        
        result = callback._filter_dict(data)
        
        assert result["enum_field"] == "enum_value"


class TestWorkflowCreation:
    """工作流创建函数的测试"""

    @patch("deep_diagnose.core.interactive.workflows.inspect_workflow.StateGraph")
    @patch("deep_diagnose.core.interactive.workflows.inspect_workflow.data_collection_node")
    @patch("deep_diagnose.core.interactive.workflows.inspect_workflow.scene_recognition_node")
    @patch("deep_diagnose.core.interactive.workflows.inspect_workflow.overview_generation_node")
    @patch("deep_diagnose.core.interactive.workflows.inspect_workflow.recommendation_generation_node")
    def test_create_inspect_workflow(self, mock_rec_node, mock_overview_node, 
                                   mock_scene_node, mock_data_node, mock_state_graph):
        """测试创建无内存工作流"""
        mock_builder = Mock()
        mock_graph = Mock()
        mock_builder.compile.return_value = mock_graph
        mock_state_graph.return_value = mock_builder
        
        result = create_inspect_workflow()
        
        # 验证节点添加
        assert mock_builder.add_node.call_count == 4
        mock_builder.add_node.assert_any_call("data_collection", mock_data_node)
        mock_builder.add_node.assert_any_call("scene_recognition", mock_scene_node)
        mock_builder.add_node.assert_any_call("overview_generation", mock_overview_node)
        mock_builder.add_node.assert_any_call("recommendation_generation", mock_rec_node)
        
        # 验证边添加
        assert mock_builder.add_edge.call_count == 5
        
        # 验证编译
        mock_builder.compile.assert_called_once()
        assert result == mock_graph

    @patch("deep_diagnose.core.interactive.workflows.inspect_workflow.StateGraph")
    @patch("deep_diagnose.core.interactive.workflows.inspect_workflow.MemorySaver")
    def test_create_inspect_workflow_with_memory(self, mock_memory_saver, mock_state_graph):
        """测试创建带内存工作流"""
        mock_builder = Mock()
        mock_graph = Mock()
        mock_checkpointer = Mock()
        mock_builder.compile.return_value = mock_graph
        mock_state_graph.return_value = mock_builder
        mock_memory_saver.return_value = mock_checkpointer
        
        result = create_inspect_workflow_with_memory()
        
        # 验证使用了 checkpointer
        mock_builder.compile.assert_called_once_with(checkpointer=mock_checkpointer)
        assert result == mock_graph


class TestLangfuseHandler:
    """Langfuse 处理器相关函数的测试"""

    @patch("deep_diagnose.core.interactive.workflows.inspect_workflow.get_config")
    @patch("deep_diagnose.core.interactive.workflows.inspect_workflow.FilteredLangfuseCallback")
    def test_create_langfuse_handler_success(self, mock_callback_class, mock_get_config):
        """测试成功创建 Langfuse 处理器"""
        # 模拟配置
        mock_config = Mock()
        mock_config.observability.langfuse.public_key = "test_public_key"
        mock_config.observability.langfuse.secret_key = "test_secret_key"
        mock_config.observability.langfuse.endpoint = "http://test.com"
        mock_get_config.return_value = mock_config
        
        mock_handler = Mock()
        mock_callback_class.return_value = mock_handler
        
        result = create_langfuse_handler(
            session_id="test_session",
            user_id="test_user",
            trace_name="test_trace"
        )
        
        mock_callback_class.assert_called_once_with(
            public_key="test_public_key",
            secret_key="test_secret_key",
            host="http://test.com",
            session_id="test_session",
            user_id="test_user",
            trace_name="test_trace"
        )
        assert result == mock_handler

    @patch("deep_diagnose.core.interactive.workflows.inspect_workflow.get_config")
    def test_create_langfuse_handler_missing_config(self, mock_get_config):
        """测试缺少配置时返回 None"""
        mock_config = Mock()
        mock_config.observability.langfuse.public_key = None
        mock_config.observability.langfuse.secret_key = None
        mock_get_config.return_value = mock_config
        
        result = create_langfuse_handler()
        
        assert result is None

    @patch("deep_diagnose.core.interactive.workflows.inspect_workflow.get_config")
    def test_create_langfuse_handler_exception(self, mock_get_config):
        """测试异常情况返回 None"""
        mock_get_config.side_effect = Exception("Config error")
        
        result = create_langfuse_handler()
        
        assert result is None


class TestEventConversion:
    """事件转换相关函数的测试"""

    def test_convert_workflow_event_to_inspect_event_custom(self):
        """测试转换自定义流式事件"""
        inspect_event = InspectEvent()
        workflow_event = ("custom", {
            "type": "overview_status",
            "status": "completed",
            "message": "生成完成"
        })
        
        result = _convert_workflow_event_to_inspect_event(workflow_event, inspect_event)
        
        assert result is not None
        assert result.overview == "生成完成"

    def test_convert_workflow_event_to_inspect_event_updates(self):
        """测试转换状态更新事件"""
        inspect_event = InspectEvent()
        workflow_event = ("updates", {
            "node1": {
                "overview": "测试概览",
                "finished": True
            }
        })
        
        result = _convert_workflow_event_to_inspect_event(workflow_event, inspect_event)
        
        assert result is not None
        assert result.overview == "测试概览"
        assert result.finished is True

    def test_process_custom_stream_event_overview_status(self):
        """测试处理概览状态自定义事件"""
        inspect_event = InspectEvent()
        event_data = {
            "type": "overview_status",
            "status": "generating",
            "message": "正在生成概览...",
            "strategy": "llm"
        }
        
        result = _process_custom_stream_event(event_data, inspect_event)
        
        assert result is True
        assert inspect_event.overview == "正在生成概览..."
        assert hasattr(inspect_event, "generation_strategy")

    def test_process_custom_stream_event_overview_content(self):
        """测试处理概览内容自定义事件"""
        inspect_event = InspectEvent()
        event_data = {
            "type": "overview_content",
            "content": "新内容",
            "overview": "完整概览内容",
            "chunk_count": 5
        }
        
        result = _process_custom_stream_event(event_data, inspect_event)
        
        assert result is True
        assert inspect_event.overview == "完整概览内容"
        assert hasattr(inspect_event, "chunk_count")

    def test_process_updates_stream_event(self):
        """测试处理状态更新事件"""
        inspect_event = InspectEvent()
        event_data = {
            "node1": {
                "overview": "更新的概览",
                "recommendations": [{"content": "建议1"}],
                "current_step": "data_collection"
            }
        }
        
        result = _process_updates_stream_event(event_data, inspect_event)
        
        assert result is True
        assert inspect_event.overview == "更新的概览"


class TestUtilityFunctions:
    """工具函数的测试"""

    def test_extract_recommendation_summary(self):
        """测试提取推荐摘要"""
        recommendations = [
            {
                "content": "展示一下宿主机的详细信息 (2025-09-02 00:00:00 - 2025-09-02 01:00:00)",
                "source": "test_source",
                "raw_content": "展示一下宿主机的详细信息 (2025-09-02 00:00:00 - 2025-09-02 01:00:00)",
                "confidence": 0.95,
                "score": 0.876543
            },
            "简单字符串推荐"
        ]
        
        result = _extract_recommendation_summary(recommendations)
        
        assert len(result) == 2
        assert len(result[0]["content"]) <= 100
        assert result[0]["source"] == "test_source"
        assert "time_range" in result[0]
        assert result[0]["time_range"] == "2025-09-02 00:00:00 - 2025-09-02 01:00:00"
        assert result[0]["score"] == 0.88  # 四舍五入到2位小数
        assert result[1]["source"] == "text"

    def test_extract_time_range_with_range(self):
        """测试提取时间范围（包含区间）"""
        content = "展示一下宿主机的详细信息 (2025-09-02 00:00:00 - 2025-09-02 01:00:00)"
        
        result = _extract_time_range(content)
        
        assert result == "2025-09-02 00:00:00 - 2025-09-02 01:00:00"

    def test_extract_time_range_single_time(self):
        """测试提取单个时间点"""
        content = "在 2025-09-02 12:30:45 发生的事件"
        
        result = _extract_time_range(content)
        
        assert result == "2025-09-02 12:30:45"

    def test_extract_time_range_no_time(self):
        """测试没有时间信息的情况"""
        content = "没有时间信息的内容"
        
        result = _extract_time_range(content)
        
        assert result == ""

    def test_extract_data_summary(self):
        """测试提取数据摘要"""
        info_data = {
            "metrics": [{"cpu": 80}, {"memory": 60}],
            "logs": {"error": "test error", "warning": "test warning"},
            "events": [1, 2, 3, 4, 5]
        }
        
        result = _extract_data_summary(info_data)
        
        assert "data_sources" in result
        assert "total_size" in result
        assert result["metrics_count"] == 2
        assert result["logs_keys"] == 2
        assert result["events_count"] == 5

    def test_get_step_progress_message(self):
        """测试获取步骤进度消息"""
        assert _get_step_progress_message("start") == "正在初始化..."
        assert _get_step_progress_message("data_collection") == "正在收集机器信息..."
        assert _get_step_progress_message("unknown_step") == "正在执行 unknown_step..."


class TestStreamInspectWorkflow:
    """stream_inspect_workflow 函数的测试"""

    @pytest.mark.asyncio
    @patch("deep_diagnose.core.interactive.workflows.inspect_workflow.create_inspect_workflow")
    @patch("deep_diagnose.core.interactive.workflows.inspect_workflow.create_langfuse_handler")
    @patch("deep_diagnose.core.interactive.workflows.inspect_workflow.MachineIdUtil")
    async def test_stream_inspect_workflow_success(self, mock_machine_util, 
                                                 mock_create_handler, mock_create_workflow):
        """测试流式执行工作流成功场景"""
        # 模拟工作流
        mock_workflow = Mock()
        mock_create_workflow.return_value = mock_workflow
        
        # 模拟 Langfuse 处理器
        mock_handler = Mock()
        mock_create_handler.return_value = mock_handler
        
        # 模拟机器ID工具
        mock_machine_util.get_machine_id_type.return_value = MachineIdType.INSTANCE_ID
        
        # 模拟工作流流式执行
        async def mock_astream(state, config=None, stream_mode=None):
            yield ("updates", {"node1": {"overview": "测试概览", "finished": True}})
        
        mock_workflow.astream = mock_astream
        
        # 执行测试
        events = []
        async for event in stream_inspect_workflow(
            machine_id="i-test123",
            start_time="2025-09-02 00:00:00",
            end_time="2025-09-02 01:00:00",
            session_id="test_session"
        ):
            events.append(event)
        
        # 验证结果
        assert len(events) >= 2  # 至少有初始事件和完成事件
        assert events[0].machine_id == "i-test123"
        assert events[-1].finished is True

    @pytest.mark.asyncio
    @patch("deep_diagnose.core.interactive.workflows.inspect_workflow.create_inspect_workflow")
    @patch("deep_diagnose.core.interactive.workflows.inspect_workflow.create_langfuse_handler")
    @patch("deep_diagnose.core.interactive.workflows.inspect_workflow.MachineIdUtil")
    async def test_stream_inspect_workflow_with_external_handler(self, mock_machine_util,
                                                               mock_create_handler, mock_create_workflow):
        """测试使用外部 Langfuse 处理器"""
        # 模拟工作流
        mock_workflow = Mock()
        mock_create_workflow.return_value = mock_workflow
        
        # 模拟机器ID工具
        mock_machine_util.get_machine_id_type.return_value = MachineIdType.INSTANCE_ID
        
        # 模拟工作流流式执行
        async def mock_astream(state, config=None, stream_mode=None):
            yield ("updates", {"node1": {"finished": True}})
        
        mock_workflow.astream = mock_astream
        
        # 创建外部处理器
        external_handler = Mock()
        
        # 执行测试
        events = []
        async for event in stream_inspect_workflow(
            machine_id="i-test123",
            start_time="2025-09-02 00:00:00",
            end_time="2025-09-02 01:00:00",
            langfuse_handler=external_handler
        ):
            events.append(event)
        
        # 验证没有创建新的处理器
        mock_create_handler.assert_not_called()
        assert len(events) >= 1

    @pytest.mark.asyncio
    @patch("deep_diagnose.core.interactive.workflows.inspect_workflow.create_inspect_workflow")
    async def test_stream_inspect_workflow_exception(self, mock_create_workflow):
        """测试工作流执行异常场景"""
        # 模拟工作流异常
        mock_workflow = Mock()
        mock_workflow.astream.side_effect = Exception("工作流执行失败")
        mock_create_workflow.return_value = mock_workflow
        
        # 执行测试
        events = []
        async for event in stream_inspect_workflow(
            machine_id="i-test123",
            start_time="2025-09-02 00:00:00",
            end_time="2025-09-02 01:00:00"
        ):
            events.append(event)
        
        # 验证错误处理
        assert len(events) >= 2  # 初始事件和错误事件
        assert events[-1].finished is True
        assert "执行失败" in events[-1].overview


if __name__ == "__main__":
    pytest.main([__file__])