"""
data_collection_node 模块的单元测试

测试数据收集节点的各种功能，包括工具调用、缓存处理、异常情况等
"""

import pytest
import asyncio
import json
from unittest.mock import Mock, MagicMock, patch, AsyncMock
from typing import Dict, Any

from deep_diagnose.core.interactive.nodes.data_collection_node import (
    data_collection_node,
    _generate_cache_key,
    _process_tool_results,
    _build_tool_tasks,
)
from deep_diagnose.common.utils.machine_utils import MachineIdType


class TestDataCollectionNode:
    """data_collection_node 函数的测试"""

    @pytest.mark.asyncio
    @patch("deep_diagnose.core.interactive.nodes.data_collection_node.MCPToolManager")
    @patch("deep_diagnose.core.interactive.nodes.data_collection_node.RedisClient")
    async def test_data_collection_node_cache_hit(self, mock_redis_client, mock_mcp_manager):
        """测试缓存命中场景"""
        # 模拟 MCP 工具
        mock_tool = Mock()
        mock_tool.name = "getVmBasicInfo"
        mock_manager = Mock()
        mock_manager.get_enabled_mcp_tools = AsyncMock(return_value=[mock_tool])
        mock_mcp_manager.return_value = mock_manager
        
        # 模拟缓存数据
        cached_data = {"getVmBasicInfo": {"instance": "test"}}
        mock_redis = Mock()
        mock_redis.get_cache.return_value = json.dumps(cached_data)  # 模拟返回字符串
        mock_redis_client.return_value = mock_redis

        # 输入状态
        state = {
            "machine_id": "i-test123",
            "machine_id_type": MachineIdType.INSTANCE_ID,
            "start_time": "2025-09-02 00:00:00",
            "end_time": "2025-09-02 01:00:00"
        }

        # 执行测试
        result = await data_collection_node(state)

        # 验证结果
        assert "info_data" in result
        assert result["info_data"] == cached_data
        assert result["step_metadata"]["cache_hit"] is True
        assert result["step_metadata"]["collection_method"] == "cached"

    @pytest.mark.asyncio
    @patch("deep_diagnose.core.interactive.nodes.data_collection_node.MCPToolManager")
    @patch("deep_diagnose.core.interactive.nodes.data_collection_node.RedisClient")
    async def test_data_collection_node_mcp_tools(self, mock_redis_client, mock_mcp_manager):
        """测试MCP工具调用场景"""
        # 模拟工具
        mock_tool = Mock()
        mock_tool.name = "getVmBasicInfo"
        mock_tool.ainvoke = AsyncMock(return_value='{"result": "test_data"}')
        
        mock_manager = Mock()
        mock_manager.get_enabled_mcp_tools = AsyncMock(return_value=[mock_tool])
        mock_mcp_manager.return_value = mock_manager

        # 模拟Redis缓存未命中
        mock_redis = Mock()
        mock_redis.get_cache.return_value = None
        mock_redis.set_cache = Mock()
        mock_redis_client.return_value = mock_redis

        # 输入状态
        state = {
            "machine_id": "i-test123",
            "machine_id_type": MachineIdType.INSTANCE_ID,
            "start_time": "2025-09-02 00:00:00",
            "end_time": "2025-09-02 01:00:00"
        }

        # 执行测试
        result = await data_collection_node(state)

        # 验证结果
        assert "info_data" in result
        assert "step_metadata" in result
        assert result["step_metadata"]["collection_method"] == "mcp_tools"
        assert result["step_metadata"]["tools_executed"] > 0

    @pytest.mark.asyncio
    @patch("deep_diagnose.core.interactive.nodes.data_collection_node.MCPToolManager")
    @patch("deep_diagnose.core.interactive.nodes.data_collection_node.RedisClient")
    async def test_data_collection_node_no_tools(self, mock_redis_client, mock_mcp_manager):
        """测试没有可用工具的场景"""
        # 模拟没有工具
        mock_manager = Mock()
        mock_manager.get_enabled_mcp_tools = AsyncMock(return_value=[])
        mock_mcp_manager.return_value = mock_manager

        # 模拟Redis缓存未命中
        mock_redis = Mock()
        mock_redis.get_cache.return_value = None
        mock_redis_client.return_value = mock_redis

        # 输入状态
        state = {
            "machine_id": "i-test123",
            "machine_id_type": MachineIdType.INSTANCE_ID,
            "start_time": "2025-09-02 00:00:00",
            "end_time": "2025-09-02 01:00:00"
        }

        # 执行测试
        result = await data_collection_node(state)

        # 验证结果
        assert result["info_data"] == {}
        assert result["step_metadata"]["collection_method"] == "no_tools"

    @pytest.mark.asyncio
    async def test_data_collection_node_exception(self):
        """测试异常处理"""
        # 输入状态（缺少必要字段）
        state = {}

        # 执行测试
        result = await data_collection_node(state)

        # 验证错误处理
        assert result["info_data"] == {}
        assert "error" in result["step_metadata"]
        assert result["step_metadata"]["collection_method"] == "error"


class TestCacheKeyGeneration:
    """缓存键生成函数的测试"""

    def test_generate_cache_key_success(self):
        """测试成功生成缓存键"""
        machine_id = "i-test123"
        start_time = "2025-09-02 00:00:00"
        end_time = "2025-09-02 01:00:00"

        result = _generate_cache_key(machine_id, start_time, end_time)

        assert isinstance(result, str)
        assert machine_id in result
        assert result.startswith("inspect_information_")

    def test_generate_cache_key_invalid_time(self):
        """测试无效时间格式"""
        machine_id = "i-test123"
        start_time = "invalid_time"
        end_time = "2025-09-02 01:00:00"

        with pytest.raises(ValueError):
            _generate_cache_key(machine_id, start_time, end_time)


class TestToolResultsProcessing:
    """工具结果处理函数的测试"""

    def test_process_tool_results_success(self):
        """测试成功处理工具结果"""
        tool_tasks = [
            {"name": "getVmBasicInfo"},
            {"name": "getNcBasicInfo"}
        ]
        task_results = [
            '{"vm": "data"}',
            '{"nc": "data"}'
        ]

        result = _process_tool_results(tool_tasks, task_results)

        assert "getVmBasicInfo" in result
        assert "getNcBasicInfo" in result
        assert result["getVmBasicInfo"] == {"vm": "data"}
        assert result["getNcBasicInfo"] == {"nc": "data"}

    def test_process_tool_results_with_exception(self):
        """测试处理包含异常的结果"""
        tool_tasks = [
            {"name": "getVmBasicInfo"},
            {"name": "failedTool"}
        ]
        task_results = [
            '{"vm": "data"}',
            Exception("Tool failed")
        ]

        result = _process_tool_results(tool_tasks, task_results)

        assert "getVmBasicInfo" in result
        assert "failedTool" in result
        assert result["getVmBasicInfo"] == {"vm": "data"}
        assert result["failedTool"] == {}  # 默认值

    def test_process_tool_results_invalid_json(self):
        """测试处理无效JSON结果"""
        tool_tasks = [
            {"name": "invalidTool"}
        ]
        task_results = [
            "invalid json"
        ]

        result = _process_tool_results(tool_tasks, task_results)

        assert "invalidTool" in result
        assert result["invalidTool"] == {}  # 默认值


class TestToolTasksBuilding:
    """工具任务构建函数的测试"""

    def test_build_tool_tasks_vm_instance(self):
        """测试构建VM实例工具任务"""
        machine_id = "i-test123"
        machine_id_type = MachineIdType.INSTANCE_ID
        start_time = "2025-09-02 00:00:00"
        end_time = "2025-09-02 01:00:00"
        
        # 模拟工具映射
        mock_tool = Mock()
        mock_tool.ainvoke = AsyncMock()
        tool_map = {
            "getVmBasicInfo": mock_tool,
            "listReportedOperationalEvents": mock_tool
        }

        result = _build_tool_tasks(machine_id, machine_id_type, start_time, end_time, tool_map)

        # 验证任务构建
        assert len(result) == 2
        task_names = [task["name"] for task in result]
        assert "getVmBasicInfo" in task_names
        assert "listReportedOperationalEvents" in task_names

    def test_build_tool_tasks_nc_instance(self):
        """测试构建NC实例工具任务"""
        machine_id = "********"
        machine_id_type = MachineIdType.NC_IP
        start_time = "2025-09-02 00:00:00"
        end_time = "2025-09-02 01:00:00"
        
        # 模拟工具映射
        mock_tool = Mock()
        mock_tool.ainvoke = AsyncMock()
        tool_map = {
            "getNcBasicInfo": mock_tool,
            "listCategorizedMonitorExceptions": mock_tool
        }

        result = _build_tool_tasks(machine_id, machine_id_type, start_time, end_time, tool_map)

        # 验证任务构建
        assert len(result) >= 1
        task_names = [task["name"] for task in result]
        assert "getNcBasicInfo" in task_names

    def test_build_tool_tasks_unknown_type(self):
        """测试未知机器类型"""
        machine_id = "unknown"
        machine_id_type = MachineIdType.UNKNOWN
        start_time = "2025-09-02 00:00:00"
        end_time = "2025-09-02 01:00:00"
        
        tool_map = {}

        result = _build_tool_tasks(machine_id, machine_id_type, start_time, end_time, tool_map)

        # 未知类型应该返回通用工具任务
        assert isinstance(result, list)

    def test_build_tool_tasks_empty_tool_map(self):
        """测试空工具映射"""
        machine_id = "i-test123"
        machine_id_type = MachineIdType.INSTANCE_ID
        start_time = "2025-09-02 00:00:00"
        end_time = "2025-09-02 01:00:00"
        
        tool_map = {}

        result = _build_tool_tasks(machine_id, machine_id_type, start_time, end_time, tool_map)

        # 空工具映射应该返回空任务列表
        assert result == []


if __name__ == "__main__":
    pytest.main([__file__])