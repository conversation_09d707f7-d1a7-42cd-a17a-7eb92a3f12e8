import os
import sys
import pytest

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../src'))

from deep_diagnose.tools.mcp.mcp_tool_cache import MCPCacheManager


class TestMCPCacheManager:
    """Test cases for MCPCacheManager."""

    @pytest.mark.asyncio
    async def test_get_tools_contains_vm_migration_tool(self):
        """Test that get_tools returns tools including query_vm_live_migration_eligibility."""
        print("\n=== Testing MCPCacheManager.get_tools() ===")
        
        # Create MCPCacheManager instance
        cache_manager = MCPCacheManager()
        print(f"Created MCPCacheManager with enable_cache: {cache_manager.enable_cache}")
        
        # Get tools from cache manager
        print("Calling get_tools()...")
        tools_dict = await cache_manager.get_tools()
        
        # Print basic info about returned tools
        print(f"Returned tools_dict type: {type(tools_dict)}")
        print(f"Number of servers: {len(tools_dict)}")
        
        # Print server names and tool counts
        for server_name, tools in tools_dict.items():
            tool_count = len(tools) if tools else 0
            print(f"Server '{server_name}': {tool_count} tools")
        
        # Check that tools_dict is a dictionary
        assert isinstance(tools_dict, dict), "get_tools should return a dictionary"
        
        # Look for the specific tool across all servers
        target_tool_name = "query_vm_live_migration_eligibility"
        found_tool = False
        found_in_server = None
        
        for server_name, tools in tools_dict.items():
            if tools:  # Check if tools list is not None or empty
                for tool in tools:
                    print(f"  Tool in {server_name}: {tool.name}")
                    if tool.name == target_tool_name:
                        found_tool = True
                        found_in_server = server_name
                        print(f"✅ Found target tool '{target_tool_name}' in server '{server_name}'")
                        break
                if found_tool:
                    break
        
        # Assert that the target tool was found
        assert found_tool, f"Tool '{target_tool_name}' should be present in the returned tools"
        print(f"✅ Test passed: Tool '{target_tool_name}' found in server '{found_in_server}'")
        print("=== Test completed successfully ===\n")