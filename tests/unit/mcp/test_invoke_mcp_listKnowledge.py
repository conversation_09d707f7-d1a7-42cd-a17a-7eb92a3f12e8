"""
测试使用basic_info服务器的listKnowledge工具
"""

import pytest
import logging
import asyncio
from typing import List, Optional, Dict, Any
import json

from langchain_mcp_adapters.client import MultiServerMCPClient
from langchain_core.tools import BaseTool

from deep_diagnose.tools.mcp.mcp_tool_config import MCPToolConfig

# 导入并应用 monkey patch
from .monkey_patch_streamable_http import (
    apply_streamable_http_monkey_patch,
    apply_alternative_monkey_patch,
    setup_enhanced_logging
)

logger = logging.getLogger(__name__)

# 在模块级别应用 monkey patch
setup_enhanced_logging()
logger.info("🔧 正在应用 streamable_http monkey patch...")
patch_success = apply_streamable_http_monkey_patch()
if not patch_success:
    logger.info("🔧 尝试替代 monkey patch 方案...")
    apply_alternative_monkey_patch()


class PayloadCaptureHandler(logging.Handler):
    """捕获 MCP 客户端原始 payload 的日志处理器"""
    
    def __init__(self):
        super().__init__()
        self.captured_payloads = []
        self.captured_errors = []
    
    def emit(self, record):
        """处理日志记录"""
        if hasattr(record, 'msg'):
            message = str(record.msg)
            
            # 捕获错误信息和相关的 payload
            if 'Error parsing SSE message' in message or 'JSON解析错误' in message:
                self.captured_errors.append({
                    'message': message,
                    'args': getattr(record, 'args', []),
                    'levelname': record.levelname,
                    'timestamp': record.created
                })
                
                # 尝试从错误信息中提取 input_value
                try:
                    if 'input_value=' in message:
                        start_idx = message.find('input_value=') + len('input_value=')
                        end_idx = message.find(', input_type=', start_idx)
                        if end_idx != -1:
                            raw_payload = message[start_idx:end_idx]
                            
                            # 去掉首尾的引号
                            if raw_payload.startswith("'") and raw_payload.endswith("'"):
                                raw_payload = raw_payload[1:-1]
                            
                            logger.error(f"🔍 完整 payload 长度: {len(raw_payload)}")
                            logger.error(f"🔍 前500字符: {raw_payload[:500]}")
                            logger.error(f"🔍 中段(1100-1200): {raw_payload[1100:1200]}")
                            logger.error(f"🔍 后100字符: {raw_payload[-100:]}")
                            
                            # 查找第1147字符附近的内容
                            if len(raw_payload) > 1147:
                                start = max(0, 1147 - 50)
                                end = min(len(raw_payload), 1147 + 50)
                                logger.error(f"🔍 第1147字符附近: {repr(raw_payload[start:end])}")
                                
                                # 找出第1147个字符
                                if 1147 < len(raw_payload):
                                    char_at_1147 = raw_payload[1147]
                                    logger.error(f"🔍 第1147个字符是: '{char_at_1147}' (ASCII: {ord(char_at_1147)})")
                            
                            self.captured_payloads.append(raw_payload)
                except Exception as e:
                    logger.warning(f"解析 payload 失败: {e}")
    
    def get_latest_error_info(self):
        """获取最新的错误信息"""
        if self.captured_errors:
            return self.captured_errors[-1]
        return None
    
    def get_latest_payload(self):
        """获取最新的 payload"""
        if self.captured_payloads:
            return self.captured_payloads[-1]
        return None


class TestBasicInfoListKnowledge:
    """测试basic_info服务器的listKnowledge工具调用"""

    @pytest.fixture(scope="class")
    def payload_capture_handler(self):
        """设置 payload 捕获处理器"""
        # 创建 payload 捕获处理器
        handler = PayloadCaptureHandler()
        handler.setLevel(logging.ERROR)
        
        # 添加到 MCP 客户端相关的 logger
        mcp_loggers = [
            logging.getLogger('mcp.client.streamable_http'),
            logging.getLogger('mcp.client'),
            logging.getLogger('langchain_mcp_adapters'),
        ]
        
        for mcp_logger in mcp_loggers:
            mcp_logger.addHandler(handler)
            mcp_logger.setLevel(logging.DEBUG)
        
        yield handler
        
        # 清理
        for mcp_logger in mcp_loggers:
            mcp_logger.removeHandler(handler)

    @pytest.fixture
    async def basic_info_client(self):
        """创建专门针对basic_info服务器的MCP客户端"""
        mcp_config = MCPToolConfig()
        servers_config = mcp_config.get_server_configs()
        servers = servers_config.get('servers', {})
        
        if 'basic_info' not in servers:
            pytest.skip("basic_info服务器配置不存在")
        
        # 确认listKnowledge工具已启用
        basic_info_config = servers['basic_info']
        enabled_tools = basic_info_config.get('enabled_tools', [])
        
        if 'listKnowledge' not in enabled_tools:
            pytest.skip("basic_info服务器中未启用listKnowledge工具")
            
        logger.info(f"basic_info服务器启用的工具: {len(enabled_tools)}个")
        
        # 清理配置，只保留必要的参数
        clean_config = {
            'basic_info': {
                'transport': basic_info_config.get('transport', 'streamable_http'),
                'url': basic_info_config.get('url'),
                'headers': basic_info_config.get('headers', {})
            }
        }
        
        logger.info(f"使用的配置: {clean_config}")
        
        # 直接传入清理后的配置
        client = MultiServerMCPClient(clean_config)
        return client

    @pytest.mark.asyncio
    async def test_basic_info_listKnowledge(self, basic_info_client, payload_capture_handler):
        """测试basic_info服务器的listKnowledge工具"""
        client = basic_info_client
        
        logger.info("🔍 开始测试basic_info服务器的listKnowledge工具")
        
        try:
            # 获取basic_info服务器的工具
            tools = await client.get_tools(server_name='basic_info')
            logger.info(f"从basic_info服务器获取到{len(tools)}个工具")
            
            # 查找listKnowledge工具
            listKnowledge_tool = None
            for tool in tools:
                if tool.name == "listKnowledge":
                    listKnowledge_tool = tool
                    break
            
            assert listKnowledge_tool is not None, "未找到listKnowledge工具"
            logger.info(f"✅ 找到listKnowledge工具: {listKnowledge_tool.description[:100]}...")
            
            # 测试调用listKnowledge工具
            test_query = "iohub是什么"
            logger.info(f"🚀 调用listKnowledge工具，查询: '{test_query}'")
            
            # 清空之前的捕获记录
            payload_capture_handler.captured_payloads.clear()
            payload_capture_handler.captured_errors.clear()
            
            result = await asyncio.wait_for(
                listKnowledge_tool.ainvoke({"query": test_query}),
                timeout=30.0
            )
            
            logger.info(f"🎉 listKnowledge调用成功!")
            logger.info(f"结果类型: {type(result)}")
            logger.info(f"结果长度: {len(str(result))}")
            
            # 验证结果
            assert result is not None, "工具应该返回结果"
            logger.info(f"✅ basic_info服务器的listKnowledge工具测试通过")
            
        except asyncio.TimeoutError:
            logger.warning("⏱️ listKnowledge工具调用超时，但集成正常")
            
            # 检查是否捕获到了 payload 错误
            self._log_captured_debug_info(payload_capture_handler)
            
        except Exception as e:
            logger.error(f"❌ 测试失败: {e}")
            
            # 检查是否捕获到了 payload 错误
            self._log_captured_debug_info(payload_capture_handler)
            
            raise
    
    def _log_captured_debug_info(self, payload_capture_handler):
        """记录捕获到的调试信息"""
        latest_error = payload_capture_handler.get_latest_error_info()
        latest_payload = payload_capture_handler.get_latest_payload()
        
        if latest_error:
            logger.error(f"🔍 捕获到的最新错误: {latest_error['message'][:200]}...")
        
        if latest_payload:
            logger.error(f"🔍 捕获到的最新 payload 片段:")
            logger.error(f"   长度: {len(latest_payload)}")
            logger.error(f"   前200字符: {latest_payload[:200]}")
            logger.error(f"   1100-1200字符: {latest_payload[1100:1200]}")
            logger.error(f"   后200字符: {latest_payload[-200:]}")
            
            # 尝试查找可能的问题字符
            problematic_chars = []
            for i, char in enumerate(latest_payload):
                if ord(char) > 127:  # 非-ASCII 字符
                    problematic_chars.append((i, char, ord(char)))
                    if len(problematic_chars) >= 10:  # 只显示前10个
                        break
            
            if problematic_chars:
                logger.error(f"🔍 发现的非-ASCII字符: {problematic_chars}")
        else:
            logger.info("🔍 未捕获到任何 payload 错误")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])