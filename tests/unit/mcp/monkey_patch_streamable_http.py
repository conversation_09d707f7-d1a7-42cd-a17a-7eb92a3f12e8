"""
Monkey patch for langchain_mcp_adapters streamable_http module
用于增强 SSE 消息解析错误的日志记录
"""

import logging
import json
import re
from typing import Any

logger = logging.getLogger(__name__)

def apply_streamable_http_monkey_patch():
    """
    应用 monkey patch 到 streamable_http 模块
    增强错误日志记录，显示完整的原始 payload 信息
    """
    try:
        # 首先尝试 patch streamable_http 模块的 SSE 解析
        try:
            import mcp.client.streamable_http as streamable_http_module
            
            # 查找并 patch _parse_sse_message 或类似的函数
            if hasattr(streamable_http_module, '_parse_sse_message'):
                original_parse_sse = streamable_http_module._parse_sse_message
                
                def enhanced_parse_sse_message(line: str):
                    """增强的 SSE 消息解析"""
                    logger.error(f"🔍 [SSE] 原始消息长度: {len(line)}")
                    logger.error(f"🔍 [SSE] 原始消息前200字符: {repr(line[:200])}")
                    logger.error(f"🔍 [SSE] 原始消息后200字符: {repr(line[-200:])}")
                    
                    # 检查是否包含中文字符
                    chinese_chars = [(i, char) for i, char in enumerate(line) if ord(char) > 127]
                    if chinese_chars:
                        logger.error(f"🔍 [SSE] 发现非ASCII字符: {chinese_chars[:10]}")
                    
                    return original_parse_sse(line)
                
                streamable_http_module._parse_sse_message = enhanced_parse_sse_message
                logger.info("✅ 成功应用 _parse_sse_message monkey patch")
        
        except Exception as e:
            logger.warning(f"SSE monkey patch 失败: {e}")
        
        # 直接 patch JSONRPCMessage.model_validate_json 方法
        try:
            from mcp.types import JSONRPCMessage
            
            # 保存原始方法
            original_model_validate_json = JSONRPCMessage.model_validate_json
            
            @classmethod
            def enhanced_model_validate_json(cls, json_data, **kwargs):
                """增强的 JSON 验证，预处理有问题的字符"""
                try:
                    # 尝试原始解析
                    return original_model_validate_json(json_data, **kwargs)
                except Exception as e:
                    # 检查是否是 pydantic ValidationError
                    from pydantic_core import ValidationError as PydanticValidationError
                    
                    # 先记录错误信息
                    logger.error(f"🚨 JSON解析错误: {str(e)}")
                    logger.error(f"📏 数据长度: {len(json_data) if isinstance(json_data, str) else 'N/A'}")
                    
                    if (isinstance(e, PydanticValidationError) and 
                        'json_invalid' in str(e) and 
                        isinstance(json_data, str)):
                        
                        logger.error("=" * 120)
                        logger.error("🔍 捕获到 JSON 解析错误! 开始详细分析...")
                        logger.error(f"原始错误: {str(e)}")
                        logger.error(f"完整内容长度: {len(json_data)} 字符")
                        
                        # 检查是否是截断问题
                        if 'EOF while parsing a string' in str(e):
                            logger.error("💔 检测到JSON字符串截断问题!")
                            logger.error(f"📍 JSON末尾200字符: {repr(json_data[-200:])}")
                            
                            # 尝试修复截断的JSON
                            logger.error("🔧 尝试修复截断的JSON字符串...")
                            fixed_json = json_data
                            
                            # 检查是否以不完整的字符串结尾
                            if not fixed_json.endswith('"}'):
                                if not fixed_json.endswith('"'):
                                    # 添加缺失的引号
                                    fixed_json += '"'
                                    logger.error("✅ 添加了缺失的结尾引号")
                                
                                # 尝试添加缺失的结构
                                if not fixed_json.endswith('}'):
                                    # 检查需要添加的结构
                                    if '"result":' in fixed_json and not fixed_json.rstrip().endswith('}'):
                                        fixed_json += '}'
                                        logger.error("✅ 添加了缺失的result结束括号")
                                    
                                    # 最外层括号
                                    if not fixed_json.endswith('}'):
                                        fixed_json += '}'
                                        logger.error("✅ 添加了缺失的最外层结束括号")
                            
                            # 尝试重新解析修复后的JSON
                            try:
                                result = original_model_validate_json(fixed_json, **kwargs)
                                logger.error("✅ JSON截断修复成功！")
                                logger.error("=" * 120)
                                return result
                            except Exception as retry_error:
                                logger.error(f"❌ JSON截断修复失败: {retry_error}")
                        
                        else:
                            # 分析具体错误位置
                            error_match = re.search(r'column (\d+)', str(e))
                            if error_match:
                                error_pos = int(error_match.group(1))
                                start_pos = max(0, error_pos - 100)
                                end_pos = min(len(json_data), error_pos + 100)
                                
                                logger.error(f"📍 错误位置附近 ({start_pos}-{end_pos}):")
                                problem_segment = json_data[start_pos:end_pos]
                                logger.error(f"问题片段: {repr(problem_segment)}")
                            
                            # 打印JSON末尾的内容
                            logger.error(f"📍 JSON末尾200字符: {repr(json_data[-200:])}")
                            
                            # 尝试清理和修复
                            logger.error("🔧 尝试修复 JSON 字符串...")
                            cleaned_json = json_data
                            
                            # 替换不间断空格为普通空格
                            if '\xa0' in cleaned_json:
                                original_count = cleaned_json.count('\xa0')
                                cleaned_json = cleaned_json.replace('\xa0', ' ')
                                logger.error(f"✅ 已替换 {original_count} 个不间断空格")
                            
                            # 尝试重新解析清理后的 JSON
                            try:
                                result = original_model_validate_json(cleaned_json, **kwargs)
                                logger.error("✅ JSON 修复成功！")
                                logger.error("=" * 120)
                                return result
                            except Exception as retry_error:
                                logger.error(f"❌ JSON 修复失败: {retry_error}")
                        
                        logger.error("=" * 120)
                    
                    # 如果不是我们关注的情况，直接抛出原始错误
                    raise
            
            # 应用 monkey patch
            JSONRPCMessage.model_validate_json = enhanced_model_validate_json
            logger.info("✅ 成功应用 JSONRPCMessage.model_validate_json monkey patch")
            return True
            
        except ImportError as e:
            logger.warning(f"无法导入 JSONRPCMessage: {e}")
        
        # 备用方案：patch json 模块
        try:
            import json
            original_loads = json.loads
            
            def enhanced_json_loads(s, **kwargs):
                """增强的 JSON loads，预处理和错误捕获"""
                try:
                    # 预处理：清理可能的问题字符
                    if isinstance(s, str) and len(s) > 1000:
                        # 替换不间断空格为普通空格
                        cleaned_s = s.replace('\xa0', ' ')
                        if cleaned_s != s:
                            logger.warning("🔧 检测到不间断空格字符，已自动清理")
                            return original_loads(cleaned_s, **kwargs)
                    
                    return original_loads(s, **kwargs)
                except json.JSONDecodeError as e:
                    if len(str(s)) > 1000:  # 只处理长JSON
                        logger.error("=" * 80)
                        logger.error("🔍 JSON 解析错误详情:")
                        logger.error(f"错误位置: 行 {e.lineno}, 列 {e.colno}")
                        logger.error(f"错误信息: {e.msg}")
                        
                        # 显示错误位置附近的内容
                        error_pos = e.pos if hasattr(e, 'pos') else e.colno
                        start = max(0, error_pos - 50)
                        end = min(len(s), error_pos + 50)
                        logger.error(f"错误附近内容: {repr(s[start:end])}")
                        logger.error("=" * 80)
                    raise
            
            json.loads = enhanced_json_loads
            logger.info("✅ 成功应用增强的 json.loads monkey patch")
            return True
            
        except Exception as e:
            logger.warning(f"JSON monkey patch 失败: {e}")
        
        return False
        
    except Exception as e:
        logger.error(f"应用 monkey patch 失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
        return False

def apply_alternative_monkey_patch():
    """
    尝试应用替代的 monkey patch 方案
    """
    try:
        # 尝试 patch HTTP 客户端响应处理
        try:
            import aiohttp
            
            # 保存原始的 ClientResponse.text 方法
            original_text = aiohttp.ClientResponse.text
            
            async def enhanced_text(self, encoding=None, errors='strict'):
                """增强的文本响应处理"""
                text_content = await original_text(self, encoding, errors)
                
                # 只记录 MCP 相关的响应
                if hasattr(self, 'url') and 'mcp' in str(self.url):
                    logger.error("=" * 120)
                    logger.error(f"🌐 [HTTP] 服务器响应 URL: {self.url}")
                    logger.error(f"🌐 [HTTP] 响应状态: {self.status}")
                    logger.error(f"🌐 [HTTP] 响应长度: {len(text_content)} 字符")
                    logger.error(f"🌐 [HTTP] 响应头: {dict(self.headers)}")
                    logger.error(f"🌐 [HTTP] 响应前500字符: {repr(text_content[:500])}")
                    logger.error(f"🌐 [HTTP] 响应后500字符: {repr(text_content[-500:])}")
                    
                    # 检查是否是 SSE 流
                    if 'text/event-stream' in self.headers.get('content-type', ''):
                        logger.error("🌐 [HTTP] 这是一个 SSE 流响应")
                        
                        # 分析 SSE 事件
                        lines = text_content.split('\n')
                        for i, line in enumerate(lines):
                            if line.startswith('data: '):
                                data_content = line[6:]  # 去掉 'data: ' 前缀
                                logger.error(f"🌐 [SSE] 事件 {i}: 长度={len(data_content)}")
                                logger.error(f"🌐 [SSE] 事件 {i}: 内容={repr(data_content[:200])}")
                                
                                # 检查是否是截断的 JSON
                                if data_content and not data_content.endswith('}'):
                                    logger.error(f"🌐 [SSE] ⚠️ 事件 {i} 可能被截断!")
                    
                    logger.error("=" * 120)
                
                return text_content
            
            # 应用 monkey patch
            aiohttp.ClientResponse.text = enhanced_text
            logger.info("✅ 成功应用 aiohttp.ClientResponse.text monkey patch")
            
        except Exception as e:
            logger.warning(f"aiohttp monkey patch 失败: {e}")
        
        # 尝试直接 patch mcp.client.streamable_http 模块的相关函数
        import mcp.client.streamable_http as streamable_http_module
        
        # 查找可能的解析函数
        for attr_name in dir(streamable_http_module):
            attr = getattr(streamable_http_module, attr_name)
            if callable(attr) and ('parse' in attr_name.lower() or 'sse' in attr_name.lower()):
                logger.info(f"发现可能的解析函数: {attr_name}")
        
        # 尝试 patch StreamableHTTPTransport 类
        try:
            if hasattr(streamable_http_module, 'StreamableHTTPTransport'):
                transport_class = streamable_http_module.StreamableHTTPTransport
                
                # 查找可能的方法
                for method_name in dir(transport_class):
                    if not method_name.startswith('_') and callable(getattr(transport_class, method_name)):
                        logger.info(f"发现 StreamableHTTPTransport 方法: {method_name}")
                
                # 尝试 patch _handle_response 或类似方法
                if hasattr(transport_class, '_handle_response'):
                    original_handle_response = transport_class._handle_response
                    
                    async def enhanced_handle_response(self, response):
                        """增强的响应处理"""
                        logger.error(f"🔧 [Transport] 处理响应: {response.status}")
                        result = await original_handle_response(self, response)
                        logger.error(f"🔧 [Transport] 响应处理完成")
                        return result
                    
                    transport_class._handle_response = enhanced_handle_response
                    logger.info("✅ 成功应用 StreamableHTTPTransport._handle_response monkey patch")
        
        except Exception as e:
            logger.warning(f"StreamableHTTPTransport monkey patch 失败: {e}")
        
        logger.info("✅ 完成替代 monkey patch 探索")
        return True
        
    except Exception as e:
        logger.error(f"替代 monkey patch 失败: {e}")
        return False

def setup_enhanced_logging():
    """
    设置增强的日志记录
    """
    # 设置 mcp 相关模块的日志级别
    mcp_loggers = [
        'mcp.client.streamable_http',
        'mcp.client',
        'langchain_mcp_adapters',
    ]
    
    for logger_name in mcp_loggers:
        mcp_logger = logging.getLogger(logger_name)
        mcp_logger.setLevel(logging.DEBUG)
        
        # 添加控制台处理器（如果还没有）
        if not mcp_logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            mcp_logger.addHandler(handler)
    
    logger.info("✅ 完成增强日志设置")

if __name__ == "__main__":
    # 测试 monkey patch
    setup_enhanced_logging()
    
    success = apply_streamable_http_monkey_patch()
    if not success:
        logger.info("尝试替代方案...")
        apply_alternative_monkey_patch()