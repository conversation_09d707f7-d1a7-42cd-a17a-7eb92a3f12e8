"""
测试规划处理器基类功能
"""

import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../src'))

from deep_diagnose.core.reasoning.events.processors.planning_processor_base import PlanningProcessorBase
from deep_diagnose.core.events.models import AgentMessage


class TestPlanningProcessor(PlanningProcessorBase):
    """测试用的规划处理器实现"""
    
    def get_agent_name(self) -> str:
        return "test_planner"


class TestPlanningProcessorBase:
    """测试规划处理器基类功能"""
    
    def test_parse_planning_content(self):
        """测试规划内容解析"""
        processor = TestPlanningProcessor()
        
        # 测试完整JSON解析
        test_json = '{"thought": "测试思考", "steps": [{"title": "步骤1", "description": "描述1"}]}'
        thought, steps = processor._parse_planning_content(test_json)
        
        assert thought == "测试思考"
        assert len(steps) == 1
        assert steps[0]["title"] == "步骤1"
        assert steps[0]["description"] == "描述1"
        
        # 测试部分解析
        partial_json = '"thought": "部分思考", "steps": [{"title": "部分步骤"'
        thought2, steps2 = processor._parse_partial_planning_content(partial_json)
        
        assert thought2 == "部分思考"


if __name__ == "__main__":
    test = TestPlanningProcessorBase()
    test.test_parse_planning_content()
    print("✅ 规划处理器测试通过")