import unittest
from deep_diagnose.core.interactive.fast_path import _inject


class TestInject(unittest.TestCase):

    def test_inject_with_string_and_simple_variable(self):
        """测试字符串中的简单变量替换"""
        raw = "Hello ${name}, welcome to ${place}!"
        var_dict = {"name": "Alice", "place": "Wonderland"}
        result = _inject(raw, var_dict)
        self.assertEqual("Hello Alice, welcome to Wonderland!", result)

    def test_inject_with_string_and_missing_variable(self):
        """测试字符串中缺少变量的情况"""
        raw = "Hello ${name}, welcome to ${place}!"
        var_dict = {"name": "Alice"}
        result = _inject(raw, var_dict)
        # 由于变量place不存在，应该返回占位符名称本身
        self.assertEqual("Hello Alice, welcome to place!", result)

    def test_inject_with_list(self):
        """测试列表类型的数据注入"""
        raw = ["Hello ${name}", "Welcome to ${place}", 123]
        var_dict = {"name": "<PERSON>", "place": "Techland"}
        result = _inject(raw, var_dict)
        self.assertEqual(["Hello Bob", "Welcome to Techland", 123], result)

    def test_inject_with_dict(self):
        """测试字典类型的数据注入"""
        raw = {
            "greeting": "Hello ${name}",
            "location": "Welcome to ${place}",
            "constant": 42
        }
        var_dict = {"name": "Charlie", "place": "Innovation City"}
        result = _inject(raw, var_dict)
        expected = {
            "greeting": "Hello Charlie",
            "location": "Welcome to Innovation City",
            "constant": 42
        }
        self.assertEqual(expected, result)

    def test_inject_with_nested_structures(self):
        """测试嵌套数据结构的数据注入"""
        raw = {
            "users": [
                {"name": "User ${index1}", "age": "${age1}"},
                {"name": "User ${index2}", "age": "${age2}"}
            ],
            "total": "Total: ${count} users"
        }
        var_dict = {
            "index1": "1", "age1": "25",
            "index2": "2", "age2": "30",
            "count": "2"
        }
        result = _inject(raw, var_dict)
        expected = {
            "users": [
                {"name": "User 1", "age": "25"},
                {"name": "User 2", "age": "30"}
            ],
            "total": "Total: 2 users"
        }
        self.assertEqual(expected, result)

    def test_inject_with_non_string_dict_list(self):
        """测试非字符串、字典、列表类型的直接返回"""
        # 测试整数
        result = _inject(123, {})
        self.assertEqual(123, result)

        # 测试浮点数
        result = _inject(123.45, {})
        self.assertEqual(123.45, result)

        # 测试布尔值
        result = _inject(True, {})
        self.assertEqual(True, result)

    def test_inject_with_complex_expression(self):
        """测试复杂表达式的注入"""
        raw = "Timestamp: ${offset_timestamp(time, 3000)}"
        var_dict = {"time": "2025-09-01 00:00:00"}
        result = _inject(raw, var_dict)
        # 应该能够调用offset_timestamp函数
        self.assertEqual("Timestamp: 2025-09-01 00:50:00", result)


if __name__ == '__main__':
    unittest.main()
