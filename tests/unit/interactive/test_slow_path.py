import unittest
from deep_diagnose.core.interactive.slow_path import _adjust_time_range


class TestAdjustTimeRange(unittest.TestCase):

    def test_normal_time_range(self):
        """测试正常的时间范围"""
        time_dict = {
            "startTime": "2023-01-01 10:00:00",
            "endTime": "2023-01-01 15:00:00"
        }
        result = _adjust_time_range(time_dict)
        self.assertEqual(result["startTime"], "2023-01-01 10:00:00")
        self.assertEqual(result["endTime"], "2023-01-01 15:00:00")

    def test_short_time_range(self):
        """测试时间间隔小于2小时的情况"""
        time_dict = {
            "startTime": "2023-01-01 10:00:00",
            "endTime": "2023-01-01 11:00:00"
        }
        result = _adjust_time_range(time_dict)
        self.assertEqual(result["startTime"], "2023-01-01 09:00:00")
        self.assertEqual(result["endTime"], "2023-01-01 12:00:00")

    def test_reversed_time_range(self):
        """测试开始时间晚于结束时间的情况"""
        time_dict = {
            "startTime": "2023-01-01 15:00:00",
            "endTime": "2023-01-01 10:00:00"
        }
        result = _adjust_time_range(time_dict)
        self.assertEqual(result["startTime"], "2023-01-01 10:00:00")
        self.assertEqual(result["endTime"], "2023-01-01 15:00:00")

    def test_underscores_time_format(self):
        """测试下划线格式的时间字段"""
        time_dict = {
            "start_time": "2023-01-01 10:00:00",
            "end_time": "2023-01-01 11:00:00"
        }
        result = _adjust_time_range(time_dict)
        self.assertEqual(result["start_time"], "2023-01-01 09:00:00")
        self.assertEqual(result["end_time"], "2023-01-01 12:00:00")

    def test_missing_time_fields(self):
        """测试缺少时间字段的情况"""
        time_dict = {
            "startTime": "2023-01-01 10:00:00"
            # 缺少 endTime
        }
        result = _adjust_time_range(time_dict)
        # 应该返回原始字典
        self.assertEqual(result, time_dict)

    def test_invalid_time_format(self):
        """测试无效时间格式的情况"""
        time_dict = {
            "startTime": "invalid-time",
            "endTime": "2023-01-01 15:00:00"
        }
        result = _adjust_time_range(time_dict)
        # 应该返回原始字典
        self.assertEqual(result, time_dict)


if __name__ == '__main__':
    unittest.main()
