"""
搜索工具单元测试
测试搜索引擎工具的创建和基本功能
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../src'))

import json
import pytest
from typing import Any, Dict, List
from unittest.mock import patch, Mock

from deep_diagnose.tools.search import (
    get_web_search_tool,
    LoggedDuckDuckGoSearch,
    LoggedBraveSearch,
    LoggedArxivSearch
)
from deep_diagnose.common.config.constants.tools import SearchEngine


class TestSearchTools:
    """搜索工具测试类"""

    def test_duckduckgo_search_tool_creation(self) -> None:
        """测试DuckDuckGo搜索工具创建"""
        print("\n=== 测试DuckDuckGo搜索工具创建 ===")
        
        with patch('deep_diagnose.tools.search.SELECTED_SEARCH_ENGINE', SearchEngine.DUCKDUCKGO.value):
            search_tool = get_web_search_tool(max_search_results=5)
            
            assert search_tool is not None
            assert search_tool.name == "web_search"
            print(f"✓ DuckDuckGo搜索工具创建成功")
            print(f"  - 工具名称: {search_tool.name}")
            print(f"  - 工具类型: {type(search_tool)}")

    def test_brave_search_tool_creation(self) -> None:
        """测试Brave搜索工具创建"""
        print("\n=== 测试Brave搜索工具创建 ===")
        
        with patch('deep_diagnose.tools.search.SELECTED_SEARCH_ENGINE', SearchEngine.BRAVE_SEARCH.value):
            with patch('os.getenv', return_value="mock_api_key"):
                search_tool = get_web_search_tool(max_search_results=3)
                
                assert search_tool is not None
                assert search_tool.name == "web_search"
                print(f"✓ Brave搜索工具创建成功")
                print(f"  - 工具名称: {search_tool.name}")
                print(f"  - 工具类型: {type(search_tool)}")

    def test_arxiv_search_tool_creation(self) -> None:
        """测试Arxiv搜索工具创建"""
        print("\n=== 测试Arxiv搜索工具创建 ===")
        
        with patch('deep_diagnose.tools.search.SELECTED_SEARCH_ENGINE', SearchEngine.ARXIV.value):
            search_tool = get_web_search_tool(max_search_results=2)
            
            assert search_tool is not None
            assert search_tool.name == "web_search"
            print(f"✓ Arxiv搜索工具创建成功")
            print(f"  - 工具名称: {search_tool.name}")
            print(f"  - 工具类型: {type(search_tool)}")

    def test_unsupported_search_engine(self) -> None:
        """测试不支持的搜索引擎"""
        print("\n=== 测试不支持的搜索引擎 ===")
        
        with patch('deep_diagnose.tools.search.SELECTED_SEARCH_ENGINE', 'unsupported_engine'):
            with pytest.raises(ValueError, match="Unsupported search engine"):
                get_web_search_tool(max_search_results=3)
            
            print(f"✓ 不支持的搜索引擎正确抛出异常")

    def test_duckduckgo_search_functionality(self) -> None:
        """测试DuckDuckGo搜索功能（模拟）"""
        print("\n=== 测试DuckDuckGo搜索功能 ===")
        
        # 模拟搜索结果
        mock_results = [
            {"title": "可爱熊猫图片", "link": "https://example.com/panda1", "snippet": "可爱的熊猫照片"},
            {"title": "熊猫百科", "link": "https://example.com/panda2", "snippet": "关于熊猫的详细信息"},
            {"title": "熊猫视频", "link": "https://example.com/panda3", "snippet": "熊猫的有趣视频"}
        ]
        
        # 验证结果格式
        assert isinstance(mock_results, list)
        assert len(mock_results) == 3
        assert mock_results[0]["title"] == "可爱熊猫图片"
        
        print(f"✓ DuckDuckGo搜索功能测试成功（模拟数据）")
        print(f"  - 搜索结果数量: {len(mock_results)}")
        print(f"  - 第一个结果: {mock_results[0]['title']}")
        print(f"  - 结果格式验证: ✓")

    def test_search_tool_logging(self) -> None:
        """测试搜索工具日志记录"""
        print("\n=== 测试搜索工具日志记录 ===")
        
        # 由于LoggedDuckDuckGoSearch等是通过装饰器创建的，
        # 我们主要测试它们的基本属性
        try:
            # 测试工具类是否正确创建
            assert LoggedDuckDuckGoSearch is not None
            assert LoggedBraveSearch is not None
            assert LoggedArxivSearch is not None
            
            print(f"✓ 所有日志搜索工具类创建成功")
            print(f"  - LoggedDuckDuckGoSearch: {LoggedDuckDuckGoSearch}")
            print(f"  - LoggedBraveSearch: {LoggedBraveSearch}")
            print(f"  - LoggedArxivSearch: {LoggedArxivSearch}")
            
        except Exception as e:
            pytest.fail(f"搜索工具日志测试失败: {e}")

    def test_search_engine_constants(self) -> None:
        """测试搜索引擎常量"""
        print("\n=== 测试搜索引擎常量 ===")
        
        # 验证搜索引擎枚举值
        assert SearchEngine.DUCKDUCKGO.value == "duckduckgo"
        assert SearchEngine.BRAVE_SEARCH.value == "brave_search"
        assert SearchEngine.ARXIV.value == "arxiv"
        
        print(f"✓ 搜索引擎常量验证成功")
        print(f"  - DuckDuckGo: {SearchEngine.DUCKDUCKGO.value}")
        print(f"  - Brave Search: {SearchEngine.BRAVE_SEARCH.value}")
        print(f"  - Arxiv: {SearchEngine.ARXIV.value}")

    def test_max_search_results_parameter(self) -> None:
        """测试最大搜索结果数参数"""
        print("\n=== 测试最大搜索结果数参数 ===")
        
        # 测试不同的最大结果数
        test_cases = [1, 3, 5, 10]
        
        with patch('deep_diagnose.tools.search.SELECTED_SEARCH_ENGINE', SearchEngine.DUCKDUCKGO.value):
            for max_results in test_cases:
                search_tool = get_web_search_tool(max_search_results=max_results)
                assert search_tool is not None
                print(f"  ✓ 最大结果数 {max_results} 测试成功")
        
        print(f"✓ 最大搜索结果数参数测试完成")

    def test_search_results_format(self) -> None:
        """测试搜索结果格式"""
        print("\n=== 测试搜索结果格式 ===")
        
        # 模拟结果
        mock_results = [
            {
                "title": "测试标题",
                "link": "https://test.com",
                "snippet": "测试摘要"
            }
        ]
        
        # 验证结果格式
        assert isinstance(mock_results, list)
        assert len(mock_results) > 0
        
        first_result = mock_results[0]
        assert "title" in first_result
        assert "link" in first_result
        assert "snippet" in first_result
        
        print(f"✓ 搜索结果格式验证成功")
        print(f"  - 结果是列表: {isinstance(mock_results, list)}")
        print(f"  - 包含必要字段: title, link, snippet")


if __name__ == "__main__":
    """直接运行测试"""
    print("🧪 搜索工具测试开始\n")
    
    # 创建测试实例
    test_instance = TestSearchTools()
    
    # 1. 工具创建测试
    print("1. DuckDuckGo工具创建测试")
    test_instance.test_duckduckgo_search_tool_creation()
    
    print("\n2. Brave搜索工具创建测试")
    test_instance.test_brave_search_tool_creation()
    
    print("\n3. Arxiv搜索工具创建测试")
    test_instance.test_arxiv_search_tool_creation()
    
    # 2. 异常测试
    print("\n4. 不支持搜索引擎测试")
    test_instance.test_unsupported_search_engine()
    
    # 3. 功能测试
    print("\n5. 搜索功能测试")
    test_instance.test_duckduckgo_search_functionality()
    
    # 4. 日志测试
    print("\n6. 搜索工具日志测试")
    test_instance.test_search_tool_logging()
    
    # 5. 常量测试
    print("\n7. 搜索引擎常量测试")
    test_instance.test_search_engine_constants()
    
    # 6. 参数测试
    print("\n8. 最大结果数参数测试")
    test_instance.test_max_search_results_parameter()
    
    # 7. 格式测试
    print("\n9. 搜索结果格式测试")
    test_instance.test_search_results_format()
    
    print(f"\n🎉 搜索工具测试完成！")
    print(f"\n=== 搜索工具特点 ===")
    print(f"✅ 支持多种搜索引擎（DuckDuckGo, Brave, Arxiv）")
    print(f"✅ 统一的工具接口")
    print(f"✅ 日志记录功能")
    print(f"✅ 灵活的结果数量控制")
    print(f"✅ 标准化的搜索结果格式")
    
    # 演示实际搜索（如果需要）
    print(f"\n=== 实际搜索演示 ===\n注意：由于网络限制，跳过实际搜索演示")
    print(f"如需实际测试搜索功能，请在具备网络连接的环境中运行")
    print(f"示例用法：")
    print(f"search_tool = LoggedDuckDuckGoSearch(name='web_search', max_results=3, output_format='list')")
    print(f"results = search_tool.invoke('cute panda')")