"""
Unit tests for next_query_prediction_tool with mocked dependencies
"""

import asyncio
import json
from unittest.mock import Async<PERSON><PERSON>, MagicMock, patch

import pytest

from deep_diagnose.tools.next_query_prediction import next_query_prediction_tool


@pytest.mark.asyncio
async def test_heuristic_strategy_basic():
    messages = [{"role": "user", "content": "实例最近是否发生迁移？"}]
    features = {"scene": "down", "machine_id_type": "instance_id", "machine_id": "i-xxx"}

    result = await next_query_prediction_tool.ainvoke(
        {
            "messages": messages,
            "features": features,
            "top_k": 5,
            "strategy": "heuristic",
            "ranker": "bm25",
        }
    )
    data = json.loads(result)
    preds = data.get("predictions", [])
    assert isinstance(preds, list)
    assert len(preds) > 0
    assert any("实例" in p.get("question", "") for p in preds)


@pytest.mark.asyncio
async def test_llm_agent_strategy_with_mocked_bailian():
    messages = [{"role": "user", "content": "如何排查 CPU 飙高？"}]
    features = {"scene": "down", "machine_id_type": "instance_id", "machine_id": "i-yyy"}

    class FakeLLM:
        async def ainvoke(self, _prompt):
            class R:
                content = "- 查看 CPU 使用率\n- 查看进程占用\n- 排查异常进程"
            return R()

    with patch("deep_diagnose.tools.next_query_prediction.get_recommendation_generator_llm") as mock_get_llm:
        mock_get_llm.return_value = FakeLLM()

        result = await next_query_prediction_tool.ainvoke(
            {
                "messages": messages,
                "features": features,
                "top_k": 5,
                "strategy": "llm",
                "llm_mode": "agent",
                "ranker": "bm25",
            }
        )
        data = json.loads(result)
        preds = data.get("predictions", [])
        print(f"Predictions: {preds}")  # Debug print to see what we get
        assert any("CPU" in p.get("question", "") for p in preds)


@pytest.mark.asyncio
async def test_llm_generic_strategy_with_mocked_llm():
    messages = [{"role": "user", "content": "网络延迟较高怎么办？"}]
    features = {"machine_id_type": "instance_id"}

    class FakeLLM:
        async def ainvoke(self, _msgs):
            class R:
                content = "- 检查网络带宽\n- 排查安全组规则\n- 查看丢包率"

            return R()

    with (
        patch("deep_diagnose.llms.llm.get_llm_by_type") as mock_get_llm,
        patch("langchain_core.messages.HumanMessage") as mock_msg,
    ):
        mock_get_llm.return_value = FakeLLM()
        mock_msg.return_value = object()

        result = await next_query_prediction_tool.ainvoke(
            {
                "messages": messages,
                "features": features,
                "top_k": 5,
                "strategy": "llm",
                "llm_mode": "generic",
                "ranker": "bm25",
            }
        )
        data = json.loads(result)
        preds = data.get("predictions", [])
        assert any("网络" in p.get("question", "") for p in preds)


@pytest.mark.asyncio
async def test_embedding_ranking_hybrid_with_mocked_embeddings():
    # Make heuristic produce some candidates, and embedding ranks combined with BM25
    messages = [{"role": "user", "content": "请检查磁盘 IO 与网络"}]
    features = {"machine_id_type": "instance_id"}

    class FakeService:
        async def get_embeddings_batch(self, texts):
            # Return equal length random-like vectors; keep it deterministic
            return [[1.0, 0.0], [0.9, 0.1], [0.0, 1.0]]

        @staticmethod
        def calculate_similarity(a, b):
            # Cosine-like simple dot product
            return sum(x * y for x, y in zip(a, b))

    with (
        patch("deep_diagnose.core.reasoning.planning.strategy.embedding_service.get_embedding_service") as mock_get_service,
        patch("deep_diagnose.core.reasoning.planning.strategy.embedding_service.DashScopeEmbeddingService") as mock_ds,
    ):
        mock_get_service.return_value = FakeService()
        mock_ds.calculate_similarity = staticmethod(FakeService.calculate_similarity)

        result = await next_query_prediction_tool.ainvoke(
            {
                "messages": messages,
                "features": features,
                "top_k": 3,
                "strategy": "heuristic",
                "ranker": "hybrid",
                "hybrid_weight": 0.7,
            }
        )
        data = json.loads(result)
        preds = data.get("predictions", [])
        # Ensure predictions exist and have confidence
        assert len(preds) > 0
        assert all("confidence" in p for p in preds)
