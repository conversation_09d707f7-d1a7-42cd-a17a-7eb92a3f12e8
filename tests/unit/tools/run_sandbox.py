
"""
E2B Sandbox Runner - Simplified version using project config system.
"""

import sys
import os
import logging
from pathlib import Path
from contextlib import contextmanager

# Setup path and credentials before E2B imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', '..', 'src'))
# 注释掉硬编码的环境变量设置，改为通过命令行控制
os.environ['ENABLE_BEARER_TOKEN'] = 'true'  # 使用字符串而不是布尔值
import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def patch_rpc_authentication():
    """
    修补RPC认证以支持Bearer token + 用户信息的混合认证
    这是一个安全的解决方案，既保持Bearer token又解决RPC兼容性
    """
    try:
        import e2b.envd.rpc as rpc_module
        import base64
        
        # 保存原始认证函数
        original_auth_header = rpc_module.authentication_header
        
        def intelligent_auth_header(user=None):
            """
            智能认证策略：
            当trigger使用Bearer token时，RPC调用也必须使用Bearer token
            但需要添加用户上下文信息来解决"no user specified"问题
            """
            from e2b.connection_config import default_username
            
            bearer_token = os.getenv('E2B_FC_BEARER_TOKEN')
            username = user if user is not None else default_username
            
            if bearer_token:
                # 使用Bearer token但添加用户信息到请求体或其他方式
                # 这是关键：我们需要告诉服务器用户是谁
                return {
                    "Authorization": f"Bearer {bearer_token}",
                    "X-Username": username,
                    "X-User": username,
                    "User": username,
                }
            else:
                # 回退到Basic认证
                value = f"{username}:"
                encoded = base64.b64encode(value.encode("utf-8")).decode("utf-8")
                return {"Authorization": f"Basic {encoded}"}
        
        # 应用补丁
        rpc_module.authentication_header = intelligent_auth_header
        logger.info("🔐 已应用智能认证补丁：Bearer token + 多重用户头部")
        return True
        
    except Exception as e:
        logger.warning(f"⚠️  认证补丁应用失败，将使用默认认证: {e}")
        return False

def setup_credentials():
    """Setup credentials with secure hybrid authentication strategy."""
    try:
        from deep_diagnose.common.config import get_config
        config = get_config()
        
        access_key = config.infrastructure.e2b_sandbox.alibaba_cloud.access_key_id
        secret_key = config.infrastructure.e2b_sandbox.alibaba_cloud.access_key_secret
        bearer_token = getattr(config.infrastructure.e2b_sandbox.alibaba_cloud, 'bearer_token', None)
        
        # Set required environment variables for Alibaba Cloud SDK
        os.environ.update({
            'ALIBABA_CLOUD_ACCESS_KEY_ID': access_key,
            'ALIBABA_CLOUD_ACCESS_KEY_SECRET': secret_key,
        })
        
        # 实用认证策略：
        # 经过测试发现：Bearer token trigger要求所有请求都使用Bearer token，
        # 但RPC服务无法正确处理用户上下文，导致"no user specified"错误
        # 
        # 解决方案：在生产环境中使用Bearer token，在开发/测试中禁用以确保功能正常
        if bearer_token:
            # 可以通过环境变量控制是否启用Bearer token
            use_bearer_token = os.getenv('ENABLE_BEARER_TOKEN', 'false').lower() == 'true'
            
            if use_bearer_token:
                os.environ['E2B_FC_BEARER_TOKEN'] = bearer_token
                logger.info("🔐 Bearer token已启用（生产模式）")
                logger.warning("⚠️  注意：RPC操作可能失败，需要后端支持改进")
            else:
                logger.info("🔧 Bearer token已禁用以确保RPC兼容性（开发模式）")
                logger.info("💡 提示：设置环境变量 ENABLE_BEARER_TOKEN=true 启用Bearer token")
        else:
            logger.info("ℹ️  未配置Bearer token，将使用Basic认证")
            
        return True
    except Exception as e:
        logger.error(f"❌ 凭据配置失败: {e}")
        return False

setup_credentials()

from e2b_code_interpreter import Sandbox
from e2b import FilesystemEventType
from deep_diagnose.common.config import get_config


class SandboxRunner:
    """E2B Sandbox operations using project config system."""
    
    def __init__(self):
        self.config = get_config().infrastructure.e2b_sandbox
        self.sandbox = None
        self.script_dir = Path(__file__).parent
        # Get bearer token for FC authentication
        self.bearer_token = getattr(self.config.alibaba_cloud, 'bearer_token', None)
    
    @contextmanager
    def create_sandbox(self):
        """Context manager for safe sandbox lifecycle management."""
        try:
            logger.info("Creating sandbox...")
            # 实用认证策略：
            # - 开发模式：使用Basic认证确保所有功能正常工作
            # - 生产模式：可选择启用Bearer token（需要后端支持改进）
            if self.bearer_token:
                use_bearer = os.getenv('ENABLE_BEARER_TOKEN', 'false').lower() == 'true'
                if use_bearer:
                    logger.info("🔐 生产模式：Bearer token认证（RPC可能受限）")
                else:
                    logger.info("🔧 开发模式：Basic认证（完整功能支持）")
            
            self.sandbox = Sandbox(
                endpoint=self.config.alibaba_cloud.endpoint,
                image=self.config.container.image,
                cpu=self.config.resources.cpu,
                memory=self.config.resources.memory,
                log_store=self.config.logging.log_store,
                log_project=self.config.logging.log_project,
                security_group_id=self.config.network.security_group_id,
                v_switch_id=self.config.network.v_switch_id,
                vpc_id=self.config.network.vpc_id
            )
            logger.info(f"Sandbox created: {self.sandbox.sandbox_id}")
            yield self.sandbox
        except Exception as e:
            logger.error(f"Failed to create sandbox: {e}")
            raise
        finally:
            self._cleanup_sandbox()
    
    def _cleanup_sandbox(self):
        """Clean up sandbox resources."""
        if self.sandbox:
            try:
                #self.sandbox.kill(self.sandbox.sandbox_id)
                logger.info("Sandbox cleaned up")
            except Exception as e:
                logger.warning(f"Cleanup failed: {e}")
            finally:
                self.sandbox = None
    
    def run_code(self, code: str):
        """Execute Python code in sandbox."""
        return self.sandbox.run_code(code)
    
    def run_command(self, command: str, user: str = "user"):
        """Execute shell command in sandbox."""
        return self.sandbox.commands.run(command, user=user)
    
    def upload_file(self, local_file_path: str, remote_path: str, user: str = "user"):
        """Upload local file to sandbox."""
        with open(local_file_path, "rb") as f:
            self.sandbox.files.write(remote_path, f, user)
    
    def download_file(self, remote_path: str, local_file_path: str, user: str = "user"):
        """Download file from sandbox to local."""
        content = self.sandbox.files.read(remote_path, user=user)
        with open(local_file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        return content
    
    def read_file(self, remote_path: str, user: str = "user"):
        """Read file content from sandbox."""
        return self.sandbox.files.read(remote_path, user=user)
    
    def write_file(self, remote_path: str, content: str, user: str = "user"):
        """Write content to file in sandbox."""
        self.sandbox.files.write(remote_path, content, user)
    
    def watch_directory(self, directory: str = "/", user: str = "user"):
        """Watch directory for file system changes."""
        handle = self.sandbox.files.watch_dir(directory, user=user)
        test_file = f"{directory.rstrip('/')}/test-watch-file"
        self.sandbox.files.write(test_file, "test content", user)
        events = handle.get_new_events()
        for event in events:
            if event.type == FilesystemEventType.WRITE:
                logger.info(f"File write detected: {event.name}")
        return events
    
    def demo(self):
        """Run sandbox demo."""
        with self.create_sandbox():
            print("=== Code Execution ===")
            print(self.run_code("print('Hello from E2B!')"))
            
            print("\n=== Command Execution ===")
            try:
                logger.info("Starting command execution...")
                result = self.run_command("echo 'Hello from command!'")
                logger.info(f"Command completed successfully: {result}")
                print(result)
            except Exception as e:
                import traceback
                traceback.print_exc()
                logger.error(f"Command execution failed: {e}")
                print(f"Command execution failed: {e}")
            
            print("\n=== File Operations ===")
            demo_file = self.script_dir / "code-demo.py"
            if demo_file.exists():
                self.upload_file(str(demo_file), "/test.py", "user")
                content = self.read_file("/test.py", "user")
                print(self.run_code(content))
                self.download_file("/test.py", str(self.script_dir / "downloaded.py"), "user")
            else:
                self.write_file("/test.py", "print('The sum of 5 and 7 is: {}'.format(5 + 7))", "user")
                print(self.run_code(self.read_file("/test.py", "user")))
            
            print("\n=== Directory Watch ===")
            try:
                logger.info("Starting directory watch...")
                events = self.watch_directory("/")
                logger.info(f"Directory watch completed successfully: {len(events)} events")
                print(f"Watch events: {len(events)}")
            except Exception as e:
                import traceback
                traceback.print_exc()
                logger.error(f"Directory watch failed: {e}")
                print(f"Directory watch failed: {e}")


def main():
    """Main entry point."""
    try:
        runner = SandboxRunner()
        runner.demo()
    except KeyboardInterrupt:
        logger.info("Interrupted")
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
