"""
Python性能数据分析运行脚本的集成测试
保持原有运行逻辑不变，转换为unit test格式
"""

import sys
import os
import asyncio
import json
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../../src'))

# 主应用模块导入
from deep_diagnose.tools.utils.vm_performance import list_vm_performance_metrics
from deep_diagnose.tools.utils.file_resource_manager import save_and_register_data


class TestRunPythonIntegration:
    """Python性能数据分析运行脚本集成测试"""
    
    async def run_performance_analysis_workflow(self):
        """执行完整的性能数据分析工作流"""
        print("🚀 开始执行性能数据分析...")

        # 1. 定义采集任务 (这个列表由外部传入，此处为示例)
        metrics = ['VmPpsBps/tx_pps', 'VmPpsBps/rx_pps', 'VmNetworkRetryMetric/tx_retry', 'VmNetworkRetryMetric/rx_retry', 'VmSessionDetail/session_count', 'VMLLCContenstionMetric/llc_hit_ratio', 'VMLLCMetric/llc', 'VmArpPingMetric/arp_drop_ratio', 'VmArpPingMetric/arp_timeout_ratio', 'VmStealMetric/vmsteal', 'VmVportDropMetric/drop_ratio', 'VmStealMetric/vcpucpu', 'VMCPUFreq/freq', 'VmStorageIOLatency/read_lat_ms', 'VmStorageIOLatency/write_lat_ms', 'VmMemBWMetric/memory_bw', 'VmStorageIOLatency/wr']
        metrics = None
        collect_data_tasks = [
            {
                "instance_id": "i-0jlfh2k5a6s33s3e1h7j",
                "start_time": "2025-08-30 21:00:00",
                "end_time": "2025-08-31 01:00:00",
                "metrics": metrics
            }
        ]

        request_id = "req_a808036c-c3d8-463f-8a45-4aa69ff5cfc5"
        if not request_id or not request_id.strip():
            request_id = "default_request"

        print(f"📋 使用请求ID: {request_id}")

        # 2. 循环执行每个独立的采集任务
        for task in collect_data_tasks:
            instance_id = task["instance_id"]
            start_time = task["start_time"]
            end_time = task["end_time"]
            task_metrics = task["metrics"]

            try:
                print(f"📊 正在执行任务: 实例 {instance_id} 从 {start_time} 到 {end_time}")

                # 2a. 获取性能数据 (函数返回值为 dict)
                performance_data = await list_vm_performance_metrics(
                    instance_id=instance_id,
                    metrics=task_metrics,
                    start_time=start_time,
                    end_time=end_time
                )

                # 2b. 检查返回是否为空
                if not performance_data:
                    print(f"❌ 未获取到数据: 实例 {instance_id}, 时间 {start_time}-{end_time}")
                    continue

                save_result = save_and_register_data(request_id, instance_id, performance_data)

                if save_result.get("success"):
                    print(f"✅ 任务结果已保存: {save_result.get('json_file')}")
                else:
                    print(f"❌ 任务结果保存失败: {save_result.get('error')}")

            except Exception as e:
                print(f"❌ 执行任务时发生严重错误 (实例 {instance_id}): {e}")

        print("✅ 所有任务执行完成！")
    
    def test_performance_analysis_workflow(self):
        """测试性能数据分析工作流"""
        # 运行完整的工作流
        asyncio.run(self.run_performance_analysis_workflow())


if __name__ == "__main__":
    test = TestRunPythonIntegration()
    test.test_performance_analysis_workflow()
    print("✅ Python性能数据分析集成测试完成")