import unittest
from datetime import datetime, timedelta

# 提取的纯函数，用于测试
from deep_diagnose.tools.utils.vm_performance import _adjust_time_range

class TestVmPerformanceTimeAdjustment(unittest.TestCase):

    def test_time_range_within_limit(self):
        """测试：时间范围在6小时内，应保持不变"""
        start_time = "2024-01-01 00:00:00"
        end_time = "2024-01-01 05:59:00"
        new_start, new_end = _adjust_time_range(start_time, end_time)
        self.assertEqual(new_start, start_time)
        self.assertEqual(new_end, end_time)

    def test_time_range_exceeds_limit(self):
        """测试：时间范围超过6小时，应自动缩减到5小时59分钟"""
        start_time = "2024-01-01 00:00:00"
        end_time = "2024-01-01 07:00:00"  # 7小时范围
        
        start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
        expected_end_dt = start_dt + timedelta(hours=5, minutes=59)
        expected_end_time = expected_end_dt.strftime("%Y-%m-%d %H:%M:%S")
        
        new_start, new_end = _adjust_time_range(start_time, end_time)
        self.assertEqual(new_start, start_time)
        self.assertEqual(new_end, expected_end_time)

    def test_time_range_at_exact_limit(self):
        """测试：时间范围正好是6小时，应保持不变"""
        start_time = "2024-01-01 00:00:00"
        end_time = "2024-01-01 06:00:00"
        new_start, new_end = _adjust_time_range(start_time, end_time)
        self.assertEqual(new_start, start_time)
        self.assertEqual(new_end, end_time)

    def test_invalid_time_format(self):
        """测试：无效的时间格式，应返回原始值并记录警告"""
        start_time = "2024-01-01"
        end_time = "2024-01-01 06:00:00"
        new_start, new_end = _adjust_time_range(start_time, end_time)
        self.assertEqual(new_start, start_time)
        self.assertEqual(new_end, end_time)

    def test_end_time_before_start_time(self):
        """测试：结束时间早于开始时间，应返回原始值"""
        start_time = "2024-01-01 06:00:00"
        end_time = "2024-01-01 00:00:00"
        new_start, new_end = _adjust_time_range(start_time, end_time)
        self.assertEqual(new_start, start_time)
        self.assertEqual(new_end, end_time)

    def test_none_time_values(self):
        """测试：输入为None，应返回None"""
        new_start, new_end = _adjust_time_range(None, None)
        self.assertIsNone(new_start)
        self.assertIsNone(new_end)

if __name__ == '__main__':
    unittest.main()
