"""
客户健康检查运行脚本的集成测试
保持原有运行逻辑不变，转换为unit test格式
"""

import sys
import os
import asyncio
import json

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../../src'))

from deep_diagnose.tools.utils.user_health_check import check_customer_health
from deep_diagnose.tools.utils.file_resource_manager import save_and_register_data


class TestRunCheckCustomerHealthIntegration:
    """客户健康检查运行脚本集成测试"""
    
    async def run_customer_health_check_workflow(self):
        """执行完整的客户健康检查工作流"""
        try:
            # 1) 准备入参
            request_id = "{{ request_id }}".strip() or "default_request"
            ali_uid = "1781574661016173"  # 示例客户UID（请替换为实际值）
            # 可选时间范围：支持 YYYYMMDD / YYYY-MM-DD / YYYY-MM-DD HH:MM:SS
            start_time = "2025-09-01"
            end_time = "2025-09-02"

            print(f"开始客户健康检查: ali_uid={ali_uid}, range={start_time}~{end_time}")

            # 2) 执行健康检查
            result = await check_customer_health(ali_uid, start_time, end_time)
            if not result:
                print("未返回健康检查结果")
                return

            # 若返回为字符串，尽量解析为JSON结构
            if isinstance(result, str):
                try:
                    result = json.loads(result)
                except Exception:
                    pass

            # 3) 保存并登记结果
            # 使用 ali_uid 作为 instance_id 的一部分，便于区分来源
            instance_id = f"customer_{ali_uid}"
            save_res = save_and_register_data(
                request_id=request_id,
                instance_id=instance_id,
                data=result,
                file_title=f"客户健康检查_{ali_uid}",
                file_purpose="客户健康检查"
            )

            print(json.dumps(save_res, ensure_ascii=False))
        except Exception as e:
            print(f"执行失败: {e}")
    
    def test_customer_health_check_workflow(self):
        """测试客户健康检查工作流"""
        # 运行完整的工作流
        asyncio.run(self.run_customer_health_check_workflow())


if __name__ == "__main__":
    test = TestRunCheckCustomerHealthIntegration()
    test.test_customer_health_check_workflow()
    print("✅ 客户健康检查集成测试完成")