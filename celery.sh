#!/bin/bash

# Celery 开发环境启动脚本
# 用于快速启动 Celery Worker 和相关服务

set -e  # 遇到错误立即退出

export CELERY_QUEUE=ecs-deep-diagnose_development
# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SRC_DIR="$PROJECT_ROOT/src"
CONFIG_DIR="$SRC_DIR/deep_diagnose/common/config/files"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 读取配置文件 - 使用 Python 配置系统获取解密后的配置
read_config() {
    local env=${1:-development}
    
    log_debug "使用环境: $env"
    
    # 使用 Python 配置系统获取解密后的配置
    cd "$SRC_DIR"
    
    # 设置环境变量并运行 Python 脚本
    APP_ENV="$env" python3 -c "
import sys
import os

# 确保环境变量已设置
if 'APP_ENV' not in os.environ:
    os.environ['APP_ENV'] = '$env'

try:
    # 导入配置模块
    from deep_diagnose.common.config import get_config
    
    # 清除配置缓存以确保重新加载
    import deep_diagnose.common.config as config_module
    config_module._config_cache = None
    
    # 获取配置
    config = get_config()
    
    # 输出 Redis 配置
    redis_config = config.get('infrastructure', {}).get('redis', {})
    print(redis_config)
    broker_url = redis_config.get('broker_url', 'redis://localhost:6379/2')
    result_backend = redis_config.get('result_backend_url', 'redis://localhost:6379/3')
    
    print(f'REDIS_BROKER_URL={broker_url}')
    print(f'REDIS_RESULT_BACKEND={result_backend}')
    
    # 输出应用配置
    app_config = config.get('app', {})
    app_port = app_config.get('port', 8000)
    print(f'APP_PORT={app_port}')
    
    # 输出其他可能需要的配置
    print(f'APP_NAME={app_config.get(\"name\", \"ecs-deep-diagnose\")}')
    print(f'APP_SECRET={app_config.get(\"secret\", \"ecs_deep_diagnose\")}')
    
except Exception as e:
    print(f'ERROR: 获取配置失败: {e}', file=sys.stderr)
    sys.exit(1)
" 2>/dev/null
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查 Python
    if ! command -v python &> /dev/null; then
        log_error "Python 未安装或不在 PATH 中"
        exit 1
    fi
    
    # 检查 Redis
    if ! command -v redis-cli &> /dev/null; then
        log_warn "Redis CLI 未找到，请确保 Redis 服务正在运行"
    else
        # 从配置文件获取 Redis 信息
        local redis_host="localhost"
        local redis_port="6379"
        
        if [ -n "$REDIS_BROKER_URL" ]; then
            # 从 broker URL 解析主机和端口，支持带密码的格式
            # 格式: redis://:password@host:port/db 或 redis://host:port/db
            redis_host=$(echo "$REDIS_BROKER_URL" | sed -n 's|redis://\(:[^@]*@\)\?\([^:]*\):\([0-9]*\)/.*|\2|p')
            redis_port=$(echo "$REDIS_BROKER_URL" | sed -n 's|redis://\(:[^@]*@\)\?\([^:]*\):\([0-9]*\)/.*|\3|p')
            redis_password=$(echo "$REDIS_BROKER_URL" | sed -n 's|redis://:\([^@]*\)@.*|\1|p')
            
            # 如果解析失败，使用默认值
            [ -z "$redis_host" ] && redis_host="localhost"
            [ -z "$redis_port" ] && redis_port="6379"
        fi
        
        # 测试 Redis 连接
        local redis_cmd="redis-cli -h $redis_host -p $redis_port"
        if [ -n "$redis_password" ]; then
            redis_cmd="$redis_cmd -a $redis_password"
        fi
        
        if ! $redis_cmd ping &> /dev/null; then
            log_warn "无法连接到 Redis 服务 ($redis_host:$redis_port)"
            log_warn "这可能是正常的，如果使用远程 Redis 服务"
            log_warn "将跳过 Redis 连接检查，继续启动 Celery"
        else
            log_info "Redis 连接正常 ($redis_host:$redis_port)"
        fi
    fi
    
    # 检查项目目录
    if [ ! -d "$SRC_DIR" ]; then
        log_error "源码目录不存在: $SRC_DIR"
        exit 1
    fi
    
    log_info "依赖检查完成"
}

# 强制停止所有 Celery 进程
kill_all_celery_processes() {
    log_info "检查并停止所有现有的 Celery 进程..."
    
    # 查找所有 celery 相关进程
    local celery_pids=$(pgrep -f "celery" 2>/dev/null || true)
    local python_celery_pids=$(pgrep -f "python.*celery" 2>/dev/null || true)
    
    # 合并所有 PID 并去重
    local all_pids=$(echo "$celery_pids $python_celery_pids" | tr ' ' '\n' | sort -u | grep -v '^$' || true)
    
    if [ -n "$all_pids" ]; then
        log_warn "发现以下 Celery 进程，正在停止:"
        for pid in $all_pids; do
            if kill -0 "$pid" 2>/dev/null; then
                local process_info=$(ps -p "$pid" -o pid,ppid,cmd --no-headers 2>/dev/null || echo "$pid - 进程信息获取失败")
                log_info "  PID $pid: $process_info"
            fi
        done
        
        # 首先尝试优雅停止 (SIGTERM)
        log_info "尝试优雅停止进程..."
        for pid in $all_pids; do
            if kill -0 "$pid" 2>/dev/null; then
                kill -TERM "$pid" 2>/dev/null || true
            fi
        done
        
        # 等待进程停止
        sleep 2
        
        # 检查是否还有进程存在，如果有则强制停止 (SIGKILL)
        local remaining_pids=$(echo "$all_pids" | xargs -I {} sh -c 'kill -0 {} 2>/dev/null && echo {}' || true)
        if [ -n "$remaining_pids" ]; then
            log_warn "部分进程未能优雅停止，强制终止..."
            for pid in $remaining_pids; do
                if kill -0 "$pid" 2>/dev/null; then
                    log_info "强制停止 PID: $pid"
                    kill -KILL "$pid" 2>/dev/null || true
                fi
            done
            sleep 1
        fi
        
        log_info "所有 Celery 进程已停止"
    else
        log_info "未发现运行中的 Celery 进程"
    fi
    
    # 清理可能存在的 PID 文件
    cd "$SRC_DIR" 2>/dev/null || true
    if [ -f "celery_worker.pid" ]; then
        log_debug "清理 Worker PID 文件"
        rm -f celery_worker.pid
    fi
    if [ -f "celery_beat.pid" ]; then
        log_debug "清理 Beat PID 文件"
        rm -f celery_beat.pid
    fi
    if [ -f "celerybeat.pid" ]; then
        log_debug "清理 Beat PID 文件 (celerybeat.pid)"
        rm -f celerybeat.pid
    fi
    if [ -f "celerybeat-schedule" ]; then
        log_debug "清理 Beat 调度文件"
        rm -f celerybeat-schedule
    fi
}

# 设置环境变量
setup_environment() {
    log_info "设置环境变量..."
    
    # 设置开发环境
    export APP_ENV=${APP_ENV:-development}
    export FORCE_CELERY=true  # 强制使用 Celery
    
    # 设置 Python 路径
    export PYTHONPATH="$SRC_DIR:$PYTHONPATH"
    
    # 读取配置文件
    log_info "通过 Python 配置系统读取解密后的配置..."
    local config_output
    if config_output=$(read_config "$APP_ENV"); then
        # 解析配置输出并设置环境变量
        while IFS='=' read -r key value; do
            if [ -n "$key" ] && [ -n "$value" ]; then
                export "$key"="$value"
                log_debug "$key=$value"
            fi
        done <<< "$config_output"
        
        log_info "配置加载成功，密码已自动解密"
    else
        log_warn "无法通过 Python 配置系统读取配置，使用默认配置"
        export REDIS_BROKER_URL="redis://localhost:6379/2"
        export REDIS_RESULT_BACKEND="redis://localhost:6379/3"
        export APP_PORT="8000"
        export APP_NAME="ecs-deep-diagnose"
        export APP_SECRET="ecs_deep_diagnose"
    fi
    
    log_debug "APP_ENV=$APP_ENV"
    log_debug "FORCE_CELERY=$FORCE_CELERY"
    log_debug "PYTHONPATH=$PYTHONPATH"
}

# 启动 Celery Worker
start_worker() {
    log_info "启动 Celery Worker..."
    
    # 停止所有现有的 Celery 进程
    kill_all_celery_processes
    
    cd "$SRC_DIR"
    
    # 获取队列名称，默认使用 celery
    local queue_name=${CELERY_QUEUE:-celery}
    
    # Celery Worker 配置
    WORKER_ARGS=(
        "-A" "deep_diagnose.services.task.celery_base.celery"
        "worker"
        "-Q" "$queue_name"
        "--loglevel=info"
        "--concurrency=2"  # 开发环境使用较少的并发数
        "--pool=prefork"   # 使用 prefork 池而不是 solo，solo 可能导致问题
        "--without-gossip"
        "--without-mingle"
        # "--without-heartbeat"  # 暂时启用 heartbeat
    )
    
    log_info "执行命令: python -m celery ${WORKER_ARGS[*]}"
    log_info "Worker 启动中，按 Ctrl+C 停止..."
    exec python -m celery "${WORKER_ARGS[@]}"
}

# 启动 Flower 监控
start_flower() {
    log_info "启动 Flower 监控界面..."
    
    # 停止所有现有的 Celery 进程
    kill_all_celery_processes
    
    cd "$SRC_DIR"
    
    FLOWER_ARGS=(
        "-A" "deep_diagnose.services.task.celery_base.celery"
        "flower"
        "--address=0.0.0.0"
        "--port=5555"
        "--loglevel=debug"
    )
    
    log_info "执行命令: python -m celery ${FLOWER_ARGS[*]}"
    log_info "Flower 将在 http://localhost:5555 启动"
    exec python -m celery "${FLOWER_ARGS[@]}"
}

# 启动 Celery Beat
start_beat() {
    log_info "启动 Celery Beat 调度器..."
    
    # 停止所有现有的 Celery 进程
    kill_all_celery_processes
    
    cd "$SRC_DIR"
    
    BEAT_ARGS=(
        "-A" "deep_diagnose.services.task.celery_base.celery"
        "beat"
        "--loglevel=info"
    )
    
    log_info "执行命令: python -m celery ${BEAT_ARGS[*]}"
    exec python -m celery "${BEAT_ARGS[@]}"
}

# 检查任务状态
check_tasks() {
    log_info "检查 Celery 任务状态..."
    
    cd "$SRC_DIR"
    
    # 检查 Redis 连接（使用配置的 Redis）
    local redis_host="localhost"
    local redis_port="6379"
    local redis_password=""
    
    if [ -n "$REDIS_BROKER_URL" ]; then
        redis_host=$(echo "$REDIS_BROKER_URL" | sed -n 's|redis://\(:[^@]*@\)\?\([^:]*\):\([0-9]*\)/.*|\2|p')
        redis_port=$(echo "$REDIS_BROKER_URL" | sed -n 's|redis://\(:[^@]*@\)\?\([^:]*\):\([0-9]*\)/.*|\3|p')
        redis_password=$(echo "$REDIS_BROKER_URL" | sed -n 's|redis://:\([^@]*\)@.*|\1|p')
        
        [ -z "$redis_host" ] && redis_host="localhost"
        [ -z "$redis_port" ] && redis_port="6379"
    fi
    
    local redis_cmd="redis-cli -h $redis_host -p $redis_port"
    if [ -n "$redis_password" ]; then
        redis_cmd="$redis_cmd -a $redis_password"
    fi
    
    if ! $redis_cmd ping &> /dev/null; then
        log_warn "无法连接到 Redis 服务 ($redis_host:$redis_port)"
        log_warn "这可能是正常的，如果使用远程 Redis 服务"
    else
        log_info "Redis 连接正常 ($redis_host:$redis_port)"
    fi
    
    echo
    log_info "注册的任务:"
    if ! python -m celery -A deep_diagnose.services.task.celery_base.celery inspect registered 2>/dev/null; then
        log_warn "无法获取注册的任务，可能没有 Worker 在运行"
    fi
    
    echo
    log_info "活跃的任务:"
    if ! python -m celery -A deep_diagnose.services.task.celery_base.celery inspect active 2>/dev/null; then
        log_warn "无法获取活跃任务，可能没有 Worker 在运行"
    fi
    
    echo
    log_info "预定的任务:"
    if ! python -m celery -A deep_diagnose.services.task.celery_base.celery inspect scheduled 2>/dev/null; then
        log_warn "无法获取预定任务，可能没有 Worker 在运行"
    fi
}

# 启动所有 Celery 服务
start_all() {
    log_info "启动所有 Celery 服务 (Worker, Flower, Beat)..."
    
    # 停止所有现有的 Celery 进程
    kill_all_celery_processes
    
    cd "$SRC_DIR"
    
    # 获取队列名称，默认使用 celery
    local queue_name=${CELERY_QUEUE:-celery}
    
    log_info "启动 Celery Worker (后台)..."
    python -m celery -A deep_diagnose.services.task.celery_base.celery worker -Q "$queue_name" --loglevel=info --concurrency=2 --pool=solo --without-gossip --without-mingle --without-heartbeat --detach --pidfile=celery_worker.pid --logfile=celery_worker.log
    
    log_info "启动 Celery Beat (后台)..."
    python -m celery -A deep_diagnose.services.task.celery_base.celery beat --loglevel=info --detach --pidfile=celery_beat.pid --logfile=celery_beat.log
    
    log_info "启动 Celery Flower (前台)..."
    log_info "Flower 将在 http://localhost:5555 启动"
    log_info "使用 Ctrl+C 停止所有服务"
    
    # 设置信号处理，当收到中断信号时停止所有服务
    trap 'stop_all_services' INT TERM
    
    python -m celery -A deep_diagnose.services.task.celery_base.celery flower --address=0.0.0.0 --port=5555 --loglevel=debug
}

# 停止所有服务
stop_all_services() {
    log_info "正在停止所有 Celery 服务..."
    
    cd "$SRC_DIR"
    
    # 停止 Worker
    if [ -f "celery_worker.pid" ]; then
        local worker_pid=$(cat celery_worker.pid)
        if kill -0 "$worker_pid" 2>/dev/null; then
            log_info "停止 Celery Worker (PID: $worker_pid)"
            kill -TERM "$worker_pid"
        fi
        rm -f celery_worker.pid
    fi
    
    # 停止 Beat
    if [ -f "celery_beat.pid" ]; then
        local beat_pid=$(cat celery_beat.pid)
        if kill -0 "$beat_pid" 2>/dev/null; then
            log_info "停止 Celery Beat (PID: $beat_pid)"
            kill -TERM "$beat_pid"
        fi
        rm -f celery_beat.pid
    fi
    
    # 清理日志文件（可选）
    # rm -f celery_worker.log celery_beat.log
    
    log_info "所有服务已停止"
    exit 0
}

# 清理任务队列
purge_tasks() {
    log_warn "清理所有任务队列..."
    read -p "确定要清理所有任务吗？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cd "$SRC_DIR"
        python -m celery -A deep_diagnose.services.task.celery_base.celery purge -f
        log_info "任务队列已清理"
    else
        log_info "取消清理操作"
    fi
}

# 显示帮助信息
show_help() {
    cat << EOF
Celery 开发环境启动脚本

用法: $0 [命令]

命令:
    all         启动所有服务 (Worker + Flower + Beat)
    worker      启动 Celery Worker
    flower      启动 Flower 监控界面
    beat        启动 Celery Beat 调度器
    status      检查任务状态
    purge       清理任务队列
    stop        停止所有后台服务
    help        显示此帮助信息

示例:
    $0              # 启动所有服务 (默认)
    $0 all          # 启动所有服务
    $0 worker       # 仅启动 Worker
    $0 flower       # 仅启动 Flower
    $0 beat         # 仅启动 Beat
    $0 status       # 检查状态
    $0 purge        # 清理队列
    $0 stop         # 停止所有服务

环境变量:
    APP_ENV=development     # 应用环境
    FORCE_CELERY=true       # 强制使用 Celery
    CELERY_QUEUE=celery     # Celery 队列名称

注意:
    - 请确保 Redis 服务正在运行
    - Worker 使用 solo 池，便于开发调试
    - Flower 监控界面: http://localhost:5555
    - 'all' 命令会在后台启动 Worker 和 Beat，前台启动 Flower
    - 使用 Ctrl+C 或 'stop' 命令停止所有服务
    - 启动任何服务前会自动停止所有现有的 Celery 进程
EOF
}

# 主函数
main() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  Celery 开发环境启动脚本${NC}"
    echo -e "${BLUE}================================${NC}"
    echo
    
    # 解析命令行参数
    COMMAND=${1:-all}
    
    case $COMMAND in
        all)
            check_dependencies
            setup_environment
            start_all
            ;;
        worker)
            check_dependencies
            setup_environment
            start_worker
            ;;
        flower)
            check_dependencies
            setup_environment
            start_flower
            ;;
        beat)
            check_dependencies
            setup_environment
            start_beat
            ;;
        status)
            setup_environment
            check_tasks
            ;;
        purge)
            setup_environment
            purge_tasks
            ;;
        stop)
            stop_all_services
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            log_error "未知命令: $COMMAND"
            echo
            show_help
            exit 1
            ;;
    esac
}

# 捕获 Ctrl+C
trap 'log_info "收到中断信号，正在退出..."; exit 0' INT

# 运行主函数
main "$@"
